import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

/// Service for handling AdMob ads
class AdService {
  /// Singleton instance
  static final AdService _instance = AdService._internal();

  /// Factory constructor
  factory AdService() => _instance;

  /// Internal constructor
  AdService._internal();

  /// Whether ads are initialized
  bool _initialized = false;

  /// Banner ad for home screen
  BannerAd? _homeBannerAd;

  /// Banner ad for pattern detail screen
  BannerAd? _detailBannerAd;

  /// Interstitial ad
  InterstitialAd? _interstitialAd;

  /// Rewarded ad
  RewardedAd? _rewardedAd;

  /// Whether ads are enabled
  bool _adsEnabled = true;

  /// Test ad unit IDs
  static const Map<String, Map<String, String>> _testAdUnitIds = {
    'android': {
      'banner': 'ca-app-pub-3940256099942544/6300978111',
      'interstitial': 'ca-app-pub-3940256099942544/1033173712',
      'rewarded': 'ca-app-pub-3940256099942544/5224354917',
    },
    'ios': {
      'banner': 'ca-app-pub-3940256099942544/2934735716',
      'interstitial': 'ca-app-pub-3940256099942544/4411468910',
      'rewarded': 'ca-app-pub-3940256099942544/1712485313',
    },
  };

  /// Get the appropriate ad unit ID based on platform and ad type
  String _getAdUnitId(String type) {
    // Use test ad unit IDs for development
    if (kDebugMode) {
      final platform = Platform.isAndroid ? 'android' : 'ios';
      return _testAdUnitIds[platform]?[type] ?? '';
    }

    // In production, use real ad unit IDs
    // TODO: Replace with real ad unit IDs
    final platform = Platform.isAndroid ? 'android' : 'ios';
    return _testAdUnitIds[platform]?[type] ?? '';
  }

  /// Initialize the ad service
  Future<void> initialize() async {
    if (_initialized) return;

    // Initialize MobileAds but don't wait for ads to load
    await MobileAds.instance.initialize();
    _initialized = true;

    // Load ads in the background
    _loadAdsInBackground();
  }

  /// Load ads in the background to avoid blocking app startup
  Future<void> _loadAdsInBackground() async {
    // Small delay to ensure app UI is shown first
    await Future.delayed(const Duration(milliseconds: 500));

    // Load ads one by one with small delays between them
    _loadHomeBannerAd();
    await Future.delayed(const Duration(milliseconds: 100));

    _loadDetailBannerAd();
    await Future.delayed(const Duration(milliseconds: 100));

    _loadInterstitialAd();
    await Future.delayed(const Duration(milliseconds: 100));

    _loadRewardedAd();
  }

  /// Load the home banner ad
  void _loadHomeBannerAd() {
    _homeBannerAd = BannerAd(
      adUnitId: _getAdUnitId('banner'),
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          debugPrint('Home Banner ad loaded.');
        },
        onAdFailedToLoad: (ad, error) {
          debugPrint('Home Banner ad failed to load: $error');
          ad.dispose();
          _homeBannerAd = null;
        },
      ),
    );

    _homeBannerAd?.load();
  }

  /// Load the detail banner ad
  void _loadDetailBannerAd() {
    _detailBannerAd = BannerAd(
      adUnitId: _getAdUnitId('banner'),
      size: AdSize.mediumRectangle,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          debugPrint('Detail Banner ad loaded.');
        },
        onAdFailedToLoad: (ad, error) {
          debugPrint('Detail Banner ad failed to load: $error');
          ad.dispose();
          _detailBannerAd = null;
        },
      ),
    );

    _detailBannerAd?.load();
  }

  /// Load the interstitial ad
  void _loadInterstitialAd() {
    InterstitialAd.load(
      adUnitId: _getAdUnitId('interstitial'),
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          debugPrint('Interstitial ad loaded.');
        },
        onAdFailedToLoad: (error) {
          debugPrint('Interstitial ad failed to load: $error');
          _interstitialAd = null;
        },
      ),
    );
  }

  /// Load the rewarded ad
  void _loadRewardedAd() {
    RewardedAd.load(
      adUnitId: _getAdUnitId('rewarded'),
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          debugPrint('Rewarded ad loaded.');
        },
        onAdFailedToLoad: (error) {
          debugPrint('Rewarded ad failed to load: $error');
          _rewardedAd = null;
        },
      ),
    );
  }

  /// Get the home banner ad
  BannerAd? getHomeBannerAd() {
    if (!_adsEnabled) return null;
    return _homeBannerAd;
  }

  /// Get the detail banner ad
  BannerAd? getDetailBannerAd() {
    if (!_adsEnabled) return null;
    return _detailBannerAd;
  }

  /// Show the interstitial ad
  Future<bool> showInterstitialAd() async {
    if (!_adsEnabled) return false;
    if (_interstitialAd == null) {
      _loadInterstitialAd();
      return false;
    }

    bool adShown = true;
    try {
      await _interstitialAd!.show();
    } catch (e) {
      debugPrint('Error showing interstitial ad: $e');
      adShown = false;
    }
    _interstitialAd = null;
    _loadInterstitialAd();
    return adShown;
  }

  /// Show the rewarded ad
  Future<bool> showRewardedAd(Function(RewardItem) onRewarded) async {
    if (!_adsEnabled) return false;
    if (_rewardedAd == null) {
      _loadRewardedAd();
      return false;
    }

    bool adShown = true;
    try {
      await _rewardedAd!.show(onUserEarnedReward: (_, reward) {
        onRewarded(reward);
      });
    } catch (e) {
      debugPrint('Error showing rewarded ad: $e');
      adShown = false;
    }
    _rewardedAd = null;
    _loadRewardedAd();
    return adShown;
  }

  /// Disable ads (e.g., for premium users)
  void disableAds() {
    _adsEnabled = false;
    _disposeAds();
  }

  /// Enable ads
  void enableAds() {
    _adsEnabled = true;
    _loadHomeBannerAd();
    _loadDetailBannerAd();
    _loadInterstitialAd();
    _loadRewardedAd();
  }

  /// Dispose all ads
  void _disposeAds() {
    _homeBannerAd?.dispose();
    _homeBannerAd = null;

    _detailBannerAd?.dispose();
    _detailBannerAd = null;

    _interstitialAd?.dispose();
    _interstitialAd = null;

    _rewardedAd?.dispose();
    _rewardedAd = null;
  }

  /// Dispose the ad service
  void dispose() {
    _disposeAds();
  }
}
