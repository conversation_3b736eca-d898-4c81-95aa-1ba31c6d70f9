import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:learn_chart_patterns/services/storage_service.dart';

/// Service for handling in-app purchases
class PurchaseService {
  /// Singleton instance
  static final PurchaseService _instance = PurchaseService._internal();

  /// Factory constructor
  factory PurchaseService() => _instance;

  /// Internal constructor
  PurchaseService._internal();

  /// The in-app purchase plugin
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  /// The storage service
  late StorageService _storageService;

  /// Whether the service is initialized
  bool _initialized = false;

  /// Available products
  List<ProductDetails> _products = [];

  /// Subscription for purchase updates
  StreamSubscription<List<PurchaseDetails>>? _subscription;

  /// Whether the user has premium access
  bool _hasPremium = false;

  /// Whether the user has advanced patterns
  bool _hasAdvancedPatterns = false;

  /// Whether the user has strategy guides
  bool _hasStrategyGuides = false;

  /// Product IDs
  static const List<String> _productIds = [
    'premium_access',
    'advanced_patterns',
    'strategy_guides',
  ];

  /// Initialize the purchase service
  Future<void> initialize(StorageService storageService) async {
    if (_initialized) return;

    _storageService = storageService;

    // Load purchase status from storage - this is fast and local
    _hasPremium = await _storageService.getBool('has_premium') ?? false;
    _hasAdvancedPatterns = await _storageService.getBool('has_advanced_patterns') ?? false;
    _hasStrategyGuides = await _storageService.getBool('has_strategy_guides') ?? false;

    // Mark as initialized early so the app can start
    _initialized = true;

    // Load products in the background
    _loadProductsInBackground();
  }

  /// Load products in the background to avoid blocking app startup
  Future<void> _loadProductsInBackground() async {
    // Small delay to ensure app UI is shown first
    await Future.delayed(const Duration(milliseconds: 700));

    // Check if in-app purchases are available
    final bool available = await _inAppPurchase.isAvailable();
    if (!available) {
      debugPrint('In-app purchases not available');
      return;
    }

    // Load products
    try {
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds.toSet());
      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('Products not found: ${response.notFoundIDs}');
      }

      _products = response.productDetails;

      // Listen for purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _handlePurchaseUpdate,
        onDone: _subscription?.cancel,
        onError: (error) {
          debugPrint('Purchase error: $error');
        },
      );
    } catch (e) {
      debugPrint('Error loading products: $e');
    }
  }

  /// Handle purchase updates
  void _handlePurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        // Show pending UI
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        // Show error UI
        debugPrint('Purchase error: ${purchaseDetails.error}');
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
                 purchaseDetails.status == PurchaseStatus.restored) {
        // Grant entitlement
        _handleSuccessfulPurchase(purchaseDetails);
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        // Do nothing
      }

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// Handle a successful purchase
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    switch (purchaseDetails.productID) {
      case 'premium_access':
        _hasPremium = true;
        await _storageService.setBool('has_premium', true);
        break;
      case 'advanced_patterns':
        _hasAdvancedPatterns = true;
        await _storageService.setBool('has_advanced_patterns', true);
        break;
      case 'strategy_guides':
        _hasStrategyGuides = true;
        await _storageService.setBool('has_strategy_guides', true);
        break;
    }
  }

  /// Buy a product
  Future<void> buyProduct(String productId) async {
    if (!_initialized) {
      debugPrint('Purchase service not initialized');
      return;
    }

    ProductDetails? product;
    try {
      product = _products.firstWhere(
        (p) => p.id == productId,
      );
    } catch (e) {
      // Product not found
      product = null;
    }

    if (product == null) {
      debugPrint('Product not found: $productId');
      return;
    }

    final PurchaseParam purchaseParam = PurchaseParam(
      productDetails: product,
    );

    await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
  }

  /// Restore purchases
  Future<void> restorePurchases() async {
    if (!_initialized) {
      debugPrint('Purchase service not initialized');
      return;
    }

    await _inAppPurchase.restorePurchases();
  }

  /// Get available products
  List<ProductDetails> getProducts() {
    return _products;
  }

  /// Check if the user has premium access
  bool get hasPremium => _hasPremium;

  /// Check if the user has advanced patterns
  bool get hasAdvancedPatterns => _hasAdvancedPatterns;

  /// Check if the user has strategy guides
  bool get hasStrategyGuides => _hasStrategyGuides;

  /// Dispose the purchase service
  void dispose() {
    _subscription?.cancel();
  }
}
