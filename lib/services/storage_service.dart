import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for storing and retrieving data from shared preferences
class StorageService {
  /// Shared preferences instance
  late SharedPreferences _prefs;

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// Initializes the storage service
  Future<void> init() async {
    if (_isInitialized) return;

    _prefs = await SharedPreferences.getInstance();
    _isInitialized = true;
  }

  /// Gets a string value from storage
  Future<String?> getString(String key) async {
    await _ensureInitialized();
    return _prefs.getString(key);
  }

  /// Sets a string value in storage
  Future<bool> setString(String key, String value) async {
    await _ensureInitialized();
    return _prefs.setString(key, value);
  }

  /// Gets a boolean value from storage
  Future<bool?> getBool(String key) async {
    await _ensureInitialized();
    return _prefs.getBool(key);
  }

  /// Sets a boolean value in storage
  Future<bool> setBool(String key, bool value) async {
    await _ensureInitialized();
    return _prefs.setBool(key, value);
  }

  /// Gets an integer value from storage
  Future<int?> getInt(String key) async {
    await _ensureInitialized();
    return _prefs.getInt(key);
  }

  /// Sets an integer value in storage
  Future<bool> setInt(String key, int value) async {
    await _ensureInitialized();
    return _prefs.setInt(key, value);
  }

  /// Gets a double value from storage
  Future<double?> getDouble(String key) async {
    await _ensureInitialized();
    return _prefs.getDouble(key);
  }

  /// Sets a double value in storage
  Future<bool> setDouble(String key, double value) async {
    await _ensureInitialized();
    return _prefs.setDouble(key, value);
  }

  /// Gets a list of strings from storage
  Future<List<String>?> getStringList(String key) async {
    await _ensureInitialized();
    return _prefs.getStringList(key);
  }

  /// Sets a list of strings in storage
  Future<bool> setStringList(String key, List<String> value) async {
    await _ensureInitialized();
    return _prefs.setStringList(key, value);
  }

  /// Gets an object from storage
  Future<T?> getObject<T>(String key, T Function(Map<String, dynamic> json) fromJson) async {
    await _ensureInitialized();
    final jsonString = _prefs.getString(key);
    if (jsonString == null) return null;

    try {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return fromJson(json);
    } catch (e) {
      debugPrint('Error decoding object from storage: $e');
      return null;
    }
  }

  /// Sets an object in storage
  Future<bool> setObject<T>(String key, T value, Map<String, dynamic> Function(T value) toJson) async {
    await _ensureInitialized();
    try {
      final json = toJson(value);
      final jsonString = jsonEncode(json);
      return _prefs.setString(key, jsonString);
    } catch (e) {
      debugPrint('Error encoding object to storage: $e');
      return false;
    }
  }

  /// Removes a value from storage
  Future<bool> remove(String key) async {
    await _ensureInitialized();
    return _prefs.remove(key);
  }

  /// Clears all values from storage
  Future<bool> clear() async {
    await _ensureInitialized();
    return _prefs.clear();
  }

  /// Gets a map from storage
  Future<Map<String, dynamic>?> getMap(String key) async {
    await _ensureInitialized();
    final jsonString = _prefs.getString(key);
    if (jsonString == null) return null;

    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error decoding map from storage: $e');
      return null;
    }
  }

  /// Sets a map in storage
  Future<bool> setMap(String key, Map<String, dynamic> value) async {
    await _ensureInitialized();
    try {
      final jsonString = jsonEncode(value);
      return _prefs.setString(key, jsonString);
    } catch (e) {
      debugPrint('Error encoding map to storage: $e');
      return false;
    }
  }

  /// Ensures the service is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await init();
    }
  }
}
