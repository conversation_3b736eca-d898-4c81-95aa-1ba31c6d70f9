import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/pattern_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';

/// Service for managing premium content
class PremiumContentService {
  /// Singleton instance
  static final PremiumContentService _instance = PremiumContentService._internal();

  /// Factory constructor
  factory PremiumContentService() => _instance;

  /// Internal constructor
  PremiumContentService._internal();

  /// Purchase service
  final PurchaseService _purchaseService = PurchaseService();

  /// Premium patterns
  final List<String> _premiumPatternIds = [
    'harmonic_patterns',
    'elliott_wave',
    'three_drives',
    'wolfe_waves',
    'gartley',
    'butterfly',
    'bat',
    'crab',
    'shark',
    'cypher',
    'abcd',
    'three_point_extension',
    'five_point_reversal',
    'diamond',
    'broadening_formation',
    'island_reversal',
    'gap_patterns',
    'measured_move',
    'three_line_strike',
    'three_black_crows',
    'three_white_soldiers',
    'morning_star',
    'evening_star',
    'dark_cloud_cover',
    'piercing_pattern',
    'engulfing',
    'harami',
    'doji',
    'hammer',
    'shooting_star',
  ];

  /// Premium strategy guides
  final List<String> _premiumStrategyGuideIds = [
    'harmonic_trading',
    'elliott_wave_analysis',
    'fibonacci_trading',
    'market_profile',
    'volume_profile',
    'order_flow',
    'market_structure',
    'supply_demand',
    'price_action',
    'candlestick_psychology',
    'volatility_trading',
    'momentum_trading',
    'trend_following',
    'mean_reversion',
    'breakout_trading',
    'range_trading',
    'swing_trading',
    'day_trading',
    'position_trading',
    'scalping',
  ];

  /// Checks if a pattern is premium
  bool isPatternPremium(String patternId) {
    return _premiumPatternIds.contains(patternId);
  }

  /// Checks if a strategy guide is premium
  bool isStrategyGuidePremium(String guideId) {
    return _premiumStrategyGuideIds.contains(guideId);
  }

  /// Checks if the user has access to a premium pattern
  bool hasPatternAccess(String patternId) {
    if (!isPatternPremium(patternId)) {
      return true;
    }

    return _purchaseService.hasPremium || _purchaseService.hasAdvancedPatterns;
  }

  /// Checks if the user has access to a premium strategy guide
  bool hasStrategyGuideAccess(String guideId) {
    if (!isStrategyGuidePremium(guideId)) {
      return true;
    }

    return _purchaseService.hasPremium || _purchaseService.hasStrategyGuides;
  }

  /// Gets all premium patterns
  List<ChartPattern> getPremiumPatterns() {
    return PatternData.allPatterns.where((pattern) =>
      isPatternPremium(pattern.id)
    ).toList();
  }

  /// Gets accessible premium patterns
  List<ChartPattern> getAccessiblePremiumPatterns() {
    if (_purchaseService.hasPremium || _purchaseService.hasAdvancedPatterns) {
      return getPremiumPatterns();
    }

    return [];
  }

  /// Gets a sample of premium patterns (for preview)
  List<ChartPattern> getPremiumPatternSample({int count = 3}) {
    final premiumPatterns = getPremiumPatterns();
    if (premiumPatterns.length <= count) {
      return premiumPatterns;
    }

    return premiumPatterns.sublist(0, count);
  }

  /// Shows an upgrade dialog for premium patterns
  Future<bool> showPremiumPatternsUpgradeDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => _buildUpgradeDialog(
        context,
        title: 'Advanced Patterns',
        content: 'Unlock 30+ advanced chart patterns with detailed analysis and trading strategies.',
        features: [
          'Harmonic patterns (Gartley, Butterfly, Bat, etc.)',
          'Elliott Wave patterns',
          'Advanced candlestick patterns',
          'Detailed annotations and explanations',
          'Professional trading strategies',
        ],
        productId: 'advanced_patterns',
      ),
    ) ?? false;
  }

  /// Shows an upgrade dialog for premium strategy guides
  Future<bool> showPremiumStrategyGuidesUpgradeDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => _buildUpgradeDialog(
        context,
        title: 'Strategy Guides',
        content: 'Access 20+ professional trading strategy guides with detailed explanations and examples.',
        features: [
          'Harmonic trading strategies',
          'Elliott Wave analysis',
          'Fibonacci trading techniques',
          'Volume and order flow analysis',
          'Market structure and price action',
        ],
        productId: 'strategy_guides',
      ),
    ) ?? false;
  }

  /// Shows an upgrade dialog for premium access
  Future<bool> showPremiumAccessUpgradeDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => _buildUpgradeDialog(
        context,
        title: 'Premium Access',
        content: 'Unlock all premium features and remove ads for the ultimate learning experience.',
        features: [
          'All advanced patterns (30+)',
          'All strategy guides (20+)',
          'Ad-free experience',
          'Priority updates and new content',
          'Support future development',
        ],
        productId: 'premium_access',
      ),
    ) ?? false;
  }

  /// Builds an upgrade dialog
  Widget _buildUpgradeDialog(
    BuildContext context, {
    required String title,
    required String content,
    required List<String> features,
    required String productId,
  }) {
    final theme = Theme.of(context);

    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Text(
        'Upgrade to $title',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.primary,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(content),
            const SizedBox(height: 16),
            ...features.map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(feature),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Not Now'),
        ),
        ElevatedButton.icon(
          onPressed: () async {
            Navigator.of(context).pop(true);
            await _purchaseService.buyProduct(productId);
          },
          icon: const Icon(Icons.lock_open),
          label: const Text('Upgrade Now'),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
