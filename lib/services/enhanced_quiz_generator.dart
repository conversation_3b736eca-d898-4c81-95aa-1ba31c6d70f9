import 'dart:math';

import 'package:learn_chart_patterns/constants/pattern_data.dart';
import 'package:learn_chart_patterns/generators/pattern_generator_factory.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/enhanced_quiz_system.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';

/// Enhanced quiz generator with adaptive questioning
class EnhancedQuizGenerator {
  /// Random number generator
  final Random _random = Random();
  
  /// Pattern data source
  final List<ChartPattern> _patterns = PatternData.allPatterns;

  /// Generates an adaptive quiz session
  AdaptiveQuizSession generateAdaptiveSession({
    required SkillLevel skillLevel,
    QuizSessionConfig? config,
  }) {
    final sessionConfig = config ?? _getDefaultConfig(skillLevel);
    final questions = _generateAdaptiveQuestions(skillLevel, sessionConfig);
    
    return AdaptiveQuizSession(
      skillLevel: skillLevel,
      questions: questions,
      startTime: DateTime.now(),
      config: sessionConfig,
      metrics: const SessionMetrics(),
    );
  }

  /// Generates questions based on skill level and configuration
  List<EnhancedQuizQuestion> _generateAdaptiveQuestions(
    SkillLevel skillLevel,
    QuizSessionConfig config,
  ) {
    final questions = <EnhancedQuizQuestion>[];
    final usedPatterns = <String>{};
    
    // Determine difficulty range based on skill level
    final difficultyRange = _getDifficultyRange(skillLevel);
    
    for (int i = 0; i < config.maxQuestions; i++) {
      final question = _generateQuestion(
        difficultyRange: difficultyRange,
        categories: config.categories,
        usedPatterns: usedPatterns,
        questionIndex: i,
      );
      
      if (question != null) {
        questions.add(question);
        // Track used patterns to avoid repetition
        if (question.chartData != null) {
          usedPatterns.add(question.id);
        }
      }
    }
    
    return questions;
  }

  /// Generates a single quiz question
  EnhancedQuizQuestion? _generateQuestion({
    required DifficultyRange difficultyRange,
    required List<QuizCategory> categories,
    required Set<String> usedPatterns,
    required int questionIndex,
  }) {
    // Select question type based on difficulty and categories
    final questionType = _selectQuestionType(categories, difficultyRange);
    final category = categories.isNotEmpty 
        ? categories[_random.nextInt(categories.length)]
        : QuizCategory.patternRecognition;
    
    switch (questionType) {
      case QuestionType.chartIdentification:
        return _generateChartIdentificationQuestion(difficultyRange, usedPatterns);
      case QuestionType.multipleChoice:
        return _generateMultipleChoiceQuestion(category, difficultyRange);
      case QuestionType.trueFalse:
        return _generateTrueFalseQuestion(category, difficultyRange);
      case QuestionType.scenario:
        return _generateScenarioQuestion(category, difficultyRange);
      default:
        return _generateChartIdentificationQuestion(difficultyRange, usedPatterns);
    }
  }

  /// Generates a chart identification question
  EnhancedQuizQuestion _generateChartIdentificationQuestion(
    DifficultyRange difficultyRange,
    Set<String> usedPatterns,
  ) {
    // Select a random pattern within difficulty range
    final availablePatterns = _patterns.where((pattern) {
      final difficulty = _getPatternDifficulty(pattern);
      return difficulty >= difficultyRange.min && 
             difficulty <= difficultyRange.max &&
             !usedPatterns.contains(pattern.id);
    }).toList();
    
    if (availablePatterns.isEmpty) {
      // Fallback to any pattern if none available
      final pattern = _patterns[_random.nextInt(_patterns.length)];
      return _createChartIdentificationQuestion(pattern, difficultyRange);
    }
    
    final correctPattern = availablePatterns[_random.nextInt(availablePatterns.length)];
    return _createChartIdentificationQuestion(correctPattern, difficultyRange);
  }

  /// Creates a chart identification question
  EnhancedQuizQuestion _createChartIdentificationQuestion(
    ChartPattern correctPattern,
    DifficultyRange difficultyRange,
  ) {
    // Generate chart data
    final generator = PatternGeneratorFactory.createGenerator(
      patternId: correctPattern.id,
      seed: _random.nextInt(1000000),
    );
    
    final chartData = generator.generateCandlesticks(length: 40);
    final supportResistance = generator.generateSupportResistanceLevels(chartData);
    
    // Generate options
    final options = _generatePatternOptions(correctPattern, difficultyRange);
    
    // Create explanation
    final explanation = _createPatternExplanation(correctPattern, chartData);
    
    return EnhancedQuizQuestion(
      questionText: 'What chart pattern is shown in this chart?',
      type: QuestionType.chartIdentification,
      difficulty: _getPatternDifficulty(correctPattern),
      category: QuizCategory.patternRecognition,
      chartData: chartData,
      supportResistanceLevels: supportResistance,
      options: options,
      correctAnswerIds: [correctPattern.id],
      explanation: explanation,
      tags: [correctPattern.trend.name, correctPattern.difficulty.name],
      estimatedTimeSeconds: 45,
    );
  }

  /// Generates multiple choice question
  EnhancedQuizQuestion _generateMultipleChoiceQuestion(
    QuizCategory category,
    DifficultyRange difficultyRange,
  ) {
    final questions = _getMultipleChoiceQuestions(category);
    final questionData = questions[_random.nextInt(questions.length)];
    
    return EnhancedQuizQuestion(
      questionText: questionData['question'],
      type: QuestionType.multipleChoice,
      difficulty: difficultyRange.min + _random.nextInt(difficultyRange.max - difficultyRange.min + 1),
      category: category,
      options: (questionData['options'] as List<String>).map((option) => 
        QuizOption(text: option, isCorrect: option == questionData['correct'])
      ).toList(),
      correctAnswerIds: [questionData['correct']],
      explanation: QuestionExplanation(
        mainExplanation: questionData['explanation'],
        correctAnswerReason: questionData['reason'],
      ),
      estimatedTimeSeconds: 30,
    );
  }

  /// Generates true/false question
  EnhancedQuizQuestion _generateTrueFalseQuestion(
    QuizCategory category,
    DifficultyRange difficultyRange,
  ) {
    final questions = _getTrueFalseQuestions(category);
    final questionData = questions[_random.nextInt(questions.length)];
    
    return EnhancedQuizQuestion(
      questionText: questionData['statement'],
      type: QuestionType.trueFalse,
      difficulty: difficultyRange.min + _random.nextInt(difficultyRange.max - difficultyRange.min + 1),
      category: category,
      options: [
        QuizOption(text: 'True', isCorrect: questionData['answer'] == true),
        QuizOption(text: 'False', isCorrect: questionData['answer'] == false),
      ],
      correctAnswerIds: [questionData['answer'] ? 'True' : 'False'],
      explanation: QuestionExplanation(
        mainExplanation: questionData['explanation'],
        correctAnswerReason: questionData['reason'],
      ),
      estimatedTimeSeconds: 20,
    );
  }

  /// Generates scenario-based question
  EnhancedQuizQuestion _generateScenarioQuestion(
    QuizCategory category,
    DifficultyRange difficultyRange,
  ) {
    final scenarios = _getScenarioQuestions(category);
    final scenarioData = scenarios[_random.nextInt(scenarios.length)];
    
    return EnhancedQuizQuestion(
      questionText: scenarioData['scenario'],
      type: QuestionType.scenario,
      difficulty: difficultyRange.min + _random.nextInt(difficultyRange.max - difficultyRange.min + 1),
      category: category,
      options: (scenarioData['options'] as List<String>).map((option) => 
        QuizOption(text: option, isCorrect: option == scenarioData['correct'])
      ).toList(),
      correctAnswerIds: [scenarioData['correct']],
      explanation: QuestionExplanation(
        mainExplanation: scenarioData['explanation'],
        correctAnswerReason: scenarioData['reason'],
        marketContext: scenarioData['context'],
      ),
      estimatedTimeSeconds: 60,
    );
  }

  /// Gets default configuration for skill level
  QuizSessionConfig _getDefaultConfig(SkillLevel skillLevel) {
    switch (skillLevel) {
      case SkillLevel.novice:
        return const QuizSessionConfig(
          maxQuestions: 5,
          difficultyRange: DifficultyRange(min: 1, max: 2),
          categories: [QuizCategory.candlestickBasics],
          useAdaptiveDifficulty: true,
          showImmediateExplanations: true,
          passingAccuracy: 0.6,
        );
      case SkillLevel.beginner:
        return const QuizSessionConfig(
          maxQuestions: 8,
          difficultyRange: DifficultyRange(min: 1, max: 3),
          categories: [QuizCategory.candlestickBasics, QuizCategory.patternRecognition],
          useAdaptiveDifficulty: true,
          showImmediateExplanations: true,
          passingAccuracy: 0.7,
        );
      case SkillLevel.intermediate:
        return const QuizSessionConfig(
          maxQuestions: 10,
          difficultyRange: DifficultyRange(min: 2, max: 4),
          categories: [
            QuizCategory.patternRecognition,
            QuizCategory.technicalAnalysis,
            QuizCategory.marketPsychology,
          ],
          useAdaptiveDifficulty: true,
          showImmediateExplanations: false,
          passingAccuracy: 0.75,
        );
      case SkillLevel.advanced:
        return const QuizSessionConfig(
          maxQuestions: 12,
          difficultyRange: DifficultyRange(min: 3, max: 5),
          categories: [
            QuizCategory.patternRecognition,
            QuizCategory.technicalAnalysis,
            QuizCategory.tradingStrategies,
            QuizCategory.riskManagement,
          ],
          useAdaptiveDifficulty: true,
          showImmediateExplanations: false,
          passingAccuracy: 0.8,
        );
      case SkillLevel.expert:
        return const QuizSessionConfig(
          maxQuestions: 15,
          difficultyRange: DifficultyRange(min: 4, max: 5),
          useAdaptiveDifficulty: true,
          showImmediateExplanations: false,
          passingAccuracy: 0.85,
        );
    }
  }

  /// Gets difficulty range for skill level
  DifficultyRange _getDifficultyRange(SkillLevel skillLevel) {
    switch (skillLevel) {
      case SkillLevel.novice:
        return const DifficultyRange(min: 1, max: 2);
      case SkillLevel.beginner:
        return const DifficultyRange(min: 1, max: 3);
      case SkillLevel.intermediate:
        return const DifficultyRange(min: 2, max: 4);
      case SkillLevel.advanced:
        return const DifficultyRange(min: 3, max: 5);
      case SkillLevel.expert:
        return const DifficultyRange(min: 4, max: 5);
    }
  }

  /// Selects question type based on categories and difficulty
  QuestionType _selectQuestionType(List<QuizCategory> categories, DifficultyRange difficultyRange) {
    if (categories.contains(QuizCategory.patternRecognition)) {
      return QuestionType.chartIdentification;
    }
    
    final types = [
      QuestionType.multipleChoice,
      QuestionType.trueFalse,
      if (difficultyRange.max >= 3) QuestionType.scenario,
    ];
    
    return types[_random.nextInt(types.length)];
  }

  /// Gets pattern difficulty as integer
  int _getPatternDifficulty(ChartPattern pattern) {
    switch (pattern.difficulty) {
      case PatternDifficulty.beginner:
        return 1;
      case PatternDifficulty.intermediate:
        return 3;
      case PatternDifficulty.advanced:
        return 5;
    }
  }

  /// Generates pattern options for multiple choice
  List<QuizOption> _generatePatternOptions(ChartPattern correctPattern, DifficultyRange difficultyRange) {
    final options = <QuizOption>[];
    
    // Add correct option
    options.add(QuizOption(
      text: correctPattern.name,
      isCorrect: true,
    ));
    
    // Add incorrect options
    final incorrectPatterns = _patterns.where((p) => p.id != correctPattern.id).toList();
    incorrectPatterns.shuffle(_random);
    
    for (int i = 0; i < 3 && i < incorrectPatterns.length; i++) {
      options.add(QuizOption(
        text: incorrectPatterns[i].name,
        isCorrect: false,
      ));
    }
    
    options.shuffle(_random);
    return options;
  }

  /// Creates explanation for pattern question
  QuestionExplanation _createPatternExplanation(ChartPattern pattern, List<CandlestickData> chartData) {
    return QuestionExplanation(
      mainExplanation: pattern.description,
      correctAnswerReason: 'This chart shows a ${pattern.name} pattern, which is characterized by ${pattern.description}',
      marketContext: 'This pattern typically occurs in ${pattern.trend.name} market conditions and suggests ${_getPatternImplication(pattern)}.',
    );
  }

  /// Gets pattern implication for explanation
  String _getPatternImplication(ChartPattern pattern) {
    switch (pattern.trend) {
      case PatternTrend.bullish:
        return 'potential upward price movement';
      case PatternTrend.bearish:
        return 'potential downward price movement';
      case PatternTrend.neutral:
        return 'continuation of the current trend';
    }
  }

  /// Gets multiple choice questions for category
  List<Map<String, dynamic>> _getMultipleChoiceQuestions(QuizCategory category) {
    // This would typically come from a database or configuration file
    // For now, returning sample questions
    return [
      {
        'question': 'What does a long upper wick on a candlestick indicate?',
        'options': ['Strong buying pressure', 'Strong selling pressure', 'Market indecision', 'Low volume'],
        'correct': 'Strong selling pressure',
        'explanation': 'A long upper wick indicates that prices moved higher during the session but were pushed back down by selling pressure.',
        'reason': 'The upper wick represents the high price that was rejected by sellers.',
      },
      // Add more questions here...
    ];
  }

  /// Gets true/false questions for category
  List<Map<String, dynamic>> _getTrueFalseQuestions(QuizCategory category) {
    return [
      {
        'statement': 'A doji candlestick indicates strong market direction.',
        'answer': false,
        'explanation': 'A doji candlestick indicates market indecision, not strong direction.',
        'reason': 'The open and close prices are nearly equal, showing balance between buyers and sellers.',
      },
      // Add more questions here...
    ];
  }

  /// Gets scenario questions for category
  List<Map<String, dynamic>> _getScenarioQuestions(QuizCategory category) {
    return [
      {
        'scenario': 'You notice a head and shoulders pattern forming on a daily chart. The neckline has just been broken with high volume. What should you consider?',
        'options': ['Enter a long position', 'Enter a short position', 'Wait for confirmation', 'Ignore the signal'],
        'correct': 'Enter a short position',
        'explanation': 'A head and shoulders pattern with neckline break and high volume is a strong bearish signal.',
        'reason': 'The pattern completion with volume confirmation suggests downward momentum.',
        'context': 'This scenario tests understanding of pattern completion and volume confirmation.',
      },
      // Add more scenarios here...
    ];
  }
}
