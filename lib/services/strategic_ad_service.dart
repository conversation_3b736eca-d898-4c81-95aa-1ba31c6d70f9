import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:learn_chart_patterns/services/ad_service.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';
import 'package:learn_chart_patterns/services/storage_service.dart';

/// Service for strategic ad placement
class StrategicAdService {
  /// Singleton instance
  static final StrategicAdService _instance = StrategicAdService._internal();

  /// Factory constructor
  factory StrategicAdService() => _instance;

  /// Internal constructor
  StrategicAdService._internal();

  /// Ad service
  final AdService _adService = AdService();

  /// Purchase service
  final PurchaseService _purchaseService = PurchaseService();

  /// Storage service
  late StorageService _storageService;

  /// Whether the service is initialized
  bool _initialized = false;

  /// Last interstitial ad time
  DateTime? _lastInterstitialAdTime;

  /// Session interaction count
  int _sessionInteractionCount = 0;

  /// Daily rewarded ad count
  int _dailyRewardedAdCount = 0;

  /// Last rewarded ad date
  DateTime? _lastRewardedAdDate;

  /// Minimum time between interstitial ads (in minutes)
  static const int _minInterstitialAdIntervalMinutes = 5;

  /// Minimum interactions before showing an interstitial ad
  static const int _minInteractionsBeforeAd = 5;

  /// Maximum daily rewarded ads
  static const int _maxDailyRewardedAds = 5;

  /// Initialize the service
  Future<void> initialize(StorageService storageService) async {
    if (_initialized) return;

    _storageService = storageService;

    // Load ad state
    await _loadAdState();

    _initialized = true;
  }

  /// Load ad state from storage
  Future<void> _loadAdState() async {
    // Load last interstitial ad time
    final lastInterstitialAdTimeMillis = await _storageService.getInt('last_interstitial_ad_time');
    if (lastInterstitialAdTimeMillis != null) {
      _lastInterstitialAdTime = DateTime.fromMillisecondsSinceEpoch(lastInterstitialAdTimeMillis);
    }

    // Load daily rewarded ad count
    final lastRewardedAdDateString = await _storageService.getString('last_rewarded_ad_date');
    if (lastRewardedAdDateString != null) {
      _lastRewardedAdDate = DateTime.parse(lastRewardedAdDateString);

      // Reset count if it's a new day
      if (_lastRewardedAdDate!.day != DateTime.now().day ||
          _lastRewardedAdDate!.month != DateTime.now().month ||
          _lastRewardedAdDate!.year != DateTime.now().year) {
        _dailyRewardedAdCount = 0;
      } else {
        _dailyRewardedAdCount = await _storageService.getInt('daily_rewarded_ad_count') ?? 0;
      }
    }
  }

  /// Save ad state to storage
  Future<void> _saveAdState() async {
    // Save last interstitial ad time
    if (_lastInterstitialAdTime != null) {
      await _storageService.setInt(
        'last_interstitial_ad_time',
        _lastInterstitialAdTime!.millisecondsSinceEpoch,
      );
    }

    // Save daily rewarded ad count
    if (_lastRewardedAdDate != null) {
      await _storageService.setString(
        'last_rewarded_ad_date',
        _lastRewardedAdDate!.toIso8601String(),
      );

      await _storageService.setInt(
        'daily_rewarded_ad_count',
        _dailyRewardedAdCount,
      );
    }
  }

  /// Increment session interaction count
  void incrementInteractionCount() {
    _sessionInteractionCount++;
  }

  /// Reset session interaction count
  void resetInteractionCount() {
    _sessionInteractionCount = 0;
  }

  /// Check if an interstitial ad should be shown
  bool shouldShowInterstitialAd() {
    // Don't show ads to premium users
    if (_purchaseService.hasPremium) {
      return false;
    }

    // Check if enough time has passed since the last ad
    if (_lastInterstitialAdTime != null) {
      final timeSinceLastAd = DateTime.now().difference(_lastInterstitialAdTime!);
      if (timeSinceLastAd.inMinutes < _minInterstitialAdIntervalMinutes) {
        return false;
      }
    }

    // Check if enough interactions have occurred
    return _sessionInteractionCount >= _minInteractionsBeforeAd;
  }

  /// Check if a rewarded ad can be shown
  bool canShowRewardedAd() {
    // Check if the daily limit has been reached
    if (_dailyRewardedAdCount >= _maxDailyRewardedAds) {
      return false;
    }

    return true;
  }

  /// Show an interstitial ad
  Future<bool> showInterstitialAd() async {
    // Don't show ads to premium users
    if (_purchaseService.hasPremium) {
      return false;
    }

    // Show the ad
    final adShown = await _adService.showInterstitialAd();

    if (adShown) {
      // Update last ad time
      _lastInterstitialAdTime = DateTime.now();

      // Reset interaction count
      _sessionInteractionCount = 0;

      // Save state
      await _saveAdState();
    }

    return adShown;
  }

  /// Show a rewarded ad
  Future<bool> showRewardedAd(Function(RewardItem) onRewarded) async {
    // Check if the daily limit has been reached
    if (_dailyRewardedAdCount >= _maxDailyRewardedAds) {
      return false;
    }

    // Show the ad
    final adShown = await _adService.showRewardedAd((reward) {
      // Increment daily count
      _dailyRewardedAdCount++;

      // Update last ad date
      _lastRewardedAdDate = DateTime.now();

      // Save state
      _saveAdState();

      // Call the reward callback
      onRewarded(reward);
    });

    return adShown;
  }

  /// Show a strategic interstitial ad if conditions are met
  Future<bool> showStrategicInterstitialAd(BuildContext context) async {
    if (shouldShowInterstitialAd()) {
      // Show a loading indicator
      final navigator = Navigator.of(context);
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Show the ad
      final adShown = await showInterstitialAd();

      // Close the loading indicator
      navigator.pop();

      return adShown;
    }

    return false;
  }

  /// Show a rewarded ad dialog
  Future<bool> showRewardedAdDialog(
    BuildContext context, {
    required String title,
    required String description,
    required String rewardText,
  }) async {
    // Don't show the dialog if the daily limit has been reached
    if (!canShowRewardedAd()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('You\'ve reached the daily limit for rewarded ads. Try again tomorrow!'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false;
    }

    // Show the dialog
    final shouldShowAd = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(description),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber),
                const SizedBox(width: 8),
                Expanded(child: Text(rewardText)),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'You can watch up to ${_maxDailyRewardedAds} rewarded ads per day.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('No Thanks'),
          ),
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pop(true),
            icon: const Icon(Icons.play_circle_outline),
            label: const Text('Watch Ad'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    ) ?? false;

    if (shouldShowAd) {
      // Show a loading indicator
      final navigator = Navigator.of(context);
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Show the ad
      bool adShown = false;
      try {
        adShown = await showRewardedAd((reward) {
          // Show a success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Reward earned: ${reward.amount} ${reward.type}'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.green,
            ),
          );
        });
      } finally {
        // Close the loading indicator
        navigator.pop();
      }

      if (!adShown) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load the ad. Please try again later.'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      return adShown;
    }

    return false;
  }
}
