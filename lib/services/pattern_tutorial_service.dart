import 'package:learn_chart_patterns/models/learning_progression.dart';

/// Service for generating pattern tutorials
class PatternTutorialService {
  /// Generates a tutorial for a specific pattern
  Future<PatternTutorial> generateTutorial(String patternId) async {
    final steps = _generateStepsForPattern(patternId);
    
    return PatternTutorial(
      patternId: patternId,
      steps: steps,
    );
  }

  /// Generates tutorial steps for a specific pattern
  List<TutorialStep> _generateStepsForPattern(String patternId) {
    switch (patternId) {
      case 'double_top':
        return _generateDoubleTopSteps();
      case 'double_bottom':
        return _generateDoubleBottomSteps();
      case 'head_and_shoulders':
        return _generateHeadAndShouldersSteps();
      case 'inverse_head_and_shoulders':
        return _generateInverseHeadAndShouldersSteps();
      case 'ascending_triangle':
        return _generateAscendingTriangleSteps();
      case 'descending_triangle':
        return _generateDescendingTriangleSteps();
      case 'cup_and_handle':
        return _generateCupAndHandleSteps();
      default:
        return _generateGenericSteps(patternId);
    }
  }

  /// Generates steps for Double Top pattern
  List<TutorialStep> _generateDoubleTopSteps() {
    return [
      const TutorialStep(
        id: 'dt_intro',
        title: 'Welcome to Double Top Pattern',
        description: 'The Double Top is a bearish reversal pattern that signals the end of an uptrend. Let\'s learn how to identify it!',
        type: TutorialStepType.introduction,
      ),
      const TutorialStep(
        id: 'dt_structure',
        title: 'Pattern Structure',
        description: 'A Double Top consists of two peaks at roughly the same price level, separated by a valley. The pattern looks like the letter "M".',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'dt_peaks',
        title: 'Identifying the Peaks',
        description: 'Look for two distinct peaks that reach approximately the same height. These peaks should be separated by a clear decline.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Tap on the two peaks in the chart',
        hint: 'The peaks should be at similar price levels',
      ),
      const TutorialStep(
        id: 'dt_support',
        title: 'Finding the Support Level',
        description: 'The valley between the two peaks creates a support level. This is where the price found temporary support before the second peak.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Tap on the support level',
        hint: 'Look for the lowest point between the two peaks',
      ),
      const TutorialStep(
        id: 'dt_confirmation',
        title: 'Pattern Confirmation',
        description: 'The pattern is confirmed when the price breaks below the support level with increased volume.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'dt_quiz',
        title: 'Quick Quiz',
        description: 'Can you identify the Double Top pattern in this chart?',
        type: TutorialStepType.quiz,
        isInteractive: true,
      ),
      const TutorialStep(
        id: 'dt_summary',
        title: 'Summary',
        description: 'Great job! You\'ve learned to identify Double Top patterns. Remember: two equal peaks + support break = bearish signal.',
        type: TutorialStepType.summary,
      ),
    ];
  }

  /// Generates steps for Double Bottom pattern
  List<TutorialStep> _generateDoubleBottomSteps() {
    return [
      const TutorialStep(
        id: 'db_intro',
        title: 'Welcome to Double Bottom Pattern',
        description: 'The Double Bottom is a bullish reversal pattern that signals the end of a downtrend. It\'s the opposite of Double Top!',
        type: TutorialStepType.introduction,
      ),
      const TutorialStep(
        id: 'db_structure',
        title: 'Pattern Structure',
        description: 'A Double Bottom consists of two troughs at roughly the same price level, separated by a peak. The pattern looks like the letter "W".',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'db_troughs',
        title: 'Identifying the Troughs',
        description: 'Look for two distinct troughs that reach approximately the same depth. These should be separated by a clear rally.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Tap on the two troughs in the chart',
        hint: 'The troughs should be at similar price levels',
      ),
      const TutorialStep(
        id: 'db_resistance',
        title: 'Finding the Resistance Level',
        description: 'The peak between the two troughs creates a resistance level. This is where the price faced selling pressure.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Tap on the resistance level',
        hint: 'Look for the highest point between the two troughs',
      ),
      const TutorialStep(
        id: 'db_confirmation',
        title: 'Pattern Confirmation',
        description: 'The pattern is confirmed when the price breaks above the resistance level with increased volume.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'db_quiz',
        title: 'Quick Quiz',
        description: 'Can you identify the Double Bottom pattern in this chart?',
        type: TutorialStepType.quiz,
        isInteractive: true,
      ),
      const TutorialStep(
        id: 'db_summary',
        title: 'Summary',
        description: 'Excellent! You\'ve learned to identify Double Bottom patterns. Remember: two equal troughs + resistance break = bullish signal.',
        type: TutorialStepType.summary,
      ),
    ];
  }

  /// Generates steps for Head and Shoulders pattern
  List<TutorialStep> _generateHeadAndShouldersSteps() {
    return [
      const TutorialStep(
        id: 'hs_intro',
        title: 'Welcome to Head and Shoulders',
        description: 'The Head and Shoulders is one of the most reliable bearish reversal patterns. Let\'s master it together!',
        type: TutorialStepType.introduction,
      ),
      const TutorialStep(
        id: 'hs_structure',
        title: 'Pattern Structure',
        description: 'This pattern has three peaks: a left shoulder, a higher head in the middle, and a right shoulder at roughly the same height as the left.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'hs_shoulders',
        title: 'Identifying the Shoulders',
        description: 'The left and right shoulders should be at approximately the same height. They form the "shoulders" of the pattern.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Tap on both shoulders',
        hint: 'Look for two peaks of similar height',
      ),
      const TutorialStep(
        id: 'hs_head',
        title: 'Finding the Head',
        description: 'The head is the highest peak in the middle, clearly higher than both shoulders.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Tap on the head',
        hint: 'The head should be the highest point',
      ),
      const TutorialStep(
        id: 'hs_neckline',
        title: 'Drawing the Neckline',
        description: 'The neckline connects the two troughs between the shoulders and head. This is the key support level.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Draw the neckline',
        hint: 'Connect the two low points between the peaks',
      ),
      const TutorialStep(
        id: 'hs_confirmation',
        title: 'Pattern Confirmation',
        description: 'The pattern is confirmed when the price breaks below the neckline. The target is typically the height of the head below the neckline.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'hs_quiz',
        title: 'Pattern Recognition Quiz',
        description: 'Now test your skills! Can you identify all parts of this Head and Shoulders pattern?',
        type: TutorialStepType.quiz,
        isInteractive: true,
      ),
      const TutorialStep(
        id: 'hs_summary',
        title: 'Congratulations!',
        description: 'You\'ve mastered the Head and Shoulders pattern! This is a powerful tool for identifying trend reversals.',
        type: TutorialStepType.summary,
      ),
    ];
  }

  /// Generates steps for Inverse Head and Shoulders pattern
  List<TutorialStep> _generateInverseHeadAndShouldersSteps() {
    return [
      const TutorialStep(
        id: 'ihs_intro',
        title: 'Inverse Head and Shoulders',
        description: 'This is the bullish version of Head and Shoulders. It signals the end of a downtrend and potential upward reversal.',
        type: TutorialStepType.introduction,
      ),
      const TutorialStep(
        id: 'ihs_structure',
        title: 'Upside-Down Structure',
        description: 'This pattern has three troughs: left shoulder, deeper head in the middle, and right shoulder. It\'s like an upside-down Head and Shoulders.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'ihs_identification',
        title: 'Pattern Identification',
        description: 'Look for the deepest trough (head) flanked by two shallower troughs (shoulders) at similar levels.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Identify the head and shoulders',
        hint: 'The head should be the lowest point',
      ),
      const TutorialStep(
        id: 'ihs_neckline',
        title: 'The Neckline',
        description: 'The neckline connects the peaks between the troughs. A break above this line confirms the bullish reversal.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Draw the neckline',
        hint: 'Connect the high points between the troughs',
      ),
      const TutorialStep(
        id: 'ihs_quiz',
        title: 'Recognition Test',
        description: 'Can you spot the Inverse Head and Shoulders pattern in this chart?',
        type: TutorialStepType.quiz,
        isInteractive: true,
      ),
      const TutorialStep(
        id: 'ihs_summary',
        title: 'Well Done!',
        description: 'You now understand both Head and Shoulders patterns. These are essential tools for any technical analyst!',
        type: TutorialStepType.summary,
      ),
    ];
  }

  /// Generates steps for Ascending Triangle pattern
  List<TutorialStep> _generateAscendingTriangleSteps() {
    return [
      const TutorialStep(
        id: 'at_intro',
        title: 'Ascending Triangle Pattern',
        description: 'The Ascending Triangle is a bullish continuation pattern that shows buyers becoming more aggressive.',
        type: TutorialStepType.introduction,
      ),
      const TutorialStep(
        id: 'at_structure',
        title: 'Triangle Structure',
        description: 'This pattern has a flat horizontal resistance line and an ascending support line, forming a triangle.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'at_resistance',
        title: 'Horizontal Resistance',
        description: 'The top of the triangle is formed by a horizontal resistance line where price repeatedly fails to break higher.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Draw the resistance line',
        hint: 'Connect the peaks at the same level',
      ),
      const TutorialStep(
        id: 'at_support',
        title: 'Ascending Support',
        description: 'The bottom is formed by an ascending support line connecting higher lows, showing increasing buying pressure.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Draw the support line',
        hint: 'Connect the rising low points',
      ),
      const TutorialStep(
        id: 'at_breakout',
        title: 'Breakout Confirmation',
        description: 'The pattern is confirmed when price breaks above the horizontal resistance with strong volume.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'at_quiz',
        title: 'Triangle Recognition',
        description: 'Can you identify the Ascending Triangle in this chart?',
        type: TutorialStepType.quiz,
        isInteractive: true,
      ),
      const TutorialStep(
        id: 'at_summary',
        title: 'Triangle Mastery',
        description: 'Great work! Ascending Triangles are powerful bullish patterns that often lead to significant upward moves.',
        type: TutorialStepType.summary,
      ),
    ];
  }

  /// Generates steps for Descending Triangle pattern
  List<TutorialStep> _generateDescendingTriangleSteps() {
    return [
      const TutorialStep(
        id: 'dt_intro',
        title: 'Descending Triangle Pattern',
        description: 'The Descending Triangle is a bearish continuation pattern showing increasing selling pressure.',
        type: TutorialStepType.introduction,
      ),
      const TutorialStep(
        id: 'dt_structure',
        title: 'Opposite Structure',
        description: 'This pattern has a flat horizontal support line and a descending resistance line, opposite of Ascending Triangle.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'dt_identification',
        title: 'Pattern Elements',
        description: 'Look for horizontal support at the bottom and descending resistance connecting lower highs.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Identify support and resistance lines',
        hint: 'Support is flat, resistance slopes downward',
      ),
      const TutorialStep(
        id: 'dt_quiz',
        title: 'Pattern Test',
        description: 'Can you spot the Descending Triangle pattern?',
        type: TutorialStepType.quiz,
        isInteractive: true,
      ),
      const TutorialStep(
        id: 'dt_summary',
        title: 'Triangle Complete',
        description: 'Excellent! You now understand both Ascending and Descending Triangles.',
        type: TutorialStepType.summary,
      ),
    ];
  }

  /// Generates steps for Cup and Handle pattern
  List<TutorialStep> _generateCupAndHandleSteps() {
    return [
      const TutorialStep(
        id: 'ch_intro',
        title: 'Cup and Handle Pattern',
        description: 'The Cup and Handle is a bullish continuation pattern that resembles a tea cup when viewed sideways.',
        type: TutorialStepType.introduction,
      ),
      const TutorialStep(
        id: 'ch_cup',
        title: 'The Cup Formation',
        description: 'The cup is a U-shaped price pattern showing a gradual decline followed by a gradual recovery to previous highs.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'ch_handle',
        title: 'The Handle Formation',
        description: 'The handle is a small pullback after the cup, typically 1/3 the depth of the cup, before the final breakout.',
        type: TutorialStepType.explanation,
      ),
      const TutorialStep(
        id: 'ch_identification',
        title: 'Pattern Recognition',
        description: 'Identify the cup and handle in this chart.',
        type: TutorialStepType.identification,
        isInteractive: true,
        expectedAction: 'Outline the cup and handle',
        hint: 'Look for the U-shape followed by a small pullback',
      ),
      const TutorialStep(
        id: 'ch_quiz',
        title: 'Cup and Handle Quiz',
        description: 'Test your ability to recognize this pattern.',
        type: TutorialStepType.quiz,
        isInteractive: true,
      ),
      const TutorialStep(
        id: 'ch_summary',
        title: 'Pattern Mastered',
        description: 'Wonderful! The Cup and Handle is a reliable bullish pattern favored by many professional traders.',
        type: TutorialStepType.summary,
      ),
    ];
  }

  /// Generates generic steps for unknown patterns
  List<TutorialStep> _generateGenericSteps(String patternId) {
    final patternName = patternId.replaceAll('_', ' ').split(' ').map((word) => 
        word[0].toUpperCase() + word.substring(1)).join(' ');
    
    return [
      TutorialStep(
        id: '${patternId}_intro',
        title: 'Learning $patternName',
        description: 'Let\'s explore the $patternName pattern and learn how to identify it.',
        type: TutorialStepType.introduction,
      ),
      TutorialStep(
        id: '${patternId}_structure',
        title: 'Pattern Structure',
        description: 'Study the key characteristics and structure of the $patternName pattern.',
        type: TutorialStepType.explanation,
      ),
      TutorialStep(
        id: '${patternId}_identification',
        title: 'Pattern Identification',
        description: 'Practice identifying the key elements of this pattern.',
        type: TutorialStepType.identification,
        isInteractive: true,
      ),
      TutorialStep(
        id: '${patternId}_quiz',
        title: 'Knowledge Check',
        description: 'Test your understanding of the $patternName pattern.',
        type: TutorialStepType.quiz,
        isInteractive: true,
      ),
      TutorialStep(
        id: '${patternId}_summary',
        title: 'Summary',
        description: 'Great job learning the $patternName pattern!',
        type: TutorialStepType.summary,
      ),
    ];
  }
}
