import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:learn_chart_patterns/services/storage_service.dart';

/// Service for visual enhancements and animations
class VisualEnhancementService {
  /// Singleton instance
  static final VisualEnhancementService _instance = VisualEnhancementService._internal();

  /// Factory constructor
  factory VisualEnhancementService() => _instance;

  /// Internal constructor
  VisualEnhancementService._internal();

  /// Storage service
  late StorageService _storageService;

  /// Whether the service is initialized
  bool _initialized = false;

  /// Whether animations are enabled
  bool _animationsEnabled = true;

  /// Whether haptic feedback is enabled
  bool _hapticFeedbackEnabled = true;

  /// Whether reduced motion is enabled
  bool _reducedMotion = false;

  /// Animation durations
  final Map<String, Duration> _animationDurations = {
    'fast': const Duration(milliseconds: 150),
    'normal': const Duration(milliseconds: 300),
    'slow': const Duration(milliseconds: 500),
  };

  /// Animation curves
  final Map<String, Curve> _animationCurves = {
    'standard': Curves.easeInOut,
    'decelerate': Curves.decelerate,
    'accelerate': Curves.easeIn,
    'bounce': Curves.bounceOut,
    'elastic': Curves.elasticOut,
  };

  /// Initialize the service
  Future<void> initialize(StorageService storageService) async {
    if (_initialized) return;

    _storageService = storageService;

    // Load settings
    await _loadSettings();

    _initialized = true;
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    _animationsEnabled = await _storageService.getBool('animations_enabled') ?? true;
    _hapticFeedbackEnabled = await _storageService.getBool('haptic_feedback_enabled') ?? true;
    _reducedMotion = await _storageService.getBool('reduced_motion') ?? false;
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    await _storageService.setBool('animations_enabled', _animationsEnabled);
    await _storageService.setBool('haptic_feedback_enabled', _hapticFeedbackEnabled);
    await _storageService.setBool('reduced_motion', _reducedMotion);
  }

  /// Set whether animations are enabled
  Future<void> setAnimationsEnabled(bool enabled) async {
    _animationsEnabled = enabled;
    await _saveSettings();
  }

  /// Set whether haptic feedback is enabled
  Future<void> setHapticFeedbackEnabled(bool enabled) async {
    _hapticFeedbackEnabled = enabled;
    await _saveSettings();
  }

  /// Set whether reduced motion is enabled
  Future<void> setReducedMotion(bool enabled) async {
    _reducedMotion = enabled;
    await _saveSettings();
  }

  /// Get whether animations are enabled
  bool get animationsEnabled => _animationsEnabled;

  /// Get whether haptic feedback is enabled
  bool get hapticFeedbackEnabled => _hapticFeedbackEnabled;

  /// Get whether reduced motion is enabled
  bool get reducedMotion => _reducedMotion;

  /// Get an animation duration
  Duration getAnimationDuration(String type) {
    if (!_animationsEnabled || _reducedMotion) {
      return const Duration(milliseconds: 0);
    }

    return _animationDurations[type] ?? _animationDurations['normal']!;
  }

  /// Get an animation curve
  Curve getAnimationCurve(String type) {
    return _animationCurves[type] ?? _animationCurves['standard']!;
  }

  /// Perform haptic feedback
  void performHapticFeedback(HapticFeedbackType type) {
    if (!_hapticFeedbackEnabled) return;

    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
      case HapticFeedbackType.vibrate:
        HapticFeedback.vibrate();
        break;
    }
  }

  /// Create a page route with appropriate transitions
  PageRoute createPageRoute({
    required WidgetBuilder builder,
    RouteSettings? settings,
    bool fullscreenDialog = false,
    TransitionType transitionType = TransitionType.rightToLeft,
  }) {
    if (!_animationsEnabled || _reducedMotion) {
      return MaterialPageRoute(
        builder: builder,
        settings: settings,
        fullscreenDialog: fullscreenDialog,
      );
    }

    return PageRouteBuilder(
      settings: settings,
      fullscreenDialog: fullscreenDialog,
      pageBuilder: (context, animation, secondaryAnimation) => builder(context),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final curve = CurvedAnimation(
          parent: animation,
          curve: getAnimationCurve('standard'),
        );

        switch (transitionType) {
          case TransitionType.rightToLeft:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(curve),
              child: child,
            );
          case TransitionType.leftToRight:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(-1.0, 0.0),
                end: Offset.zero,
              ).animate(curve),
              child: child,
            );
          case TransitionType.bottomToTop:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 1.0),
                end: Offset.zero,
              ).animate(curve),
              child: child,
            );
          case TransitionType.topToBottom:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, -1.0),
                end: Offset.zero,
              ).animate(curve),
              child: child,
            );
          case TransitionType.fade:
            return FadeTransition(
              opacity: curve,
              child: child,
            );
          case TransitionType.scale:
            return ScaleTransition(
              scale: curve,
              child: child,
            );
          case TransitionType.rotation:
            return RotationTransition(
              turns: Tween<double>(
                begin: 0.1,
                end: 0.0,
              ).animate(curve),
              child: FadeTransition(
                opacity: curve,
                child: child,
              ),
            );
        }
      },
    );
  }

  /// Create a staggered animation controller
  AnimationController createStaggeredController(
    TickerProvider vsync, {
    String durationType = 'normal',
  }) {
    return AnimationController(
      vsync: vsync,
      duration: getAnimationDuration(durationType),
    );
  }

  /// Create a staggered animation
  Animation<double> createStaggeredAnimation(
    AnimationController controller, {
    double begin = 0.0,
    double end = 1.0,
    String curveType = 'standard',
    double startInterval = 0.0,
    double endInterval = 1.0,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: Interval(
          startInterval,
          endInterval,
          curve: getAnimationCurve(curveType),
        ),
      ),
    );
  }

  /// Apply a shimmer effect to a widget
  Widget applyShimmerEffect(
    Widget child, {
    Color baseColor = const Color(0xFFEBEBF4),
    Color highlightColor = const Color(0xFFF4F4F4),
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    if (!_animationsEnabled || _reducedMotion) {
      return child;
    }

    return ShimmerEffect(
      baseColor: baseColor,
      highlightColor: highlightColor,
      duration: duration,
      child: child,
    );
  }
}

/// Types of haptic feedback
enum HapticFeedbackType {
  /// Light impact
  light,

  /// Medium impact
  medium,

  /// Heavy impact
  heavy,

  /// Selection click
  selection,

  /// Vibrate
  vibrate,
}

/// Types of transitions
enum TransitionType {
  /// Right to left transition
  rightToLeft,

  /// Left to right transition
  leftToRight,

  /// Bottom to top transition
  bottomToTop,

  /// Top to bottom transition
  topToBottom,

  /// Fade transition
  fade,

  /// Scale transition
  scale,

  /// Rotation transition
  rotation,
}

/// A widget that applies a shimmer effect to its child
class ShimmerEffect extends StatefulWidget {
  /// The base color of the shimmer effect
  final Color baseColor;

  /// The highlight color of the shimmer effect
  final Color highlightColor;

  /// The duration of the shimmer effect
  final Duration duration;

  /// The child widget
  final Widget child;

  /// Creates a new shimmer effect
  const ShimmerEffect({
    super.key,
    required this.baseColor,
    required this.highlightColor,
    required this.duration,
    required this.child,
  });

  @override
  State<ShimmerEffect> createState() => _ShimmerEffectState();
}

class _ShimmerEffectState extends State<ShimmerEffect> with SingleTickerProviderStateMixin {
  /// Animation controller
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return ShaderMask(
          blendMode: BlendMode.srcATop,
          shaderCallback: (bounds) {
            return LinearGradient(
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: const [
                0.0,
                0.5,
                1.0,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              transform: _SlidingGradientTransform(
                slidePercent: _controller.value,
              ),
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// A gradient transform that slides the gradient
class _SlidingGradientTransform extends GradientTransform {
  /// The percentage to slide the gradient
  final double slidePercent;

  /// Creates a new sliding gradient transform
  const _SlidingGradientTransform({
    required this.slidePercent,
  });

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    return Matrix4.translationValues(
      bounds.width * (slidePercent * 2 - 1),
      0.0,
      0.0,
    );
  }
}
