import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/user_progress.dart';
import 'package:learn_chart_patterns/services/storage_service.dart';

/// Service for managing user progress
class ProgressService {
  /// Storage service for persisting progress
  final StorageService _storageService;
  
  /// Current user progress
  UserProgress _progress = UserProgress.empty();

  /// Creates a new progress service
  ProgressService(this._storageService);

  /// Initializes the progress service
  Future<void> init() async {
    await _loadProgress();
  }

  /// Gets the current user progress
  UserProgress get progress => _progress;

  /// Marks a pattern as learned
  Future<void> markPatternAsLearned(String patternId) async {
    final updatedProgress = _progress.markPatternAsLearned(patternId);
    await _updateProgress(updatedProgress);
  }

  /// Records a quiz result
  Future<void> recordQuizResult(String patternId, bool isCorrect) async {
    final updatedProgress = _progress.recordQuizResult(patternId, isCorrect);
    await _updateProgress(updatedProgress);
  }

  /// Updates the streak based on the current date
  Future<void> updateStreak() async {
    final updatedProgress = _progress.updateStreak();
    await _updateProgress(updatedProgress);
  }

  /// Resets all progress
  Future<void> resetProgress() async {
    await _updateProgress(UserProgress.empty());
  }

  /// Loads progress from storage
  Future<void> _loadProgress() async {
    final progress = await _storageService.getObject<UserProgress>(
      AppConstants.prefKeyUserProgress,
      (json) => UserProgress(
        learnedPatterns: Map<String, bool>.from(json['learnedPatterns'] ?? {}),
        quizAccuracy: Map<String, double>.from(
          (json['quizAccuracy'] ?? {}).map(
            (key, value) => MapEntry(key, (value as num).toDouble()),
          ),
        ),
        currentStreak: json['currentStreak'] ?? 0,
        highestStreak: json['highestStreak'] ?? 0,
        lastPracticeDate: json['lastPracticeDate'] != null
            ? DateTime.parse(json['lastPracticeDate'])
            : null,
        totalQuizzesCompleted: json['totalQuizzesCompleted'] ?? 0,
        totalCorrectAnswers: json['totalCorrectAnswers'] ?? 0,
      ),
    );
    
    if (progress != null) {
      _progress = progress;
    }
  }

  /// Updates progress in memory and storage
  Future<void> _updateProgress(UserProgress progress) async {
    _progress = progress;
    
    await _storageService.setObject<UserProgress>(
      AppConstants.prefKeyUserProgress,
      progress,
      (progress) => {
        'learnedPatterns': progress.learnedPatterns,
        'quizAccuracy': progress.quizAccuracy,
        'currentStreak': progress.currentStreak,
        'highestStreak': progress.highestStreak,
        'lastPracticeDate': progress.lastPracticeDate?.toIso8601String(),
        'totalQuizzesCompleted': progress.totalQuizzesCompleted,
        'totalCorrectAnswers': progress.totalCorrectAnswers,
      },
    );
  }
}
