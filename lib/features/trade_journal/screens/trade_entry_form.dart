import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_tag.dart';
import 'package:learn_chart_patterns/features/trade_journal/providers/trade_journal_provider.dart';
import 'package:learn_chart_patterns/providers/pattern_provider.dart';
import 'package:provider/provider.dart';

/// Form for adding or editing a trade entry
class TradeEntryForm extends StatefulWidget {
  /// The trade entry to edit (null for new entries)
  final TradeEntry? entry;

  /// Creates a new trade entry form
  const TradeEntryForm({
    super.key,
    this.entry,
  });

  @override
  State<TradeEntryForm> createState() => _TradeEntryFormState();
}

class _TradeEntryFormState extends State<TradeEntryForm> {
  /// Form key
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  /// Symbol
  String _symbol = '';

  /// Entry price
  double? _entryPrice;

  /// Position size
  double? _positionSize;

  /// Direction
  TradeDirection _direction = TradeDirection.long;

  /// Pattern ID
  String? _patternId;

  /// Risk-reward ratio
  double? _riskRewardRatio;

  /// Stop loss
  double? _stopLoss;

  /// Take profit
  double? _takeProfit;

  /// Notes
  String? _notes;

  /// Selected tag IDs
  List<String> _selectedTagIds = [];

  /// Sentiment before
  int? _sentimentBefore;

  /// Timeframe
  String? _timeframe;

  /// Image files
  List<XFile> _imageFiles = [];

  /// Text editing controllers
  final TextEditingController _symbolController = TextEditingController();
  final TextEditingController _entryPriceController = TextEditingController();
  final TextEditingController _positionSizeController = TextEditingController();
  final TextEditingController _riskRewardRatioController = TextEditingController();
  final TextEditingController _stopLossController = TextEditingController();
  final TextEditingController _takeProfitController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _timeframeController = TextEditingController();

  /// Image picker
  final ImagePicker _imagePicker = ImagePicker();

  /// Whether the form is in edit mode
  bool get _isEditMode => widget.entry != null;

  @override
  void initState() {
    super.initState();
    
    // Initialize form values if editing an existing entry
    if (_isEditMode) {
      _symbol = widget.entry!.symbol;
      _symbolController.text = _symbol;
      
      _entryPrice = widget.entry!.entryPrice;
      _entryPriceController.text = _entryPrice.toString();
      
      _positionSize = widget.entry!.positionSize;
      _positionSizeController.text = _positionSize.toString();
      
      _direction = widget.entry!.direction;
      
      _patternId = widget.entry!.patternId;
      
      _riskRewardRatio = widget.entry!.riskRewardRatio;
      if (_riskRewardRatio != null) {
        _riskRewardRatioController.text = _riskRewardRatio.toString();
      }
      
      _stopLoss = widget.entry!.stopLoss;
      if (_stopLoss != null) {
        _stopLossController.text = _stopLoss.toString();
      }
      
      _takeProfit = widget.entry!.takeProfit;
      if (_takeProfit != null) {
        _takeProfitController.text = _takeProfit.toString();
      }
      
      _notes = widget.entry!.notes;
      if (_notes != null) {
        _notesController.text = _notes!;
      }
      
      _selectedTagIds = List<String>.from(widget.entry!.tags);
      
      _sentimentBefore = widget.entry!.sentimentBefore;
      
      _timeframe = widget.entry!.timeframe;
      if (_timeframe != null) {
        _timeframeController.text = _timeframe!;
      }
    }
  }

  @override
  void dispose() {
    _symbolController.dispose();
    _entryPriceController.dispose();
    _positionSizeController.dispose();
    _riskRewardRatioController.dispose();
    _stopLossController.dispose();
    _takeProfitController.dispose();
    _notesController.dispose();
    _timeframeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          _isEditMode ? 'Edit Trade' : 'New Trade',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: provider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildForm(context),
      bottomNavigationBar: _buildBottomBar(context),
    );
  }

  /// Builds the form
  Widget _buildForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        children: [
          // Required fields section
          _buildSectionTitle(context, 'Required Fields'),
          
          // Symbol
          TextFormField(
            controller: _symbolController,
            decoration: const InputDecoration(
              labelText: 'Symbol *',
              hintText: 'Enter the trading pair or symbol (e.g., EUR/USD)',
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the symbol';
              }
              return null;
            },
            onChanged: (value) {
              setState(() {
                _symbol = value;
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Entry price
          TextFormField(
            controller: _entryPriceController,
            decoration: const InputDecoration(
              labelText: 'Entry Price *',
              hintText: 'Enter the entry price',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the entry price';
              }
              
              final price = double.tryParse(value);
              if (price == null || price <= 0) {
                return 'Please enter a valid price';
              }
              
              return null;
            },
            onChanged: (value) {
              setState(() {
                _entryPrice = double.tryParse(value);
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Position size
          TextFormField(
            controller: _positionSizeController,
            decoration: const InputDecoration(
              labelText: 'Position Size *',
              hintText: 'Enter the position size',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the position size';
              }
              
              final size = double.tryParse(value);
              if (size == null || size <= 0) {
                return 'Please enter a valid position size';
              }
              
              return null;
            },
            onChanged: (value) {
              setState(() {
                _positionSize = double.tryParse(value);
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Direction
          _buildDirectionSelector(context),
          
          const SizedBox(height: AppConstants.spacingLarge),
          
          // Trade setup section
          _buildSectionTitle(context, 'Trade Setup'),
          
          // Pattern
          _buildPatternSelector(context),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Risk-reward ratio
          TextFormField(
            controller: _riskRewardRatioController,
            decoration: const InputDecoration(
              labelText: 'Risk-Reward Ratio',
              hintText: 'Enter the risk-reward ratio (e.g., 2 for 1:2)',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final ratio = double.tryParse(value);
                if (ratio == null || ratio <= 0) {
                  return 'Please enter a valid risk-reward ratio';
                }
              }
              
              return null;
            },
            onChanged: (value) {
              setState(() {
                _riskRewardRatio = double.tryParse(value);
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Stop loss
          TextFormField(
            controller: _stopLossController,
            decoration: const InputDecoration(
              labelText: 'Stop Loss',
              hintText: 'Enter the stop loss price',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final price = double.tryParse(value);
                if (price == null || price <= 0) {
                  return 'Please enter a valid price';
                }
              }
              
              return null;
            },
            onChanged: (value) {
              setState(() {
                _stopLoss = double.tryParse(value);
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Take profit
          TextFormField(
            controller: _takeProfitController,
            decoration: const InputDecoration(
              labelText: 'Take Profit',
              hintText: 'Enter the take profit price',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final price = double.tryParse(value);
                if (price == null || price <= 0) {
                  return 'Please enter a valid price';
                }
              }
              
              return null;
            },
            onChanged: (value) {
              setState(() {
                _takeProfit = double.tryParse(value);
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Timeframe
          TextFormField(
            controller: _timeframeController,
            decoration: const InputDecoration(
              labelText: 'Timeframe',
              hintText: 'Enter the timeframe (e.g., 1H, 4H, 1D)',
            ),
            onChanged: (value) {
              setState(() {
                _timeframe = value.isEmpty ? null : value;
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingLarge),
          
          // Additional information section
          _buildSectionTitle(context, 'Additional Information'),
          
          // Tags
          _buildTagSelector(context),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Sentiment before
          Text(
            'Sentiment Before (1-10)',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: AppConstants.spacingSmall),
          Slider(
            value: _sentimentBefore?.toDouble() ?? 5,
            min: 1,
            max: 10,
            divisions: 9,
            label: _sentimentBefore?.toString() ?? '5',
            onChanged: (value) {
              setState(() {
                _sentimentBefore = value.round();
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Notes
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'Notes',
              hintText: 'Enter any notes about this trade',
              alignLabelWithHint: true,
            ),
            maxLines: 5,
            onChanged: (value) {
              setState(() {
                _notes = value.isEmpty ? null : value;
              });
            },
          ),
          
          const SizedBox(height: AppConstants.spacingLarge),
          
          // Images section
          _buildSectionTitle(context, 'Images'),
          
          // Image picker
          _buildImagePicker(context),
          
          const SizedBox(height: AppConstants.spacingLarge),
        ],
      ),
    );
  }

  /// Builds the bottom bar
  Widget _buildBottomBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Cancel button
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Cancel'),
            ),
          ),
          
          const SizedBox(width: AppConstants.spacingMedium),
          
          // Save button
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  _saveTradeEntry(context);
                }
              },
              child: Text(_isEditMode ? 'Update' : 'Save'),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a section title
  Widget _buildSectionTitle(BuildContext context, String title) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppConstants.paddingSmall,
      ),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Builds the direction selector
  Widget _buildDirectionSelector(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Direction *',
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(height: AppConstants.spacingSmall),
        Row(
          children: [
            // Long button
            Expanded(
              child: RadioListTile<TradeDirection>(
                title: const Text('Long'),
                value: TradeDirection.long,
                groupValue: _direction,
                onChanged: (value) {
                  setState(() {
                    _direction = value!;
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
            
            // Short button
            Expanded(
              child: RadioListTile<TradeDirection>(
                title: const Text('Short'),
                value: TradeDirection.short,
                groupValue: _direction,
                onChanged: (value) {
                  setState(() {
                    _direction = value!;
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds the pattern selector
  Widget _buildPatternSelector(BuildContext context) {
    final theme = Theme.of(context);
    final patternProvider = Provider.of<PatternProvider>(context);
    final patterns = patternProvider.allPatterns;
    
    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'Pattern',
        hintText: 'Select the chart pattern',
      ),
      value: _patternId,
      items: [
        const DropdownMenuItem<String>(
          value: null,
          child: Text('None'),
        ),
        ...patterns.map((pattern) {
          return DropdownMenuItem<String>(
            value: pattern.id,
            child: Text(pattern.name),
          );
        }).toList(),
      ],
      onChanged: (value) {
        setState(() {
          _patternId = value;
        });
      },
    );
  }

  /// Builds the tag selector
  Widget _buildTagSelector(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    final tags = provider.tags;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(height: AppConstants.spacingSmall),
        Wrap(
          spacing: AppConstants.spacingSmall,
          runSpacing: AppConstants.spacingSmall,
          children: tags.map((tag) {
            final isSelected = _selectedTagIds.contains(tag.id);
            
            return FilterChip(
              label: Text(tag.name),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedTagIds.add(tag.id);
                  } else {
                    _selectedTagIds.remove(tag.id);
                  }
                });
              },
              backgroundColor: Color(tag.color.value).withAlpha(30),
              selectedColor: Color(tag.color.value).withAlpha(100),
              checkmarkColor: tag.color,
              labelStyle: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected ? tag.color : theme.colorScheme.onSurface,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Builds the image picker
  Widget _buildImagePicker(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Add image button
        ElevatedButton.icon(
          onPressed: () {
            _showImageSourceDialog(context);
          },
          icon: const Icon(Icons.add_photo_alternate),
          label: const Text('Add Image'),
        ),
        
        const SizedBox(height: AppConstants.spacingMedium),
        
        // Image preview grid
        if (_imageFiles.isNotEmpty || (_isEditMode && widget.entry!.imagePaths.isNotEmpty))
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: AppConstants.spacingSmall,
              mainAxisSpacing: AppConstants.spacingSmall,
            ),
            itemCount: _imageFiles.length + (_isEditMode ? widget.entry!.imagePaths.length : 0),
            itemBuilder: (context, index) {
              // New images
              if (index < _imageFiles.length) {
                final imageFile = _imageFiles[index];
                
                return Stack(
                  children: [
                    // Image preview
                    ClipRRect(
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                      child: Image.file(
                        File(imageFile.path),
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      ),
                    ),
                    
                    // Remove button
                    Positioned(
                      top: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _imageFiles.removeAt(index);
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }
              // Existing images
              else if (_isEditMode) {
                final existingIndex = index - _imageFiles.length;
                if (existingIndex < widget.entry!.imagePaths.length) {
                  final imagePath = widget.entry!.imagePaths[existingIndex];
                  
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    child: Image.file(
                      File(imagePath),
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  );
                }
              }
              
              return const SizedBox();
            },
          ),
      ],
    );
  }

  /// Shows the image source dialog
  void _showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Image'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Camera option
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.camera);
              },
            ),
            
            // Gallery option
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.gallery);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Picks an image from the given source
  Future<void> _pickImage(ImageSource source) async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: source,
        imageQuality: 70,
      );
      
      if (pickedFile != null) {
        setState(() {
          _imageFiles.add(pickedFile);
        });
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
    }
  }

  /// Saves the trade entry
  Future<void> _saveTradeEntry(BuildContext context) async {
    final provider = Provider.of<TradeJournalProvider>(context, listen: false);
    
    if (_isEditMode) {
      // Update existing entry
      final updatedEntry = widget.entry!.copyWith(
        symbol: _symbol,
        entryPrice: _entryPrice!,
        positionSize: _positionSize!,
        direction: _direction,
        patternId: _patternId,
        clearPatternId: _patternId == null,
        riskRewardRatio: _riskRewardRatio,
        clearRiskRewardRatio: _riskRewardRatio == null,
        stopLoss: _stopLoss,
        clearStopLoss: _stopLoss == null,
        takeProfit: _takeProfit,
        clearTakeProfit: _takeProfit == null,
        notes: _notes,
        clearNotes: _notes == null,
        tags: _selectedTagIds,
        sentimentBefore: _sentimentBefore,
        clearSentimentBefore: _sentimentBefore == null,
        timeframe: _timeframe,
        clearTimeframe: _timeframe == null,
      );
      
      await provider.updateTradeEntry(updatedEntry);
      
      // Add new images
      for (final imageFile in _imageFiles) {
        await provider.addTradeImage(
          tradeId: updatedEntry.id,
          imageFile: imageFile,
        );
      }
    } else {
      // Create new entry
      final newEntry = TradeEntry.create(
        symbol: _symbol,
        entryPrice: _entryPrice!,
        positionSize: _positionSize!,
        direction: _direction,
        patternId: _patternId,
        riskRewardRatio: _riskRewardRatio,
        stopLoss: _stopLoss,
        takeProfit: _takeProfit,
        notes: _notes,
        tags: _selectedTagIds,
        sentimentBefore: _sentimentBefore,
        timeframe: _timeframe,
      );
      
      await provider.addTradeEntry(newEntry);
      
      // Add images
      for (final imageFile in _imageFiles) {
        await provider.addTradeImage(
          tradeId: newEntry.id,
          imageFile: imageFile,
        );
      }
    }
    
    if (context.mounted) {
      Navigator.pop(context);
    }
  }
}
