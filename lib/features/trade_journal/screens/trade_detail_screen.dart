import 'dart:io';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/providers/trade_journal_provider.dart';
import 'package:learn_chart_patterns/features/trade_journal/screens/trade_entry_form.dart';
import 'package:learn_chart_patterns/features/trade_journal/widgets/close_trade_dialog.dart';
import 'package:provider/provider.dart';

/// Screen for viewing the details of a trade entry
class TradeDetailScreen extends StatelessWidget {
  /// The trade entry to display
  final TradeEntry entry;

  /// Creates a new trade detail screen
  const TradeDetailScreen({
    super.key,
    required this.entry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Trade Details',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Edit button
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editTrade(context),
          ),
          
          // More options button
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _deleteTrade(context);
              } else if (value == 'close') {
                _closeTrade(context);
              }
            },
            itemBuilder: (context) => [
              if (entry.outcome == TradeOutcome.open)
                const PopupMenuItem<String>(
                  value: 'close',
                  child: Row(
                    children: [
                      Icon(Icons.check_circle),
                      SizedBox(width: AppConstants.spacingSmall),
                      Text('Close Trade'),
                    ],
                  ),
                ),
              const PopupMenuItem<String>(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete),
                    SizedBox(width: AppConstants.spacingSmall),
                    Text('Delete'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: provider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(context),
    );
  }

  /// Builds the body of the screen
  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trade summary card
          _buildSummaryCard(context),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Trade details card
          _buildDetailsCard(context),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Trade notes card
          if (entry.notes != null && entry.notes!.isNotEmpty)
            _buildNotesCard(context),
          
          if (entry.notes != null && entry.notes!.isNotEmpty)
            const SizedBox(height: AppConstants.spacingMedium),
          
          // Trade images
          if (entry.imagePaths.isNotEmpty)
            _buildImagesCard(context),
        ],
      ),
    );
  }

  /// Builds the summary card
  Widget _buildSummaryCard(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Symbol and direction
            Row(
              children: [
                _buildDirectionIndicator(context),
                const SizedBox(width: AppConstants.spacingSmall),
                Text(
                  entry.symbol,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildOutcomeChip(context),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Entry and exit dates
            Row(
              children: [
                // Entry date
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Entry Date',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(150),
                        ),
                      ),
                      Text(
                        _formatDateTime(entry.entryDate),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Exit date
                if (entry.exitDate != null)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Exit Date',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(150),
                          ),
                        ),
                        Text(
                          _formatDateTime(entry.exitDate!),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Entry and exit prices
            Row(
              children: [
                // Entry price
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Entry Price',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(150),
                        ),
                      ),
                      Text(
                        entry.entryPrice.toString(),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Exit price
                if (entry.exitPrice != null)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Exit Price',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(150),
                          ),
                        ),
                        Text(
                          entry.exitPrice.toString(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Position size and profit/loss
            Row(
              children: [
                // Position size
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Position Size',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(150),
                        ),
                      ),
                      Text(
                        entry.positionSize.toString(),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Profit/loss
                if (entry.profitLoss != null)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Profit/Loss',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(150),
                          ),
                        ),
                        Text(
                          _formatProfitLoss(entry.profitLoss!),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _getProfitLossColor(entry.profitLoss!, theme),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            
            // Tags
            if (provider.getTagsForEntry(entry).isNotEmpty) ...[
              const SizedBox(height: AppConstants.spacingMedium),
              Text(
                'Tags',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(150),
                ),
              ),
              const SizedBox(height: AppConstants.spacingSmall),
              Wrap(
                spacing: AppConstants.spacingSmall,
                runSpacing: AppConstants.spacingSmall,
                children: provider.getTagsForEntry(entry).map((tag) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Color(tag.color.value).withAlpha(50),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                    child: Text(
                      tag.name,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: tag.color,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds the details card
  Widget _buildDetailsCard(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trade Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Risk-reward ratio
            if (entry.riskRewardRatio != null)
              _buildDetailRow(
                context,
                'Risk-Reward Ratio',
                '1:${entry.riskRewardRatio!.toStringAsFixed(1)}',
              ),
            
            // Stop loss
            if (entry.stopLoss != null)
              _buildDetailRow(
                context,
                'Stop Loss',
                entry.stopLoss!.toString(),
              ),
            
            // Take profit
            if (entry.takeProfit != null)
              _buildDetailRow(
                context,
                'Take Profit',
                entry.takeProfit!.toString(),
              ),
            
            // Pattern
            if (entry.patternId != null)
              _buildDetailRow(
                context,
                'Pattern',
                entry.patternId!,
              ),
            
            // Timeframe
            if (entry.timeframe != null)
              _buildDetailRow(
                context,
                'Timeframe',
                entry.timeframe!,
              ),
            
            // Commission
            if (entry.commission != null)
              _buildDetailRow(
                context,
                'Commission',
                entry.commission!.toString(),
              ),
            
            // Sentiment before
            if (entry.sentimentBefore != null)
              _buildDetailRow(
                context,
                'Sentiment Before',
                '${entry.sentimentBefore!}/10',
              ),
            
            // Sentiment after
            if (entry.sentimentAfter != null)
              _buildDetailRow(
                context,
                'Sentiment After',
                '${entry.sentimentAfter!}/10',
              ),
            
            // Followed trading plan
            if (entry.followedTradingPlan != null)
              _buildDetailRow(
                context,
                'Followed Trading Plan',
                entry.followedTradingPlan! ? 'Yes' : 'No',
              ),
          ],
        ),
      ),
    );
  }

  /// Builds the notes card
  Widget _buildNotesCard(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            Text(
              entry.notes!,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the images card
  Widget _buildImagesCard(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Images',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: AppConstants.spacingSmall,
                mainAxisSpacing: AppConstants.spacingSmall,
                childAspectRatio: 1,
              ),
              itemCount: entry.imagePaths.length,
              itemBuilder: (context, index) {
                final imagePath = entry.imagePaths[index];
                
                return GestureDetector(
                  onTap: () => _viewImage(context, imagePath),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    child: Image.file(
                      File(imagePath),
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a detail row
  Widget _buildDetailRow(BuildContext context, String label, String value) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(180),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the direction indicator
  Widget _buildDirectionIndicator(BuildContext context) {
    final color = entry.direction == TradeDirection.long
        ? Colors.green
        : Colors.red;
    
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        shape: BoxShape.circle,
      ),
      child: Icon(
        entry.direction == TradeDirection.long
            ? Icons.arrow_upward
            : Icons.arrow_downward,
        size: 16,
        color: color,
      ),
    );
  }

  /// Builds the outcome chip
  Widget _buildOutcomeChip(BuildContext context) {
    final theme = Theme.of(context);
    
    Color color;
    String text;
    IconData icon;
    
    switch (entry.outcome) {
      case TradeOutcome.profit:
        color = Colors.green;
        text = 'Profit';
        icon = Icons.check_circle;
        break;
      case TradeOutcome.loss:
        color = Colors.red;
        text = 'Loss';
        icon = Icons.cancel;
        break;
      case TradeOutcome.breakeven:
        color = Colors.grey;
        text = 'Breakeven';
        icon = Icons.horizontal_rule;
        break;
      case TradeOutcome.open:
        color = Colors.orange;
        text = 'Open';
        icon = Icons.pending;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Formats a date and time
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('MMM d, yyyy - h:mm a').format(dateTime);
  }

  /// Formats a profit/loss value
  String _formatProfitLoss(double value) {
    final sign = value >= 0 ? '+' : '';
    return '$sign${value.toStringAsFixed(2)}';
  }

  /// Gets the color for a profit/loss value
  Color _getProfitLossColor(double value, ThemeData theme) {
    if (value > 0) {
      return Colors.green;
    } else if (value < 0) {
      return Colors.red;
    } else {
      return theme.colorScheme.onSurface;
    }
  }

  /// Edits the trade
  void _editTrade(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TradeEntryForm(entry: entry),
      ),
    );
  }

  /// Deletes the trade
  void _deleteTrade(BuildContext context) {
    final provider = Provider.of<TradeJournalProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Trade'),
        content: const Text('Are you sure you want to delete this trade? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await provider.deleteTradeEntry(entry.id);
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Closes the trade
  void _closeTrade(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CloseTradeDialog(entry: entry),
    );
  }

  /// Views an image
  void _viewImage(BuildContext context, String imagePath) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: Container(
            color: Colors.black,
            child: Center(
              child: InteractiveViewer(
                child: Image.file(File(imagePath)),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
