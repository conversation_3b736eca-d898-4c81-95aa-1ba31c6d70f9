import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/providers/trade_journal_provider.dart';
import 'package:learn_chart_patterns/features/trade_journal/screens/trade_detail_screen.dart';
import 'package:learn_chart_patterns/features/trade_journal/screens/trade_entry_form.dart';
import 'package:learn_chart_patterns/features/trade_journal/widgets/journal_filter_dialog.dart';
import 'package:learn_chart_patterns/features/trade_journal/widgets/trade_entry_card.dart';
import 'package:learn_chart_patterns/features/trade_journal/widgets/trade_statistics_widget.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';
import 'package:learn_chart_patterns/widgets/premium_feature_dialog.dart';
import 'package:provider/provider.dart';

/// The main screen for the trade journal feature
class JournalScreen extends StatefulWidget {
  /// Creates a new journal screen
  const JournalScreen({super.key});

  @override
  State<JournalScreen> createState() => _JournalScreenState();
}

class _JournalScreenState extends State<JournalScreen> {
  /// Whether the statistics are expanded
  bool _statisticsExpanded = false;

  @override
  void initState() {
    super.initState();
    // Initialize the trade journal provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<TradeJournalProvider>(context, listen: false);
      provider.init();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    final purchaseService = Provider.of<PurchaseService>(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Trade Journal',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Filter button
          Container(
            margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.filter_list),
              color: theme.colorScheme.primary,
              onPressed: _showFilterDialog,
            ),
          ),
        ],
      ),
      body: provider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(context),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addNewTrade(context),
        backgroundColor: theme.colorScheme.primary,
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Builds the body of the screen
  Widget _buildBody(BuildContext context) {
    final provider = Provider.of<TradeJournalProvider>(context);
    final entries = provider.entries;

    return Column(
      children: [
        // Statistics card
        _buildStatisticsCard(context),

        // Filter chips
        if (provider.filters.isNotEmpty) _buildFilterChips(context),

        // Trade entries
        Expanded(
          child: entries.isEmpty
              ? _buildEmptyState(context)
              : _buildTradeList(context, entries),
        ),
      ],
    );
  }

  /// Builds the statistics card
  Widget _buildStatisticsCard(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Column(
        children: [
          // Header
          ListTile(
            title: Text(
              'Trade Statistics',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            trailing: IconButton(
              icon: Icon(
                _statisticsExpanded ? Icons.expand_less : Icons.expand_more,
                color: theme.colorScheme.primary,
              ),
              onPressed: () {
                setState(() {
                  _statisticsExpanded = !_statisticsExpanded;
                });
              },
            ),
          ),

          // Statistics
          if (_statisticsExpanded)
            const Padding(
              padding: EdgeInsets.all(AppConstants.paddingMedium),
              child: TradeStatisticsWidget(),
            ),
        ],
      ),
    );
  }

  /// Builds the filter chips
  Widget _buildFilterChips(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    final filters = provider.filters;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: Wrap(
        spacing: AppConstants.spacingSmall,
        runSpacing: AppConstants.spacingSmall,
        children: [
          // Date range filter
          if (filters['startDate'] != null || filters['endDate'] != null)
            Chip(
              label: Text(
                'Date: ${_formatDateRange(filters['startDate'], filters['endDate'])}',
                style: theme.textTheme.bodySmall,
              ),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () {
                provider.setFilters(
                  startDate: null,
                  endDate: null,
                  symbol: filters['symbol'],
                  direction: filters['direction'],
                  outcome: filters['outcome'],
                  patternId: filters['patternId'],
                  tagIds: filters['tagIds'],
                );
              },
            ),

          // Symbol filter
          if (filters['symbol'] != null)
            Chip(
              label: Text(
                'Symbol: ${filters['symbol']}',
                style: theme.textTheme.bodySmall,
              ),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () {
                provider.setFilters(
                  startDate: filters['startDate'],
                  endDate: filters['endDate'],
                  symbol: null,
                  direction: filters['direction'],
                  outcome: filters['outcome'],
                  patternId: filters['patternId'],
                  tagIds: filters['tagIds'],
                );
              },
            ),

          // Direction filter
          if (filters['direction'] != null)
            Chip(
              label: Text(
                'Direction: ${filters['direction'].name.toUpperCase()}',
                style: theme.textTheme.bodySmall,
              ),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () {
                provider.setFilters(
                  startDate: filters['startDate'],
                  endDate: filters['endDate'],
                  symbol: filters['symbol'],
                  direction: null,
                  outcome: filters['outcome'],
                  patternId: filters['patternId'],
                  tagIds: filters['tagIds'],
                );
              },
            ),

          // Outcome filter
          if (filters['outcome'] != null)
            Chip(
              label: Text(
                'Outcome: ${filters['outcome'].name.toUpperCase()}',
                style: theme.textTheme.bodySmall,
              ),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () {
                provider.setFilters(
                  startDate: filters['startDate'],
                  endDate: filters['endDate'],
                  symbol: filters['symbol'],
                  direction: filters['direction'],
                  outcome: null,
                  patternId: filters['patternId'],
                  tagIds: filters['tagIds'],
                );
              },
            ),

          // Pattern filter
          if (filters['patternId'] != null)
            Chip(
              label: Text(
                'Pattern: ${filters['patternId']}',
                style: theme.textTheme.bodySmall,
              ),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () {
                provider.setFilters(
                  startDate: filters['startDate'],
                  endDate: filters['endDate'],
                  symbol: filters['symbol'],
                  direction: filters['direction'],
                  outcome: filters['outcome'],
                  patternId: null,
                  tagIds: filters['tagIds'],
                );
              },
            ),

          // Clear all filters button
          if (filters.isNotEmpty)
            ActionChip(
              label: Text(
                'Clear All',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
              avatar: Icon(
                Icons.filter_alt_off,
                size: 16,
                color: theme.colorScheme.primary,
              ),
              onPressed: () {
                provider.clearFilters();
              },
            ),
        ],
      ),
    );
  }

  /// Builds the trade list
  Widget _buildTradeList(BuildContext context, List<TradeEntry> entries) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      itemCount: entries.length,
      itemBuilder: (context, index) {
        final entry = entries[index];
        return TradeEntryCard(
          entry: entry,
          onTap: () => _viewTradeDetails(context, entry),
        );
      },
    );
  }

  /// Builds the empty state
  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.note_alt_outlined,
            size: 64,
            color: theme.colorScheme.primary.withAlpha(150),
          ),
          const SizedBox(height: AppConstants.spacingMedium),
          Text(
            provider.filters.isEmpty
                ? 'No trades yet'
                : 'No trades match your filters',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.spacingSmall),
          Text(
            provider.filters.isEmpty
                ? 'Start by adding your first trade'
                : 'Try adjusting your filters',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(150),
            ),
          ),
          const SizedBox(height: AppConstants.spacingLarge),
          if (provider.filters.isNotEmpty)
            ElevatedButton.icon(
              onPressed: () {
                provider.clearFilters();
              },
              icon: const Icon(Icons.filter_alt_off),
              label: const Text('Clear Filters'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingLarge,
                  vertical: AppConstants.paddingMedium,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Shows the filter dialog
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => const JournalFilterDialog(),
    );
  }

  /// Adds a new trade
  void _addNewTrade(BuildContext context) {
    final purchaseService = Provider.of<PurchaseService>(context, listen: false);

    // Check if the user has premium access
    if (!purchaseService.hasPremium) {
      // Show premium feature dialog
      showDialog(
        context: context,
        builder: (context) => const PremiumFeatureDialog(
          title: 'Premium Feature',
          message: 'The trade journal is a premium feature. Upgrade to premium to access it.',
        ),
      );
      return;
    }

    // Navigate to the trade entry form
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TradeEntryForm(),
      ),
    );
  }

  /// Views the details of a trade
  void _viewTradeDetails(BuildContext context, TradeEntry entry) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TradeDetailScreen(entry: entry),
      ),
    );
  }

  /// Formats a date range
  String _formatDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate != null && endDate != null) {
      return '${_formatDate(startDate)} - ${_formatDate(endDate)}';
    } else if (startDate != null) {
      return 'From ${_formatDate(startDate)}';
    } else if (endDate != null) {
      return 'Until ${_formatDate(endDate)}';
    } else {
      return 'All time';
    }
  }

  /// Formats a date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
