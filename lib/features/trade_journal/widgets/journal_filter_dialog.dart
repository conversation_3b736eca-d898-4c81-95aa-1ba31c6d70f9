import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/providers/trade_journal_provider.dart';
import 'package:provider/provider.dart';

/// Dialog for filtering trade entries
class JournalFilterDialog extends StatefulWidget {
  /// Creates a new journal filter dialog
  const JournalFilterDialog({super.key});

  @override
  State<JournalFilterDialog> createState() => _JournalFilterDialogState();
}

class _JournalFilterDialogState extends State<JournalFilterDialog> {
  /// Start date for filtering
  DateTime? _startDate;

  /// End date for filtering
  DateTime? _endDate;

  /// Symbol for filtering
  String? _symbol;

  /// Direction for filtering
  TradeDirection? _direction;

  /// Outcome for filtering
  TradeOutcome? _outcome;

  /// Pattern ID for filtering
  String? _patternId;

  /// Tag IDs for filtering
  List<String> _tagIds = [];

  /// Text editing controller for the symbol field
  final TextEditingController _symbolController = TextEditingController();

  @override
  void initState() {
    super.initState();
    
    // Initialize filter values from provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<TradeJournalProvider>(context, listen: false);
      final filters = provider.filters;
      
      setState(() {
        _startDate = filters['startDate'];
        _endDate = filters['endDate'];
        _symbol = filters['symbol'];
        _direction = filters['direction'];
        _outcome = filters['outcome'];
        _patternId = filters['patternId'];
        _tagIds = filters['tagIds'] ?? [];
        
        if (_symbol != null) {
          _symbolController.text = _symbol!;
        }
      });
    });
  }

  @override
  void dispose() {
    _symbolController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: Text(
        'Filter Trades',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date range
            Text(
              'Date Range',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.spacingSmall),
            Row(
              children: [
                // Start date
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, true),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'From',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.paddingSmall,
                          vertical: AppConstants.paddingSmall,
                        ),
                      ),
                      child: Text(
                        _startDate != null
                            ? '${_startDate!.day}/${_startDate!.month}/${_startDate!.year}'
                            : 'Any',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: AppConstants.spacingSmall),
                
                // End date
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, false),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'To',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.paddingSmall,
                          vertical: AppConstants.paddingSmall,
                        ),
                      ),
                      child: Text(
                        _endDate != null
                            ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                            : 'Any',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Symbol
            Text(
              'Symbol',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.spacingSmall),
            TextField(
              controller: _symbolController,
              decoration: InputDecoration(
                hintText: 'Enter symbol (e.g., EUR/USD)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _symbol = value.isEmpty ? null : value;
                });
              },
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Direction
            Text(
              'Direction',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.spacingSmall),
            Row(
              children: [
                // Any direction
                _buildDirectionChip(context, null, 'Any'),
                
                const SizedBox(width: AppConstants.spacingSmall),
                
                // Long direction
                _buildDirectionChip(context, TradeDirection.long, 'Long'),
                
                const SizedBox(width: AppConstants.spacingSmall),
                
                // Short direction
                _buildDirectionChip(context, TradeDirection.short, 'Short'),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Outcome
            Text(
              'Outcome',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.spacingSmall),
            Wrap(
              spacing: AppConstants.spacingSmall,
              runSpacing: AppConstants.spacingSmall,
              children: [
                // Any outcome
                _buildOutcomeChip(context, null, 'Any'),
                
                // Profit outcome
                _buildOutcomeChip(context, TradeOutcome.profit, 'Profit'),
                
                // Loss outcome
                _buildOutcomeChip(context, TradeOutcome.loss, 'Loss'),
                
                // Breakeven outcome
                _buildOutcomeChip(context, TradeOutcome.breakeven, 'Breakeven'),
                
                // Open outcome
                _buildOutcomeChip(context, TradeOutcome.open, 'Open'),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Tags
            Text(
              'Tags',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.spacingSmall),
            _buildTagSelector(context),
          ],
        ),
      ),
      actions: [
        // Clear button
        TextButton(
          onPressed: () {
            setState(() {
              _startDate = null;
              _endDate = null;
              _symbol = null;
              _symbolController.clear();
              _direction = null;
              _outcome = null;
              _patternId = null;
              _tagIds = [];
            });
          },
          child: const Text('Clear'),
        ),
        
        // Cancel button
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('Cancel'),
        ),
        
        // Apply button
        ElevatedButton(
          onPressed: () {
            final provider = Provider.of<TradeJournalProvider>(context, listen: false);
            
            provider.setFilters(
              startDate: _startDate,
              endDate: _endDate,
              symbol: _symbol,
              direction: _direction,
              outcome: _outcome,
              patternId: _patternId,
              tagIds: _tagIds.isEmpty ? null : _tagIds,
            );
            
            Navigator.pop(context);
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }

  /// Builds a direction chip
  Widget _buildDirectionChip(
    BuildContext context,
    TradeDirection? direction,
    String label,
  ) {
    final theme = Theme.of(context);
    final isSelected = _direction == direction;
    
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _direction = selected ? direction : null;
        });
      },
      backgroundColor: theme.colorScheme.surface,
      selectedColor: theme.colorScheme.primary.withAlpha(50),
      labelStyle: theme.textTheme.bodyMedium?.copyWith(
        color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
      ),
    );
  }

  /// Builds an outcome chip
  Widget _buildOutcomeChip(
    BuildContext context,
    TradeOutcome? outcome,
    String label,
  ) {
    final theme = Theme.of(context);
    final isSelected = _outcome == outcome;
    
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _outcome = selected ? outcome : null;
        });
      },
      backgroundColor: theme.colorScheme.surface,
      selectedColor: theme.colorScheme.primary.withAlpha(50),
      labelStyle: theme.textTheme.bodyMedium?.copyWith(
        color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
      ),
    );
  }

  /// Builds the tag selector
  Widget _buildTagSelector(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    final tags = provider.tags;
    
    return Wrap(
      spacing: AppConstants.spacingSmall,
      runSpacing: AppConstants.spacingSmall,
      children: tags.map((tag) {
        final isSelected = _tagIds.contains(tag.id);
        
        return FilterChip(
          label: Text(tag.name),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _tagIds.add(tag.id);
              } else {
                _tagIds.remove(tag.id);
              }
            });
          },
          backgroundColor: Color(tag.color.value).withAlpha(30),
          selectedColor: Color(tag.color.value).withAlpha(100),
          checkmarkColor: tag.color,
          labelStyle: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected ? tag.color : theme.colorScheme.onSurface,
          ),
        );
      }).toList(),
    );
  }

  /// Selects a date
  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final initialDate = isStartDate ? _startDate : _endDate;
    final now = DateTime.now();
    
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? now,
      firstDate: DateTime(2000),
      lastDate: now,
    );
    
    if (pickedDate != null) {
      setState(() {
        if (isStartDate) {
          _startDate = pickedDate;
          
          // Ensure end date is not before start date
          if (_endDate != null && _endDate!.isBefore(_startDate!)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = pickedDate;
          
          // Ensure start date is not after end date
          if (_startDate != null && _startDate!.isAfter(_endDate!)) {
            _startDate = _endDate;
          }
        }
      });
    }
  }
}
