import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/providers/trade_journal_provider.dart';
import 'package:provider/provider.dart';

/// Dialog for closing a trade
class CloseTradeDialog extends StatefulWidget {
  /// The trade entry to close
  final TradeEntry entry;

  /// Creates a new close trade dialog
  const CloseTradeDialog({
    super.key,
    required this.entry,
  });

  @override
  State<CloseTradeDialog> createState() => _CloseTradeDialogState();
}

class _CloseTradeDialogState extends State<CloseTradeDialog> {
  /// The exit price
  double? _exitPrice;

  /// The exit date
  DateTime _exitDate = DateTime.now();

  /// The sentiment after the trade
  int? _sentimentAfter;

  /// Whether the trade followed the trading plan
  bool? _followedTradingPlan;

  /// The commission or fees paid for this trade
  double? _commission;

  /// Text editing controller for the exit price field
  final TextEditingController _exitPriceController = TextEditingController();

  /// Text editing controller for the commission field
  final TextEditingController _commissionController = TextEditingController();

  /// Form key
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _exitPriceController.dispose();
    _commissionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: Text(
        'Close Trade',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Exit price
              TextFormField(
                controller: _exitPriceController,
                decoration: const InputDecoration(
                  labelText: 'Exit Price *',
                  hintText: 'Enter the exit price',
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter the exit price';
                  }
                  
                  final price = double.tryParse(value);
                  if (price == null || price <= 0) {
                    return 'Please enter a valid price';
                  }
                  
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    _exitPrice = double.tryParse(value);
                  });
                },
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              // Exit date
              InkWell(
                onTap: () => _selectDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Exit Date *',
                    hintText: 'Select the exit date',
                  ),
                  child: Text(
                    '${_exitDate.day}/${_exitDate.month}/${_exitDate.year}',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              // Sentiment after
              Text(
                'Sentiment After (1-10)',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: AppConstants.spacingSmall),
              Slider(
                value: _sentimentAfter?.toDouble() ?? 5,
                min: 1,
                max: 10,
                divisions: 9,
                label: _sentimentAfter?.toString() ?? '5',
                onChanged: (value) {
                  setState(() {
                    _sentimentAfter = value.round();
                  });
                },
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              // Followed trading plan
              Text(
                'Followed Trading Plan',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: AppConstants.spacingSmall),
              Row(
                children: [
                  // Yes button
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('Yes'),
                      value: true,
                      groupValue: _followedTradingPlan,
                      onChanged: (value) {
                        setState(() {
                          _followedTradingPlan = value;
                        });
                      },
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  
                  // No button
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('No'),
                      value: false,
                      groupValue: _followedTradingPlan,
                      onChanged: (value) {
                        setState(() {
                          _followedTradingPlan = value;
                        });
                      },
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              // Commission
              TextFormField(
                controller: _commissionController,
                decoration: const InputDecoration(
                  labelText: 'Commission/Fees',
                  hintText: 'Enter the commission or fees paid',
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final commission = double.tryParse(value);
                    if (commission == null || commission < 0) {
                      return 'Please enter a valid commission';
                    }
                  }
                  
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    _commission = double.tryParse(value);
                  });
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        // Cancel button
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('Cancel'),
        ),
        
        // Close button
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              _closeTrade(context);
            }
          },
          child: const Text('Close Trade'),
        ),
      ],
    );
  }

  /// Selects a date
  Future<void> _selectDate(BuildContext context) async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _exitDate,
      firstDate: widget.entry.entryDate,
      lastDate: DateTime.now(),
    );
    
    if (pickedDate != null) {
      setState(() {
        _exitDate = pickedDate;
      });
    }
  }

  /// Closes the trade
  Future<void> _closeTrade(BuildContext context) async {
    final provider = Provider.of<TradeJournalProvider>(context, listen: false);
    
    await provider.closeTradePosition(
      id: widget.entry.id,
      exitPrice: _exitPrice!,
      exitDate: _exitDate,
      sentimentAfter: _sentimentAfter,
      followedTradingPlan: _followedTradingPlan,
      commission: _commission,
    );
    
    if (context.mounted) {
      Navigator.pop(context);
    }
  }
}
