import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/providers/trade_journal_provider.dart';
import 'package:provider/provider.dart';

/// Card for displaying a trade entry in the list
class TradeEntryCard extends StatelessWidget {
  /// The trade entry to display
  final TradeEntry entry;

  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Creates a new trade entry card
  const TradeEntryCard({
    super.key,
    required this.entry,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with symbol and date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Symbol and direction
                  Row(
                    children: [
                      _buildDirectionIndicator(context),
                      const SizedBox(width: AppConstants.spacingSmall),
                      Text(
                        entry.symbol,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  // Date
                  Text(
                    _formatDate(entry.entryDate),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(150),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.spacingSmall),
              
              // Entry and exit prices
              Row(
                children: [
                  // Entry price
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Entry',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(150),
                        ),
                      ),
                      Text(
                        entry.entryPrice.toString(),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(width: AppConstants.spacingMedium),
                  
                  // Exit price
                  if (entry.exitPrice != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Exit',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(150),
                          ),
                        ),
                        Text(
                          entry.exitPrice.toString(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  
                  const Spacer(),
                  
                  // Profit/loss
                  if (entry.profitLoss != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'P/L',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(150),
                          ),
                        ),
                        Text(
                          _formatProfitLoss(entry.profitLoss!),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _getProfitLossColor(entry.profitLoss!, theme),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              
              const SizedBox(height: AppConstants.spacingSmall),
              
              // Tags and outcome
              Row(
                children: [
                  // Tags
                  Expanded(
                    child: Wrap(
                      spacing: AppConstants.spacingSmall,
                      runSpacing: AppConstants.spacingSmall,
                      children: provider.getTagsForEntry(entry).map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.paddingSmall,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Color(tag.color.value).withAlpha(50),
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                          ),
                          child: Text(
                            tag.name,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: tag.color,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  
                  // Outcome
                  _buildOutcomeChip(context),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the direction indicator
  Widget _buildDirectionIndicator(BuildContext context) {
    final color = entry.direction == TradeDirection.long
        ? Colors.green
        : Colors.red;
    
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        shape: BoxShape.circle,
      ),
      child: Icon(
        entry.direction == TradeDirection.long
            ? Icons.arrow_upward
            : Icons.arrow_downward,
        size: 16,
        color: color,
      ),
    );
  }

  /// Builds the outcome chip
  Widget _buildOutcomeChip(BuildContext context) {
    final theme = Theme.of(context);
    
    Color color;
    String text;
    IconData icon;
    
    switch (entry.outcome) {
      case TradeOutcome.profit:
        color = Colors.green;
        text = 'Profit';
        icon = Icons.check_circle;
        break;
      case TradeOutcome.loss:
        color = Colors.red;
        text = 'Loss';
        icon = Icons.cancel;
        break;
      case TradeOutcome.breakeven:
        color = Colors.grey;
        text = 'Breakeven';
        icon = Icons.horizontal_rule;
        break;
      case TradeOutcome.open:
        color = Colors.orange;
        text = 'Open';
        icon = Icons.pending;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Formats a date
  String _formatDate(DateTime date) {
    return DateFormat('MMM d, yyyy').format(date);
  }

  /// Formats a profit/loss value
  String _formatProfitLoss(double value) {
    final sign = value >= 0 ? '+' : '';
    return '$sign${value.toStringAsFixed(2)}';
  }

  /// Gets the color for a profit/loss value
  Color _getProfitLossColor(double value, ThemeData theme) {
    if (value > 0) {
      return Colors.green;
    } else if (value < 0) {
      return Colors.red;
    } else {
      return theme.colorScheme.onSurface;
    }
  }
}
