import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/providers/trade_journal_provider.dart';
import 'package:provider/provider.dart';

/// Widget for displaying trade statistics
class TradeStatisticsWidget extends StatelessWidget {
  /// Creates a new trade statistics widget
  const TradeStatisticsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TradeJournalProvider>(context);
    final statistics = provider.statistics;
    
    return Column(
      children: [
        // Summary row
        Row(
          children: [
            _buildStatCard(
              context,
              'Total Trades',
              '${statistics['totalTrades'] ?? 0}',
              Colors.blue,
              Icons.analytics,
            ),
            const SizedBox(width: AppConstants.spacingSmall),
            _buildStatCard(
              context,
              'Win Rate',
              '${(statistics['winRate'] ?? 0).toStringAsFixed(1)}%',
              Colors.green,
              Icons.trending_up,
            ),
          ],
        ),
        
        const SizedBox(height: AppConstants.spacingSmall),
        
        // Trades by outcome
        Row(
          children: [
            _buildStatCard(
              context,
              'Winning',
              '${statistics['winningTrades'] ?? 0}',
              Colors.green,
              Icons.check_circle,
            ),
            const SizedBox(width: AppConstants.spacingSmall),
            _buildStatCard(
              context,
              'Losing',
              '${statistics['losingTrades'] ?? 0}',
              Colors.red,
              Icons.cancel,
            ),
          ],
        ),
        
        const SizedBox(height: AppConstants.spacingSmall),
        
        // More stats
        Row(
          children: [
            _buildStatCard(
              context,
              'Open',
              '${statistics['openTrades'] ?? 0}',
              Colors.orange,
              Icons.pending,
            ),
            const SizedBox(width: AppConstants.spacingSmall),
            _buildStatCard(
              context,
              'Breakeven',
              '${statistics['breakevenTrades'] ?? 0}',
              Colors.grey,
              Icons.horizontal_rule,
            ),
          ],
        ),
        
        const SizedBox(height: AppConstants.spacingSmall),
        
        // Profit/loss
        Row(
          children: [
            _buildStatCard(
              context,
              'Total P/L',
              _formatProfitLoss(statistics['totalProfitLoss'] ?? 0),
              _getProfitLossColor(statistics['totalProfitLoss'] ?? 0, theme),
              Icons.account_balance_wallet,
            ),
            const SizedBox(width: AppConstants.spacingSmall),
            _buildStatCard(
              context,
              'Avg P/L',
              _formatProfitLoss(statistics['avgProfitLoss'] ?? 0),
              _getProfitLossColor(statistics['avgProfitLoss'] ?? 0, theme),
              Icons.show_chart,
            ),
          ],
        ),
        
        // Premium features teaser
        if (!provider.hasPremium)
          Padding(
            padding: const EdgeInsets.only(top: AppConstants.paddingMedium),
            child: Card(
              color: theme.colorScheme.primary.withAlpha(30),
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: AppConstants.spacingSmall),
                    Expanded(
                      child: Text(
                        'Upgrade to Premium for advanced statistics and analytics',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // TODO: Navigate to premium upgrade screen
                      },
                      child: const Text('Upgrade'),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Builds a stat card
  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Expanded(
      child: Card(
        elevation: 0,
        color: color.withAlpha(20),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    size: 16,
                    color: color,
                  ),
                  const SizedBox(width: AppConstants.spacingSmall),
                  Text(
                    label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(180),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.spacingSmall),
              Text(
                value,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Formats a profit/loss value
  String _formatProfitLoss(double value) {
    final sign = value >= 0 ? '+' : '';
    return '$sign${value.toStringAsFixed(2)}';
  }

  /// Gets the color for a profit/loss value
  Color _getProfitLossColor(double value, ThemeData theme) {
    if (value > 0) {
      return Colors.green;
    } else if (value < 0) {
      return Colors.red;
    } else {
      return theme.colorScheme.onSurface;
    }
  }
}
