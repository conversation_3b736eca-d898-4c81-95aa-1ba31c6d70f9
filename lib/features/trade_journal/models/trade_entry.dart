import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Enum representing the direction of a trade
enum TradeDirection {
  /// Long position (buy)
  long,

  /// Short position (sell)
  short,
}

/// Enum representing the outcome of a trade
enum TradeOutcome {
  /// Profitable trade
  profit,

  /// Loss-making trade
  loss,

  /// Breakeven trade
  breakeven,

  /// Trade not yet closed
  open,
}

/// Model representing a trade journal entry
class TradeEntry extends Equatable {
  /// Unique identifier for the trade
  final String id;

  /// Date and time when the trade was opened
  final DateTime entryDate;

  /// Date and time when the trade was closed (null if still open)
  final DateTime? exitDate;

  /// The trading pair or symbol (e.g., "EUR/USD", "BTC/USDT")
  final String symbol;

  /// The entry price
  final double entryPrice;

  /// The exit price (null if still open)
  final double? exitPrice;

  /// The position size
  final double positionSize;

  /// The direction of the trade (long or short)
  final TradeDirection direction;

  /// The chart pattern identified for this trade
  final String? patternId;

  /// The risk-reward ratio planned for this trade
  final double? riskRewardRatio;

  /// The stop loss price
  final double? stopLoss;

  /// The take profit price
  final double? takeProfit;

  /// The outcome of the trade
  final TradeOutcome outcome;

  /// The profit or loss amount
  final double? profitLoss;

  /// The profit or loss percentage
  final double? profitLossPercentage;

  /// Notes about the trade
  final String? notes;

  /// Tags associated with this trade
  final List<String> tags;

  /// Sentiment before entering the trade (1-10)
  final int? sentimentBefore;

  /// Sentiment after closing the trade (1-10)
  final int? sentimentAfter;

  /// List of image paths associated with this trade
  final List<String> imagePaths;

  /// Whether this trade was executed according to the trading plan
  final bool? followedTradingPlan;

  /// The timeframe used for this trade
  final String? timeframe;

  /// The commission or fees paid for this trade
  final double? commission;

  /// Creates a new trade entry
  const TradeEntry({
    required this.id,
    required this.entryDate,
    this.exitDate,
    required this.symbol,
    required this.entryPrice,
    this.exitPrice,
    required this.positionSize,
    required this.direction,
    this.patternId,
    this.riskRewardRatio,
    this.stopLoss,
    this.takeProfit,
    required this.outcome,
    this.profitLoss,
    this.profitLossPercentage,
    this.notes,
    required this.tags,
    this.sentimentBefore,
    this.sentimentAfter,
    required this.imagePaths,
    this.followedTradingPlan,
    this.timeframe,
    this.commission,
  });

  /// Creates a new trade entry with default values
  factory TradeEntry.create({
    required String symbol,
    required double entryPrice,
    required double positionSize,
    required TradeDirection direction,
    String? patternId,
    double? riskRewardRatio,
    double? stopLoss,
    double? takeProfit,
    String? notes,
    List<String>? tags,
    int? sentimentBefore,
    List<String>? imagePaths,
    String? timeframe,
  }) {
    return TradeEntry(
      id: const Uuid().v4(),
      entryDate: DateTime.now(),
      symbol: symbol,
      entryPrice: entryPrice,
      positionSize: positionSize,
      direction: direction,
      patternId: patternId,
      riskRewardRatio: riskRewardRatio,
      stopLoss: stopLoss,
      takeProfit: takeProfit,
      outcome: TradeOutcome.open,
      notes: notes,
      tags: tags ?? [],
      sentimentBefore: sentimentBefore,
      imagePaths: imagePaths ?? [],
      timeframe: timeframe,
    );
  }

  /// Creates a copy of this trade entry with the given fields replaced with new values
  TradeEntry copyWith({
    String? id,
    DateTime? entryDate,
    DateTime? exitDate,
    bool clearExitDate = false,
    String? symbol,
    double? entryPrice,
    double? exitPrice,
    bool clearExitPrice = false,
    double? positionSize,
    TradeDirection? direction,
    String? patternId,
    bool clearPatternId = false,
    double? riskRewardRatio,
    bool clearRiskRewardRatio = false,
    double? stopLoss,
    bool clearStopLoss = false,
    double? takeProfit,
    bool clearTakeProfit = false,
    TradeOutcome? outcome,
    double? profitLoss,
    bool clearProfitLoss = false,
    double? profitLossPercentage,
    bool clearProfitLossPercentage = false,
    String? notes,
    bool clearNotes = false,
    List<String>? tags,
    int? sentimentBefore,
    bool clearSentimentBefore = false,
    int? sentimentAfter,
    bool clearSentimentAfter = false,
    List<String>? imagePaths,
    bool? followedTradingPlan,
    bool clearFollowedTradingPlan = false,
    String? timeframe,
    bool clearTimeframe = false,
    double? commission,
    bool clearCommission = false,
  }) {
    return TradeEntry(
      id: id ?? this.id,
      entryDate: entryDate ?? this.entryDate,
      exitDate: clearExitDate ? null : exitDate ?? this.exitDate,
      symbol: symbol ?? this.symbol,
      entryPrice: entryPrice ?? this.entryPrice,
      exitPrice: clearExitPrice ? null : exitPrice ?? this.exitPrice,
      positionSize: positionSize ?? this.positionSize,
      direction: direction ?? this.direction,
      patternId: clearPatternId ? null : patternId ?? this.patternId,
      riskRewardRatio: clearRiskRewardRatio ? null : riskRewardRatio ?? this.riskRewardRatio,
      stopLoss: clearStopLoss ? null : stopLoss ?? this.stopLoss,
      takeProfit: clearTakeProfit ? null : takeProfit ?? this.takeProfit,
      outcome: outcome ?? this.outcome,
      profitLoss: clearProfitLoss ? null : profitLoss ?? this.profitLoss,
      profitLossPercentage: clearProfitLossPercentage ? null : profitLossPercentage ?? this.profitLossPercentage,
      notes: clearNotes ? null : notes ?? this.notes,
      tags: tags ?? this.tags,
      sentimentBefore: clearSentimentBefore ? null : sentimentBefore ?? this.sentimentBefore,
      sentimentAfter: clearSentimentAfter ? null : sentimentAfter ?? this.sentimentAfter,
      imagePaths: imagePaths ?? this.imagePaths,
      followedTradingPlan: clearFollowedTradingPlan ? null : followedTradingPlan ?? this.followedTradingPlan,
      timeframe: clearTimeframe ? null : timeframe ?? this.timeframe,
      commission: clearCommission ? null : commission ?? this.commission,
    );
  }

  /// Closes the trade with the given exit price and date
  TradeEntry closePosition({
    required double exitPrice,
    required DateTime exitDate,
    TradeOutcome? outcome,
    double? profitLoss,
    double? profitLossPercentage,
    int? sentimentAfter,
    bool? followedTradingPlan,
    double? commission,
  }) {
    // Calculate profit/loss if not provided
    final calculatedProfitLoss = profitLoss ?? _calculateProfitLoss(exitPrice);
    final calculatedProfitLossPercentage = profitLossPercentage ?? _calculateProfitLossPercentage(exitPrice);
    
    // Determine outcome if not provided
    final calculatedOutcome = outcome ?? _determineOutcome(calculatedProfitLoss);

    return copyWith(
      exitPrice: exitPrice,
      exitDate: exitDate,
      outcome: calculatedOutcome,
      profitLoss: calculatedProfitLoss,
      profitLossPercentage: calculatedProfitLossPercentage,
      sentimentAfter: sentimentAfter,
      followedTradingPlan: followedTradingPlan,
      commission: commission,
    );
  }

  /// Calculates the profit or loss amount
  double _calculateProfitLoss(double exitPrice) {
    final priceDifference = direction == TradeDirection.long
        ? exitPrice - entryPrice
        : entryPrice - exitPrice;
    return priceDifference * positionSize;
  }

  /// Calculates the profit or loss percentage
  double _calculateProfitLossPercentage(double exitPrice) {
    final priceDifference = direction == TradeDirection.long
        ? exitPrice - entryPrice
        : entryPrice - exitPrice;
    return (priceDifference / entryPrice) * 100;
  }

  /// Determines the outcome of the trade based on profit/loss
  TradeOutcome _determineOutcome(double profitLoss) {
    if (profitLoss > 0) {
      return TradeOutcome.profit;
    } else if (profitLoss < 0) {
      return TradeOutcome.loss;
    } else {
      return TradeOutcome.breakeven;
    }
  }

  /// Converts the trade entry to a map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entryDate': entryDate.toIso8601String(),
      'exitDate': exitDate?.toIso8601String(),
      'symbol': symbol,
      'entryPrice': entryPrice,
      'exitPrice': exitPrice,
      'positionSize': positionSize,
      'direction': direction.name,
      'patternId': patternId,
      'riskRewardRatio': riskRewardRatio,
      'stopLoss': stopLoss,
      'takeProfit': takeProfit,
      'outcome': outcome.name,
      'profitLoss': profitLoss,
      'profitLossPercentage': profitLossPercentage,
      'notes': notes,
      'tags': tags.join(','),
      'sentimentBefore': sentimentBefore,
      'sentimentAfter': sentimentAfter,
      'imagePaths': imagePaths.join(','),
      'followedTradingPlan': followedTradingPlan,
      'timeframe': timeframe,
      'commission': commission,
    };
  }

  /// Creates a trade entry from a map
  factory TradeEntry.fromMap(Map<String, dynamic> map) {
    return TradeEntry(
      id: map['id'],
      entryDate: DateTime.parse(map['entryDate']),
      exitDate: map['exitDate'] != null ? DateTime.parse(map['exitDate']) : null,
      symbol: map['symbol'],
      entryPrice: map['entryPrice'],
      exitPrice: map['exitPrice'],
      positionSize: map['positionSize'],
      direction: TradeDirection.values.firstWhere(
        (e) => e.name == map['direction'],
        orElse: () => TradeDirection.long,
      ),
      patternId: map['patternId'],
      riskRewardRatio: map['riskRewardRatio'],
      stopLoss: map['stopLoss'],
      takeProfit: map['takeProfit'],
      outcome: TradeOutcome.values.firstWhere(
        (e) => e.name == map['outcome'],
        orElse: () => TradeOutcome.open,
      ),
      profitLoss: map['profitLoss'],
      profitLossPercentage: map['profitLossPercentage'],
      notes: map['notes'],
      tags: map['tags'] != null && map['tags'].isNotEmpty
          ? map['tags'].split(',')
          : [],
      sentimentBefore: map['sentimentBefore'],
      sentimentAfter: map['sentimentAfter'],
      imagePaths: map['imagePaths'] != null && map['imagePaths'].isNotEmpty
          ? map['imagePaths'].split(',')
          : [],
      followedTradingPlan: map['followedTradingPlan'],
      timeframe: map['timeframe'],
      commission: map['commission'],
    );
  }

  @override
  List<Object?> get props => [
        id,
        entryDate,
        exitDate,
        symbol,
        entryPrice,
        exitPrice,
        positionSize,
        direction,
        patternId,
        riskRewardRatio,
        stopLoss,
        takeProfit,
        outcome,
        profitLoss,
        profitLossPercentage,
        notes,
        tags,
        sentimentBefore,
        sentimentAfter,
        imagePaths,
        followedTradingPlan,
        timeframe,
        commission,
      ];
}
