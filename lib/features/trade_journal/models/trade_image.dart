import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// Model representing an image associated with a trade
class TradeImage extends Equatable {
  /// Unique identifier for the image
  final String id;

  /// ID of the trade this image is associated with
  final String tradeId;

  /// Path to the image file
  final String imagePath;

  /// Description of the image
  final String? description;

  /// Date and time when the image was added
  final DateTime createdAt;

  /// Creates a new trade image
  const TradeImage({
    required this.id,
    required this.tradeId,
    required this.imagePath,
    this.description,
    required this.createdAt,
  });

  /// Creates a new trade image with a generated ID
  factory TradeImage.create({
    required String tradeId,
    required String imagePath,
    String? description,
  }) {
    return TradeImage(
      id: const Uuid().v4(),
      tradeId: tradeId,
      imagePath: imagePath,
      description: description,
      createdAt: DateTime.now(),
    );
  }

  /// Creates a copy of this trade image with the given fields replaced with new values
  TradeImage copyWith({
    String? id,
    String? tradeId,
    String? imagePath,
    String? description,
    bool clearDescription = false,
    DateTime? createdAt,
  }) {
    return TradeImage(
      id: id ?? this.id,
      tradeId: tradeId ?? this.tradeId,
      imagePath: imagePath ?? this.imagePath,
      description: clearDescription ? null : description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Converts the trade image to a map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tradeId': tradeId,
      'imagePath': imagePath,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Creates a trade image from a map
  factory TradeImage.fromMap(Map<String, dynamic> map) {
    return TradeImage(
      id: map['id'],
      tradeId: map['tradeId'],
      imagePath: map['imagePath'],
      description: map['description'],
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  @override
  List<Object?> get props => [id, tradeId, imagePath, description, createdAt];
}
