import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// Model representing a tag for categorizing trades
class TradeTag extends Equatable {
  /// Unique identifier for the tag
  final String id;

  /// Name of the tag
  final String name;

  /// Color of the tag
  final Color color;

  /// Creates a new trade tag
  const TradeTag({
    required this.id,
    required this.name,
    required this.color,
  });

  /// Creates a new trade tag with a generated ID
  factory TradeTag.create({
    required String name,
    required Color color,
  }) {
    return TradeTag(
      id: const Uuid().v4(),
      name: name,
      color: color,
    );
  }

  /// Creates a copy of this trade tag with the given fields replaced with new values
  TradeTag copyWith({
    String? id,
    String? name,
    Color? color,
  }) {
    return TradeTag(
      id: id ?? this.id,
      name: name ?? this.name,
      color: color ?? this.color,
    );
  }

  /// Converts the trade tag to a map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'color': color.value,
    };
  }

  /// Creates a trade tag from a map
  factory TradeTag.fromMap(Map<String, dynamic> map) {
    return TradeTag(
      id: map['id'],
      name: map['name'],
      color: Color(map['color']),
    );
  }

  @override
  List<Object?> get props => [id, name, color];
}
