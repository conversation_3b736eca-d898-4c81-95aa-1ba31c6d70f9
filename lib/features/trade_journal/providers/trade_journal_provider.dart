import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_tag.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_image.dart';
import 'package:learn_chart_patterns/features/trade_journal/services/database_service.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';

/// Provider for the trade journal feature
class TradeJournalProvider extends ChangeNotifier {
  /// Database service
  final DatabaseService _databaseService;

  /// Purchase service
  final PurchaseService _purchaseService;

  /// List of trade entries
  List<TradeEntry> _entries = [];

  /// List of trade tags
  List<TradeTag> _tags = [];

  /// Trade statistics
  Map<String, dynamic> _statistics = {};

  /// Current filter settings
  Map<String, dynamic> _filters = {};

  /// Whether the provider is loading data
  bool _isLoading = false;

  /// Whether the provider has initialized
  bool _isInitialized = false;

  /// Creates a new trade journal provider
  TradeJournalProvider(this._databaseService, this._purchaseService);

  /// Gets the list of trade entries
  List<TradeEntry> get entries => _entries;

  /// Gets the list of trade tags
  List<TradeTag> get tags => _tags;

  /// Gets the trade statistics
  Map<String, dynamic> get statistics => _statistics;

  /// Gets the current filter settings
  Map<String, dynamic> get filters => _filters;

  /// Gets whether the provider is loading data
  bool get isLoading => _isLoading;

  /// Gets whether the user has premium access
  bool get hasPremium => _purchaseService.hasPremium;

  /// Initializes the provider
  Future<void> init() async {
    if (_isInitialized) return;

    _setLoading(true);

    try {
      await _databaseService.init();
      await _loadTags();
      await _loadEntries();
      await _loadStatistics();

      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing trade journal provider: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Sets the loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Loads trade entries from the database
  Future<void> _loadEntries() async {
    try {
      if (_filters.isEmpty) {
        _entries = await _databaseService.getAllTradeEntries();
      } else {
        _entries = await _databaseService.getFilteredTradeEntries(
          startDate: _filters['startDate'],
          endDate: _filters['endDate'],
          symbol: _filters['symbol'],
          direction: _filters['direction'],
          outcome: _filters['outcome'],
          patternId: _filters['patternId'],
          tagIds: _filters['tagIds'],
        );
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading trade entries: $e');
    }
  }

  /// Loads trade tags from the database
  Future<void> _loadTags() async {
    try {
      _tags = await _databaseService.getAllTradeTags();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading trade tags: $e');
    }
  }

  /// Loads trade statistics from the database
  Future<void> _loadStatistics() async {
    try {
      _statistics = await _databaseService.getTradeStatistics();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading trade statistics: $e');
    }
  }

  /// Sets filters for trade entries
  Future<void> setFilters({
    DateTime? startDate,
    DateTime? endDate,
    String? symbol,
    TradeDirection? direction,
    TradeOutcome? outcome,
    String? patternId,
    List<String>? tagIds,
  }) async {
    _filters = {
      'startDate': startDate,
      'endDate': endDate,
      'symbol': symbol,
      'direction': direction,
      'outcome': outcome,
      'patternId': patternId,
      'tagIds': tagIds,
    };

    await _loadEntries();
  }

  /// Clears all filters
  Future<void> clearFilters() async {
    _filters = {};
    await _loadEntries();
  }

  /// Adds a new trade entry
  Future<void> addTradeEntry(TradeEntry entry) async {
    _setLoading(true);

    try {
      await _databaseService.insertTradeEntry(entry);
      await _loadEntries();
      await _loadStatistics();
    } catch (e) {
      debugPrint('Error adding trade entry: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Updates an existing trade entry
  Future<void> updateTradeEntry(TradeEntry entry) async {
    _setLoading(true);

    try {
      await _databaseService.updateTradeEntry(entry);
      await _loadEntries();
      await _loadStatistics();
    } catch (e) {
      debugPrint('Error updating trade entry: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Deletes a trade entry
  Future<void> deleteTradeEntry(String id) async {
    _setLoading(true);

    try {
      await _databaseService.deleteTradeEntry(id);
      await _loadEntries();
      await _loadStatistics();
    } catch (e) {
      debugPrint('Error deleting trade entry: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Closes a trade position
  Future<void> closeTradePosition({
    required String id,
    required double exitPrice,
    required DateTime exitDate,
    TradeOutcome? outcome,
    double? profitLoss,
    double? profitLossPercentage,
    int? sentimentAfter,
    bool? followedTradingPlan,
    double? commission,
  }) async {
    _setLoading(true);

    try {
      final entry = await _databaseService.getTradeEntry(id);

      if (entry != null) {
        final updatedEntry = entry.closePosition(
          exitPrice: exitPrice,
          exitDate: exitDate,
          outcome: outcome,
          profitLoss: profitLoss,
          profitLossPercentage: profitLossPercentage,
          sentimentAfter: sentimentAfter,
          followedTradingPlan: followedTradingPlan,
          commission: commission,
        );

        await _databaseService.updateTradeEntry(updatedEntry);
        await _loadEntries();
        await _loadStatistics();
      }
    } catch (e) {
      debugPrint('Error closing trade position: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Adds a new trade tag
  Future<void> addTradeTag(TradeTag tag) async {
    _setLoading(true);

    try {
      await _databaseService.insertTradeTag(tag);
      await _loadTags();
    } catch (e) {
      debugPrint('Error adding trade tag: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Updates an existing trade tag
  Future<void> updateTradeTag(TradeTag tag) async {
    _setLoading(true);

    try {
      await _databaseService.updateTradeTag(tag);
      await _loadTags();
    } catch (e) {
      debugPrint('Error updating trade tag: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Deletes a trade tag
  Future<void> deleteTradeTag(String id) async {
    _setLoading(true);

    try {
      await _databaseService.deleteTradeTag(id);
      await _loadTags();
    } catch (e) {
      debugPrint('Error deleting trade tag: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Adds an image to a trade
  Future<String?> addTradeImage({
    required String tradeId,
    required XFile imageFile,
    String? description,
  }) async {
    _setLoading(true);

    try {
      // Get the application documents directory
      final Directory documentsDirectory = await getApplicationDocumentsDirectory();
      final String imagesPath = path.join(documentsDirectory.path, 'trade_images');

      // Create the directory if it doesn't exist
      final Directory imagesDirectory = Directory(imagesPath);
      if (!await imagesDirectory.exists()) {
        await imagesDirectory.create(recursive: true);
      }

      // Generate a unique filename
      final String fileName = '${DateTime.now().millisecondsSinceEpoch}_${path.basename(imageFile.path)}';
      final String filePath = path.join(imagesPath, fileName);

      // Copy the image file to the app's documents directory
      final File newFile = File(filePath);
      await newFile.writeAsBytes(await imageFile.readAsBytes());

      // Create and save the trade image
      final tradeImage = TradeImage.create(
        tradeId: tradeId,
        imagePath: filePath,
        description: description,
      );

      final imageId = await _databaseService.insertTradeImage(tradeImage);

      // Update the trade entry with the new image path
      final entry = await _databaseService.getTradeEntry(tradeId);
      if (entry != null) {
        final imagePaths = List<String>.from(entry.imagePaths);
        imagePaths.add(filePath);

        final updatedEntry = entry.copyWith(imagePaths: imagePaths);
        await _databaseService.updateTradeEntry(updatedEntry);
      }

      await _loadEntries();

      return imageId;
    } catch (e) {
      debugPrint('Error adding trade image: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Deletes a trade image
  Future<bool> deleteTradeImage(String imageId, String tradeId) async {
    _setLoading(true);

    try {
      // Get the trade image
      final image = await _databaseService.getTradeImage(imageId);
      if (image == null) return false;

      // Delete the image file
      final file = File(image.imagePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Delete the trade image from the database
      await _databaseService.deleteTradeImage(imageId);

      // Update the trade entry
      final entry = await _databaseService.getTradeEntry(tradeId);
      if (entry != null) {
        final imagePaths = List<String>.from(entry.imagePaths);
        imagePaths.remove(image.imagePath);

        final updatedEntry = entry.copyWith(imagePaths: imagePaths);
        await _databaseService.updateTradeEntry(updatedEntry);
      }

      await _loadEntries();

      return true;
    } catch (e) {
      debugPrint('Error deleting trade image: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Gets a tag by ID
  TradeTag? getTagById(String id) {
    try {
      return _tags.firstWhere((tag) => tag.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Gets tags for a trade entry
  List<TradeTag> getTagsForEntry(TradeEntry entry) {
    return entry.tags
        .map((tagId) => getTagById(tagId))
        .where((tag) => tag != null)
        .cast<TradeTag>()
        .toList();
  }

  /// Refreshes all data
  Future<void> refreshData() async {
    _setLoading(true);

    try {
      await _loadTags();
      await _loadEntries();
      await _loadStatistics();
    } catch (e) {
      debugPrint('Error refreshing data: $e');
    } finally {
      _setLoading(false);
    }
  }
}
