import 'dart:async';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_tag.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_image.dart';

/// Service for managing the trade journal database
class DatabaseService {
  /// Singleton instance
  static final DatabaseService _instance = DatabaseService._internal();

  /// Factory constructor
  factory DatabaseService() => _instance;

  /// Internal constructor
  DatabaseService._internal();

  /// Database instance
  Database? _database;

  /// Database name
  static const String _databaseName = 'trade_journal.db';

  /// Database version
  static const int _databaseVersion = 1;

  /// Table names
  static const String tableTradeEntries = 'trade_entries';
  static const String tableTradeTags = 'trade_tags';
  static const String tableTradeImages = 'trade_images';

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// Gets the database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Initializes the database service
  Future<void> init() async {
    if (_isInitialized) return;
    await database;
    _isInitialized = true;
  }

  /// Initializes the database
  Future<Database> _initDatabase() async {
    // Get the application documents directory
    final Directory documentsDirectory = await getApplicationDocumentsDirectory();
    final String dbPath = path.join(documentsDirectory.path, _databaseName);

    // Open the database
    return await openDatabase(
      dbPath,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// Creates the database tables
  Future<void> _onCreate(Database db, int version) async {
    // Create trade entries table
    await db.execute('''
      CREATE TABLE $tableTradeEntries (
        id TEXT PRIMARY KEY,
        entryDate TEXT NOT NULL,
        exitDate TEXT,
        symbol TEXT NOT NULL,
        entryPrice REAL NOT NULL,
        exitPrice REAL,
        positionSize REAL NOT NULL,
        direction TEXT NOT NULL,
        patternId TEXT,
        riskRewardRatio REAL,
        stopLoss REAL,
        takeProfit REAL,
        outcome TEXT NOT NULL,
        profitLoss REAL,
        profitLossPercentage REAL,
        notes TEXT,
        tags TEXT,
        sentimentBefore INTEGER,
        sentimentAfter INTEGER,
        imagePaths TEXT,
        followedTradingPlan INTEGER,
        timeframe TEXT,
        commission REAL
      )
    ''');

    // Create trade tags table
    await db.execute('''
      CREATE TABLE $tableTradeTags (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        color INTEGER NOT NULL
      )
    ''');

    // Create trade images table
    await db.execute('''
      CREATE TABLE $tableTradeImages (
        id TEXT PRIMARY KEY,
        tradeId TEXT NOT NULL,
        imagePath TEXT NOT NULL,
        description TEXT,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (tradeId) REFERENCES $tableTradeEntries (id) ON DELETE CASCADE
      )
    ''');

    // Create default tags
    await _createDefaultTags(db);
  }

  /// Creates default tags
  Future<void> _createDefaultTags(Database db) async {
    final defaultTags = [
      {'id': '1', 'name': 'Bullish', 'color': 0xFF4CAF50},
      {'id': '2', 'name': 'Bearish', 'color': 0xFFF44336},
      {'id': '3', 'name': 'Breakout', 'color': 0xFF2196F3},
      {'id': '4', 'name': 'Reversal', 'color': 0xFFFF9800},
      {'id': '5', 'name': 'Trend', 'color': 0xFF9C27B0},
    ];

    final batch = db.batch();
    for (final tag in defaultTags) {
      batch.insert(tableTradeTags, tag);
    }
    await batch.commit();
  }

  /// Upgrades the database
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
  }

  // CRUD operations for TradeEntry

  /// Inserts a new trade entry
  Future<String> insertTradeEntry(TradeEntry entry) async {
    final db = await database;
    await db.insert(
      tableTradeEntries,
      entry.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return entry.id;
  }

  /// Updates an existing trade entry
  Future<int> updateTradeEntry(TradeEntry entry) async {
    final db = await database;
    return await db.update(
      tableTradeEntries,
      entry.toMap(),
      where: 'id = ?',
      whereArgs: [entry.id],
    );
  }

  /// Deletes a trade entry
  Future<int> deleteTradeEntry(String id) async {
    final db = await database;
    return await db.delete(
      tableTradeEntries,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Gets a trade entry by ID
  Future<TradeEntry?> getTradeEntry(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableTradeEntries,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return TradeEntry.fromMap(maps.first);
  }

  /// Gets all trade entries
  Future<List<TradeEntry>> getAllTradeEntries() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableTradeEntries,
      orderBy: 'entryDate DESC',
    );

    return List.generate(maps.length, (i) {
      return TradeEntry.fromMap(maps[i]);
    });
  }

  /// Gets trade entries with filters
  Future<List<TradeEntry>> getFilteredTradeEntries({
    DateTime? startDate,
    DateTime? endDate,
    String? symbol,
    TradeDirection? direction,
    TradeOutcome? outcome,
    String? patternId,
    List<String>? tagIds,
  }) async {
    final db = await database;

    final List<String> whereConditions = [];
    final List<dynamic> whereArgs = [];

    if (startDate != null) {
      whereConditions.add('entryDate >= ?');
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      whereConditions.add('entryDate <= ?');
      whereArgs.add(endDate.toIso8601String());
    }

    if (symbol != null) {
      whereConditions.add('symbol = ?');
      whereArgs.add(symbol);
    }

    if (direction != null) {
      whereConditions.add('direction = ?');
      whereArgs.add(direction.name);
    }

    if (outcome != null) {
      whereConditions.add('outcome = ?');
      whereArgs.add(outcome.name);
    }

    if (patternId != null) {
      whereConditions.add('patternId = ?');
      whereArgs.add(patternId);
    }

    // Handle tag filtering
    if (tagIds != null && tagIds.isNotEmpty) {
      final List<String> tagConditions = [];
      for (final tagId in tagIds) {
        tagConditions.add('tags LIKE ?');
        whereArgs.add('%$tagId%');
      }
      whereConditions.add('(${tagConditions.join(' OR ')})');
    }

    String whereClause = '';
    if (whereConditions.isNotEmpty) {
      whereClause = 'WHERE ${whereConditions.join(' AND ')}';
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT * FROM $tableTradeEntries $whereClause ORDER BY entryDate DESC',
      whereArgs,
    );

    return List.generate(maps.length, (i) {
      return TradeEntry.fromMap(maps[i]);
    });
  }

  // CRUD operations for TradeTag

  /// Inserts a new trade tag
  Future<String> insertTradeTag(TradeTag tag) async {
    final db = await database;
    await db.insert(
      tableTradeTags,
      tag.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return tag.id;
  }

  /// Updates an existing trade tag
  Future<int> updateTradeTag(TradeTag tag) async {
    final db = await database;
    return await db.update(
      tableTradeTags,
      tag.toMap(),
      where: 'id = ?',
      whereArgs: [tag.id],
    );
  }

  /// Deletes a trade tag
  Future<int> deleteTradeTag(String id) async {
    final db = await database;
    return await db.delete(
      tableTradeTags,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Gets a trade tag by ID
  Future<TradeTag?> getTradeTag(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableTradeTags,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return TradeTag.fromMap(maps.first);
  }

  /// Gets all trade tags
  Future<List<TradeTag>> getAllTradeTags() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableTradeTags,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return TradeTag.fromMap(maps[i]);
    });
  }

  // CRUD operations for TradeImage

  /// Inserts a new trade image
  Future<String> insertTradeImage(TradeImage image) async {
    final db = await database;
    await db.insert(
      tableTradeImages,
      image.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return image.id;
  }

  /// Updates an existing trade image
  Future<int> updateTradeImage(TradeImage image) async {
    final db = await database;
    return await db.update(
      tableTradeImages,
      image.toMap(),
      where: 'id = ?',
      whereArgs: [image.id],
    );
  }

  /// Deletes a trade image
  Future<int> deleteTradeImage(String id) async {
    final db = await database;
    return await db.delete(
      tableTradeImages,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Gets a trade image by ID
  Future<TradeImage?> getTradeImage(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableTradeImages,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;
    return TradeImage.fromMap(maps.first);
  }

  /// Gets all trade images for a trade
  Future<List<TradeImage>> getTradeImages(String tradeId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableTradeImages,
      where: 'tradeId = ?',
      whereArgs: [tradeId],
      orderBy: 'createdAt DESC',
    );

    return List.generate(maps.length, (i) {
      return TradeImage.fromMap(maps[i]);
    });
  }

  // Statistics methods

  /// Gets trade statistics
  Future<Map<String, dynamic>> getTradeStatistics() async {
    final db = await database;

    // Total trades
    final totalTradesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableTradeEntries',
    );
    final totalTrades = Sqflite.firstIntValue(totalTradesResult) ?? 0;

    // Winning trades
    final winningTradesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableTradeEntries WHERE outcome = ?',
      [TradeOutcome.profit.name],
    );
    final winningTrades = Sqflite.firstIntValue(winningTradesResult) ?? 0;

    // Losing trades
    final losingTradesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableTradeEntries WHERE outcome = ?',
      [TradeOutcome.loss.name],
    );
    final losingTrades = Sqflite.firstIntValue(losingTradesResult) ?? 0;

    // Breakeven trades
    final breakevenTradesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableTradeEntries WHERE outcome = ?',
      [TradeOutcome.breakeven.name],
    );
    final breakevenTrades = Sqflite.firstIntValue(breakevenTradesResult) ?? 0;

    // Open trades
    final openTradesResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableTradeEntries WHERE outcome = ?',
      [TradeOutcome.open.name],
    );
    final openTrades = Sqflite.firstIntValue(openTradesResult) ?? 0;

    // Win rate
    final closedTrades = winningTrades + losingTrades + breakevenTrades;
    final winRate = closedTrades > 0 ? (winningTrades / closedTrades) * 100 : 0.0;

    // Total profit/loss
    final profitLossResult = await db.rawQuery(
      'SELECT SUM(profitLoss) as total FROM $tableTradeEntries WHERE outcome != ?',
      [TradeOutcome.open.name],
    );
    final totalProfitLoss = profitLossResult.first['total'] as double? ?? 0.0;

    // Average profit/loss
    final avgProfitLoss = closedTrades > 0 ? totalProfitLoss / closedTrades : 0.0;

    return {
      'totalTrades': totalTrades,
      'winningTrades': winningTrades,
      'losingTrades': losingTrades,
      'breakevenTrades': breakevenTrades,
      'openTrades': openTrades,
      'winRate': winRate,
      'totalProfitLoss': totalProfitLoss,
      'avgProfitLoss': avgProfitLoss,
    };
  }
}
