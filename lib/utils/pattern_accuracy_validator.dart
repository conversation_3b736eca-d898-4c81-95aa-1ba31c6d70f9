import 'dart:math';

import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';

/// Utility class for validating pattern accuracy and technical correctness
class PatternAccuracyValidator {
  /// Validates if a pattern meets technical analysis standards
  static PatternValidationResult validatePattern(
    ChartPattern pattern,
    List<CandlestickData> candlesticks,
  ) {
    switch (pattern.id) {
      case 'head_and_shoulders':
        return _validateHeadAndShoulders(candlesticks);
      case 'inverse_head_and_shoulders':
        return _validateInverseHeadAndShoulders(candlesticks);
      case 'double_top':
        return _validateDoubleTop(candlesticks);
      case 'double_bottom':
        return _validateDoubleBottom(candlesticks);
      case 'ascending_triangle':
        return _validateAscendingTriangle(candlesticks);
      case 'descending_triangle':
        return _validateDescendingTriangle(candlesticks);
      case 'cup_and_handle':
        return _validateCupAndHandle(candlesticks);
      default:
        return PatternValidationResult(
          isValid: true,
          score: 0.8,
          issues: ['Pattern validation not implemented'],
        );
    }
  }

  /// Validates Head and Shoulders pattern
  static PatternValidationResult _validateHeadAndShoulders(
    List<CandlestickData> candlesticks,
  ) {
    final issues = <String>[];
    double score = 1.0;

    // Find peaks and troughs
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);

    // Should have 3 peaks (left shoulder, head, right shoulder)
    if (peaks.length < 3) {
      issues.add('Insufficient peaks for head and shoulders pattern');
      score -= 0.3;
    } else {
      // Check if head is higher than shoulders
      final leftShoulder = peaks[0];
      final head = peaks[1];
      final rightShoulder = peaks[2];

      if (head.high <= leftShoulder.high || head.high <= rightShoulder.high) {
        issues.add('Head should be higher than both shoulders');
        score -= 0.2;
      }

      // Check if shoulders are roughly equal height (within 5%)
      final shoulderDifference = (leftShoulder.high - rightShoulder.high).abs() / leftShoulder.high;
      if (shoulderDifference > 0.05) {
        issues.add('Shoulders should be roughly equal in height');
        score -= 0.1;
      }
    }

    // Should have 2 troughs for neckline
    if (troughs.length < 2) {
      issues.add('Insufficient troughs for neckline formation');
      score -= 0.2;
    } else {
      // Check if neckline troughs are roughly equal (within 3%)
      final necklineDifference = (troughs[0].low - troughs[1].low).abs() / troughs[0].low;
      if (necklineDifference > 0.03) {
        issues.add('Neckline should be relatively horizontal');
        score -= 0.1;
      }
    }

    return PatternValidationResult(
      isValid: score >= 0.6,
      score: score,
      issues: issues,
    );
  }

  /// Validates Inverse Head and Shoulders pattern
  static PatternValidationResult _validateInverseHeadAndShoulders(
    List<CandlestickData> candlesticks,
  ) {
    final issues = <String>[];
    double score = 1.0;

    // Find peaks and troughs
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);

    // Should have 3 troughs (left shoulder, head, right shoulder)
    if (troughs.length < 3) {
      issues.add('Insufficient troughs for inverse head and shoulders pattern');
      score -= 0.3;
    } else {
      // Check if head is lower than shoulders
      final leftShoulder = troughs[0];
      final head = troughs[1];
      final rightShoulder = troughs[2];

      if (head.low >= leftShoulder.low || head.low >= rightShoulder.low) {
        issues.add('Head should be lower than both shoulders');
        score -= 0.2;
      }

      // Check if shoulders are roughly equal depth (within 5%)
      final shoulderDifference = (leftShoulder.low - rightShoulder.low).abs() / leftShoulder.low;
      if (shoulderDifference > 0.05) {
        issues.add('Shoulders should be roughly equal in depth');
        score -= 0.1;
      }
    }

    // Should have 2 peaks for neckline
    if (peaks.length < 2) {
      issues.add('Insufficient peaks for neckline formation');
      score -= 0.2;
    }

    return PatternValidationResult(
      isValid: score >= 0.6,
      score: score,
      issues: issues,
    );
  }

  /// Validates Double Top pattern
  static PatternValidationResult _validateDoubleTop(
    List<CandlestickData> candlesticks,
  ) {
    final issues = <String>[];
    double score = 1.0;

    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);

    // Should have 2 peaks
    if (peaks.length < 2) {
      issues.add('Insufficient peaks for double top pattern');
      score -= 0.4;
    } else {
      // Check if peaks are roughly equal (within 3%)
      final peakDifference = (peaks[0].high - peaks[1].high).abs() / peaks[0].high;
      if (peakDifference > 0.03) {
        issues.add('Peaks should be roughly equal in height');
        score -= 0.2;
      }
    }

    // Should have 1 trough between peaks
    if (troughs.isEmpty) {
      issues.add('Missing trough between peaks');
      score -= 0.3;
    }

    return PatternValidationResult(
      isValid: score >= 0.6,
      score: score,
      issues: issues,
    );
  }

  /// Validates Double Bottom pattern
  static PatternValidationResult _validateDoubleBottom(
    List<CandlestickData> candlesticks,
  ) {
    final issues = <String>[];
    double score = 1.0;

    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);

    // Should have 2 troughs
    if (troughs.length < 2) {
      issues.add('Insufficient troughs for double bottom pattern');
      score -= 0.4;
    } else {
      // Check if troughs are roughly equal (within 3%)
      final troughDifference = (troughs[0].low - troughs[1].low).abs() / troughs[0].low;
      if (troughDifference > 0.03) {
        issues.add('Troughs should be roughly equal in depth');
        score -= 0.2;
      }
    }

    // Should have 1 peak between troughs
    if (peaks.isEmpty) {
      issues.add('Missing peak between troughs');
      score -= 0.3;
    }

    return PatternValidationResult(
      isValid: score >= 0.6,
      score: score,
      issues: issues,
    );
  }

  /// Validates Ascending Triangle pattern
  static PatternValidationResult _validateAscendingTriangle(
    List<CandlestickData> candlesticks,
  ) {
    final issues = <String>[];
    double score = 1.0;

    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);

    // Should have at least 2 peaks for horizontal resistance
    if (peaks.length < 2) {
      issues.add('Insufficient peaks for resistance line');
      score -= 0.3;
    } else {
      // Check if resistance line is relatively horizontal (within 2%)
      final resistanceDifference = (peaks.first.high - peaks.last.high).abs() / peaks.first.high;
      if (resistanceDifference > 0.02) {
        issues.add('Resistance line should be relatively horizontal');
        score -= 0.2;
      }
    }

    // Should have at least 2 troughs for ascending support
    if (troughs.length < 2) {
      issues.add('Insufficient troughs for ascending support line');
      score -= 0.3;
    } else {
      // Check if support line is ascending
      if (troughs.last.low <= troughs.first.low) {
        issues.add('Support line should be ascending (higher lows)');
        score -= 0.2;
      }
    }

    return PatternValidationResult(
      isValid: score >= 0.6,
      score: score,
      issues: issues,
    );
  }

  /// Validates Descending Triangle pattern
  static PatternValidationResult _validateDescendingTriangle(
    List<CandlestickData> candlesticks,
  ) {
    final issues = <String>[];
    double score = 1.0;

    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);

    // Should have at least 2 troughs for horizontal support
    if (troughs.length < 2) {
      issues.add('Insufficient troughs for support line');
      score -= 0.3;
    } else {
      // Check if support line is relatively horizontal (within 2%)
      final supportDifference = (troughs.first.low - troughs.last.low).abs() / troughs.first.low;
      if (supportDifference > 0.02) {
        issues.add('Support line should be relatively horizontal');
        score -= 0.2;
      }
    }

    // Should have at least 2 peaks for descending resistance
    if (peaks.length < 2) {
      issues.add('Insufficient peaks for descending resistance line');
      score -= 0.3;
    } else {
      // Check if resistance line is descending
      if (peaks.last.high >= peaks.first.high) {
        issues.add('Resistance line should be descending (lower highs)');
        score -= 0.2;
      }
    }

    return PatternValidationResult(
      isValid: score >= 0.6,
      score: score,
      issues: issues,
    );
  }

  /// Validates Cup and Handle pattern
  static PatternValidationResult _validateCupAndHandle(
    List<CandlestickData> candlesticks,
  ) {
    final issues = <String>[];
    double score = 1.0;

    // Find the overall high and low points
    final highestPoint = candlesticks.fold(
      double.negativeInfinity,
      (highest, candle) => max(highest, candle.high),
    );
    final lowestPoint = candlesticks.fold(
      double.infinity,
      (lowest, candle) => min(lowest, candle.low),
    );

    // Cup should be U-shaped (gradual decline and recovery)
    final cupDepth = (highestPoint - lowestPoint) / highestPoint;
    if (cupDepth < 0.12 || cupDepth > 0.33) {
      issues.add('Cup depth should be between 12% and 33%');
      score -= 0.2;
    }

    // Handle should be a smaller pullback
    final handleDepth = cupDepth * 0.5; // Handle should be about 50% of cup depth
    if (handleDepth < 0.05) {
      issues.add('Handle pullback too shallow');
      score -= 0.1;
    }

    return PatternValidationResult(
      isValid: score >= 0.6,
      score: score,
      issues: issues,
    );
  }

  /// Finds peaks in the candlestick data
  static List<CandlestickData> _findPeaks(List<CandlestickData> candlesticks) {
    final peaks = <CandlestickData>[];
    
    for (int i = 1; i < candlesticks.length - 1; i++) {
      if (candlesticks[i].high > candlesticks[i - 1].high &&
          candlesticks[i].high > candlesticks[i + 1].high) {
        peaks.add(candlesticks[i]);
      }
    }
    
    return peaks;
  }

  /// Finds troughs in the candlestick data
  static List<CandlestickData> _findTroughs(List<CandlestickData> candlesticks) {
    final troughs = <CandlestickData>[];
    
    for (int i = 1; i < candlesticks.length - 1; i++) {
      if (candlesticks[i].low < candlesticks[i - 1].low &&
          candlesticks[i].low < candlesticks[i + 1].low) {
        troughs.add(candlesticks[i]);
      }
    }
    
    return troughs;
  }
}

/// Result of pattern validation
class PatternValidationResult {
  /// Whether the pattern is technically valid
  final bool isValid;
  
  /// Accuracy score (0.0 to 1.0)
  final double score;
  
  /// List of issues found
  final List<String> issues;

  /// Creates a new pattern validation result
  const PatternValidationResult({
    required this.isValid,
    required this.score,
    required this.issues,
  });
}
