import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';

/// App theme provider
class AppTheme {
  /// Creates light theme
  static ThemeData lightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4A6FFF), // Modern blue
        brightness: Brightness.light,
        primary: const Color(0xFF4A6FFF), // Modern blue
        secondary: const Color(0xFFFF6B6B), // Soft coral
        tertiary: const Color(0xFF4ECDC4), // Teal accent
        surfaceContainerHighest: const Color(0xFFF9F9F9), // Off-white background
        surface: Colors.white,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onTertiary: Colors.white,
        onSurface: const Color(0xFF333333), // Dark gray for text
      ),
      textTheme: GoogleFonts.poppinsTextTheme(ThemeData.light().textTheme).copyWith(
        displayLarge: GoogleFonts.poppins(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          letterSpacing: -0.5,
          color: const Color(0xFF333333),
        ),
        displayMedium: GoogleFonts.poppins(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          letterSpacing: -0.5,
          color: const Color(0xFF333333),
        ),
        displaySmall: GoogleFonts.poppins(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.25,
          color: const Color(0xFF333333),
        ),
        headlineLarge: GoogleFonts.poppins(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.25,
          color: const Color(0xFF333333),
        ),
        headlineMedium: GoogleFonts.poppins(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
        headlineSmall: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
        titleLarge: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
        titleMedium: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
        titleSmall: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
        bodyLarge: GoogleFonts.poppins(
          fontSize: 16,
          color: const Color(0xFF333333),
        ),
        bodyMedium: GoogleFonts.poppins(
          fontSize: 14,
          color: const Color(0xFF333333),
        ),
        bodySmall: GoogleFonts.poppins(
          fontSize: 12,
          color: const Color(0xFF666666),
        ),
      ),
      appBarTheme: AppBarTheme(
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF333333),
        titleTextStyle: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
        iconTheme: const IconThemeData(
          color: Color(0xFF4A6FFF),
        ),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        ),
        color: Colors.white,
        surfaceTintColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          shadowColor: Colors.black.withAlpha(25),
          backgroundColor: const Color(0xFF4A6FFF),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingMedium,
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
      ),
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        elevation: AppConstants.elevationMedium,
      ),
      bottomSheetTheme: BottomSheetThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppConstants.borderRadiusLarge),
            topRight: Radius.circular(AppConstants.borderRadiusLarge),
          ),
        ),
        elevation: AppConstants.elevationMedium,
      ),
      navigationBarTheme: NavigationBarThemeData(
        elevation: AppConstants.elevationSmall,
        labelBehavior: NavigationDestinationLabelBehavior.onlyShowSelected,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
      ),
      dividerTheme: const DividerThemeData(
        thickness: AppConstants.dividerThickness,
        space: AppConstants.spacingMedium,
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        circularTrackColor: Colors.transparent,
      ),
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(AppConstants.tooltipBorderRadius),
        ),
        textStyle: const TextStyle(
          color: Colors.white,
          fontSize: AppConstants.tooltipFontSize,
        ),
        padding: const EdgeInsets.all(AppConstants.tooltipPadding),
        margin: const EdgeInsets.all(AppConstants.tooltipMargin),
      ),
      chipTheme: ChipThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingSmall,
          vertical: AppConstants.paddingSmall / 2,
        ),
      ),
      tabBarTheme: const TabBarTheme(
        labelPadding: EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        elevation: AppConstants.elevationSmall,
        selectedItemColor: Color(0xFF1E88E5), // Blue
        unselectedItemColor: Colors.grey,
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        elevation: AppConstants.fabElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.fabBorderRadius),
        ),
      ),
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) {
            return const Color(0xFF1E88E5); // Blue
          }
          return Colors.grey;
        }),
        trackColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) {
            return const Color(0xFF1E88E5).withValues(alpha: 128); // Blue with opacity
          }
          return Colors.grey.withValues(alpha: 128);
        }),
      ),
      checkboxTheme: CheckboxThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall / 2),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) {
            return const Color(0xFF1E88E5); // Blue
          }
          return Colors.grey;
        }),
      ),
      sliderTheme: SliderThemeData(
        thumbShape: const RoundSliderThumbShape(
          enabledThumbRadius: 8,
        ),
        overlayShape: const RoundSliderOverlayShape(
          overlayRadius: 16,
        ),
        trackHeight: 4,
        activeTrackColor: const Color(0xFF1E88E5), // Blue
        inactiveTrackColor: Colors.grey.withValues(alpha: 77),
        thumbColor: const Color(0xFF1E88E5), // Blue
        overlayColor: const Color(0xFF1E88E5).withValues(alpha: 77), // Blue with opacity
      ),
    );
  }

  /// Creates dark theme
  static ThemeData darkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF64B5F6), // Light Blue
        brightness: Brightness.dark,
      ),
      textTheme: GoogleFonts.interTextTheme(ThemeData.dark().textTheme),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: AppConstants.elevationSmall,
      ),
      cardTheme: CardTheme(
        elevation: AppConstants.elevationSmall,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: AppConstants.elevationSmall,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
      ),
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
        elevation: AppConstants.elevationMedium,
      ),
      bottomSheetTheme: BottomSheetThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppConstants.borderRadiusLarge),
            topRight: Radius.circular(AppConstants.borderRadiusLarge),
          ),
        ),
        elevation: AppConstants.elevationMedium,
      ),
      navigationBarTheme: NavigationBarThemeData(
        elevation: AppConstants.elevationSmall,
        labelBehavior: NavigationDestinationLabelBehavior.onlyShowSelected,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        ),
      ),
      dividerTheme: const DividerThemeData(
        thickness: AppConstants.dividerThickness,
        space: AppConstants.spacingMedium,
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        circularTrackColor: Colors.transparent,
      ),
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: Colors.grey[700],
          borderRadius: BorderRadius.circular(AppConstants.tooltipBorderRadius),
        ),
        textStyle: const TextStyle(
          color: Colors.white,
          fontSize: AppConstants.tooltipFontSize,
        ),
        padding: const EdgeInsets.all(AppConstants.tooltipPadding),
        margin: const EdgeInsets.all(AppConstants.tooltipMargin),
      ),
      chipTheme: ChipThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingSmall,
          vertical: AppConstants.paddingSmall / 2,
        ),
      ),
      tabBarTheme: const TabBarTheme(
        labelPadding: EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        elevation: AppConstants.elevationSmall,
        selectedItemColor: Color(0xFF64B5F6), // Light Blue
        unselectedItemColor: Colors.grey,
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        elevation: AppConstants.fabElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.fabBorderRadius),
        ),
      ),
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return const Color(0xFF64B5F6); // Light Blue
          }
          return Colors.grey;
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return const Color(0xFF64B5F6).withOpacity(0.5); // Light Blue with opacity
          }
          return Colors.grey.withOpacity(0.5);
        }),
      ),
      checkboxTheme: CheckboxThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall / 2),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return const Color(0xFF64B5F6); // Light Blue
          }
          return Colors.grey;
        }),
      ),
      sliderTheme: SliderThemeData(
        thumbShape: const RoundSliderThumbShape(
          enabledThumbRadius: 8,
        ),
        overlayShape: const RoundSliderOverlayShape(
          overlayRadius: 16,
        ),
        trackHeight: 4,
        activeTrackColor: const Color(0xFF64B5F6), // Light Blue
        inactiveTrackColor: Colors.grey.withOpacity(0.3),
        thumbColor: const Color(0xFF64B5F6), // Light Blue
        overlayColor: const Color(0xFF64B5F6).withOpacity(0.3), // Light Blue with opacity
      ),
    );
  }

  /// Gets chart colors for candlesticks in light theme
  static ChartColors lightChartColors() {
    return const ChartColors(
      bullishColor: Color(0xFF4CAF50), // Green
      bearishColor: Color(0xFFF44336), // Red
      neutralColor: Color(0xFF9E9E9E), // Grey
      gridColor: Color(0xFFE0E0E0), // Light Grey
      labelColor: Color(0xFF757575), // Dark Grey
      supportColor: Color(0xFF4CAF50), // Green
      resistanceColor: Color(0xFFF44336), // Red
      trendLineColor: Color(0xFF2196F3), // Blue
      volumeColor: Color(0xFF9E9E9E), // Grey
      backgroundColor: Colors.white,
    );
  }

  /// Gets chart colors for candlesticks in dark theme
  static ChartColors darkChartColors() {
    return const ChartColors(
      bullishColor: Color(0xFF66BB6A), // Light Green
      bearishColor: Color(0xFFEF5350), // Light Red
      neutralColor: Color(0xFFBDBDBD), // Light Grey
      gridColor: Color(0xFF424242), // Dark Grey
      labelColor: Color(0xFFE0E0E0), // Light Grey
      supportColor: Color(0xFF66BB6A), // Light Green
      resistanceColor: Color(0xFFEF5350), // Light Red
      trendLineColor: Color(0xFF64B5F6), // Light Blue
      volumeColor: Color(0xFFBDBDBD), // Light Grey
      backgroundColor: Color(0xFF212121), // Dark Grey
    );
  }
}

/// Colors used for charts
class ChartColors {
  /// Color for bullish candlesticks (close > open)
  final Color bullishColor;

  /// Color for bearish candlesticks (close < open)
  final Color bearishColor;

  /// Color for neutral candlesticks (close == open)
  final Color neutralColor;

  /// Color for grid lines
  final Color gridColor;

  /// Color for axis labels
  final Color labelColor;

  /// Color for support lines
  final Color supportColor;

  /// Color for resistance lines
  final Color resistanceColor;

  /// Color for trend lines
  final Color trendLineColor;

  /// Color for volume bars
  final Color volumeColor;

  /// Background color for charts
  final Color backgroundColor;

  /// Creates a new chart colors instance
  const ChartColors({
    required this.bullishColor,
    required this.bearishColor,
    required this.neutralColor,
    required this.gridColor,
    required this.labelColor,
    required this.supportColor,
    required this.resistanceColor,
    required this.trendLineColor,
    required this.volumeColor,
    required this.backgroundColor,
  });
}
