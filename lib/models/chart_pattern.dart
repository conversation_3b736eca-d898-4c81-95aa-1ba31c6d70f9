import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';

/// Represents the trend direction of a pattern
enum PatternTrend {
  /// Bullish pattern (typically indicates upward price movement)
  bullish,

  /// Bearish pattern (typically indicates downward price movement)
  bearish,

  /// Neutral pattern (can go either way)
  neutral,

  /// Reversal pattern (changes the current trend)
  reversal,

  /// Continuation pattern (confirms the current trend)
  continuation,
}

/// Represents the difficulty level of recognizing a pattern
enum PatternDifficulty {
  /// Easy to recognize
  beginner,

  /// Moderately difficult to recognize
  intermediate,

  /// Difficult to recognize
  advanced,
}

/// Represents a chart pattern with its characteristics and data
class ChartPattern extends Equatable {
  /// Unique identifier for the pattern
  final String id;

  /// Name of the pattern
  final String name;

  /// Description of the pattern
  final String description;

  /// Path to an example image (optional)
  final String? imagePath;

  /// Trend direction of the pattern
  final PatternTrend trend;

  /// Difficulty level of recognizing the pattern
  final PatternDifficulty? difficulty;

  /// Reliability rating (1-5)
  final int? reliability;

  /// Complexity rating (1-5)
  final int? complexity;

  /// Category of the pattern
  final String? category;

  /// Whether this is a premium pattern
  final bool isPremium;

  /// Key points to identify in the pattern
  final List<String> identificationPoints;

  /// Trading strategies for this pattern
  final List<String> tradingStrategies;

  /// Icon to represent the pattern
  final IconData? icon;

  /// Color associated with the pattern
  final Color? color;

  /// Creates a new chart pattern
  const ChartPattern({
    required this.id,
    required this.name,
    required this.description,
    this.imagePath,
    required this.trend,
    this.difficulty,
    this.reliability,
    this.complexity,
    this.category,
    this.isPremium = false,
    this.identificationPoints = const [],
    this.tradingStrategies = const [],
    this.icon,
    this.color,
  });

  /// Generates a list of candlestick data for this pattern
  ///
  /// [seed] is used for reproducible random generation
  /// [length] is the number of candlesticks to generate
  /// [volatility] controls the price movement volatility (0.0-1.0)
  List<CandlestickData> generateCandlesticks({
    int? seed,
    int length = 30,
    double volatility = 0.5,
  }) {
    // This is a placeholder. Each pattern will override this method
    // with its specific generation logic in the pattern generator classes.
    return [];
  }

  /// Generates support and resistance levels for this pattern
  ///
  /// [candlesticks] is the list of candlesticks to analyze
  List<SupportResistanceLevel> generateSupportResistanceLevels(
    List<CandlestickData> candlesticks,
  ) {
    // This is a placeholder. Each pattern will override this method
    // with its specific support/resistance logic in the pattern generator classes.
    return [];
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        imagePath,
        trend,
        difficulty,
        reliability,
        complexity,
        category,
        isPremium,
        identificationPoints,
        tradingStrategies,
        icon,
        color,
      ];

  @override
  String toString() {
    return 'ChartPattern(id: $id, name: $name, trend: $trend, difficulty: $difficulty)';
  }
}
