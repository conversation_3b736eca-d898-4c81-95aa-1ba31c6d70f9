import 'package:flutter/material.dart';

/// Represents an annotation on a chart pattern
class PatternAnnotation {
  /// The date where the annotation should be placed
  final DateTime date;
  
  /// The price level where the annotation should be placed
  final double price;
  
  /// The text of the annotation
  final String text;
  
  /// The color of the annotation
  final Color color;
  
  /// Whether the annotation is important
  final bool isImportant;
  
  /// Creates a new pattern annotation
  const PatternAnnotation({
    required this.date,
    required this.price,
    required this.text,
    required this.color,
    this.isImportant = false,
  });
  
  /// Creates a bullish annotation
  factory PatternAnnotation.bullish({
    required DateTime date,
    required double price,
    required String text,
    Color color = Colors.green,
    bool isImportant = false,
  }) {
    return PatternAnnotation(
      date: date,
      price: price,
      text: text,
      color: color,
      isImportant: isImportant,
    );
  }
  
  /// Creates a bearish annotation
  factory PatternAnnotation.bearish({
    required DateTime date,
    required double price,
    required String text,
    Color color = Colors.red,
    bool isImportant = false,
  }) {
    return PatternAnnotation(
      date: date,
      price: price,
      text: text,
      color: color,
      isImportant: isImportant,
    );
  }
  
  /// Creates a neutral annotation
  factory PatternAnnotation.neutral({
    required DateTime date,
    required double price,
    required String text,
    Color color = Colors.blue,
    bool isImportant = false,
  }) {
    return PatternAnnotation(
      date: date,
      price: price,
      text: text,
      color: color,
      isImportant: isImportant,
    );
  }
  
  /// Creates a breakout annotation
  factory PatternAnnotation.breakout({
    required DateTime date,
    required double price,
    String text = 'Breakout',
    Color color = Colors.purple,
    bool isImportant = true,
  }) {
    return PatternAnnotation(
      date: date,
      price: price,
      text: text,
      color: color,
      isImportant: isImportant,
    );
  }
  
  /// Creates a support annotation
  factory PatternAnnotation.support({
    required DateTime date,
    required double price,
    String text = 'Support',
    Color color = Colors.green,
    bool isImportant = false,
  }) {
    return PatternAnnotation(
      date: date,
      price: price,
      text: text,
      color: color,
      isImportant: isImportant,
    );
  }
  
  /// Creates a resistance annotation
  factory PatternAnnotation.resistance({
    required DateTime date,
    required double price,
    String text = 'Resistance',
    Color color = Colors.red,
    bool isImportant = false,
  }) {
    return PatternAnnotation(
      date: date,
      price: price,
      text: text,
      color: color,
      isImportant: isImportant,
    );
  }
  
  /// Creates a target annotation
  factory PatternAnnotation.target({
    required DateTime date,
    required double price,
    String text = 'Target',
    Color color = Colors.orange,
    bool isImportant = true,
  }) {
    return PatternAnnotation(
      date: date,
      price: price,
      text: text,
      color: color,
      isImportant: isImportant,
    );
  }
  
  /// Creates a stop loss annotation
  factory PatternAnnotation.stopLoss({
    required DateTime date,
    required double price,
    String text = 'Stop Loss',
    Color color = Colors.red,
    bool isImportant = true,
  }) {
    return PatternAnnotation(
      date: date,
      price: price,
      text: text,
      color: color,
      isImportant: isImportant,
    );
  }
}
