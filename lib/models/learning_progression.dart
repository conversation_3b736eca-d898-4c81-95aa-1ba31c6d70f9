import 'package:learn_chart_patterns/models/chart_pattern.dart';

/// Learning progression levels for pattern recognition
enum LearningLevel {
  /// Complete beginner - never seen patterns before
  absolute_beginner,
  
  /// Beginner - knows basic concepts
  beginner,
  
  /// Intermediate - can identify some patterns
  intermediate,
  
  /// Advanced - can identify most patterns
  advanced,
  
  /// Expert - can identify all patterns and variations
  expert,
}

/// Learning stage for a specific pattern
enum PatternLearningStage {
  /// Not yet introduced to this pattern
  not_started,
  
  /// Learning the basic concept
  introduction,
  
  /// Practicing identification
  practice,
  
  /// Taking quizzes on this pattern
  assessment,
  
  /// Pattern is mastered
  mastered,
  
  /// Needs review due to poor performance
  needs_review,
}

/// Learning progression configuration
class LearningProgression {
  /// Current learning level of the user
  final LearningLevel level;
  
  /// Patterns organized by difficulty
  final Map<PatternDifficulty, List<String>> patternsByDifficulty;
  
  /// Recommended learning order
  final List<String> learningOrder;
  
  /// Prerequisites for each pattern
  final Map<String, List<String>> prerequisites;
  
  /// Minimum accuracy required to advance
  final double masteryThreshold;

  /// Creates a new learning progression
  const LearningProgression({
    required this.level,
    required this.patternsByDifficulty,
    required this.learningOrder,
    required this.prerequisites,
    this.masteryThreshold = 0.8,
  });

  /// Creates a default learning progression
  factory LearningProgression.defaultProgression() {
    return LearningProgression(
      level: LearningLevel.beginner,
      patternsByDifficulty: {
        PatternDifficulty.beginner: [
          'double_top',
          'double_bottom',
          'head_and_shoulders',
          'inverse_head_and_shoulders',
        ],
        PatternDifficulty.intermediate: [
          'ascending_triangle',
          'descending_triangle',
          'symmetrical_triangle',
          'cup_and_handle',
          'rectangle',
        ],
        PatternDifficulty.advanced: [
          'bullish_flag',
          'bearish_flag',
          'bullish_pennant',
          'bearish_pennant',
          'rising_wedge',
          'falling_wedge',
          'rounding_bottom',
          'rounding_top',
        ],
      },
      learningOrder: [
        // Start with most recognizable patterns
        'double_top',
        'double_bottom',
        'head_and_shoulders',
        'inverse_head_and_shoulders',
        
        // Move to triangles (clear trend lines)
        'ascending_triangle',
        'descending_triangle',
        'symmetrical_triangle',
        
        // Add continuation patterns
        'rectangle',
        'cup_and_handle',
        
        // Advanced flag and pennant patterns
        'bullish_flag',
        'bearish_flag',
        'bullish_pennant',
        'bearish_pennant',
        
        // Complex wedge patterns
        'rising_wedge',
        'falling_wedge',
        
        // Rounding patterns (hardest to identify)
        'rounding_bottom',
        'rounding_top',
      ],
      prerequisites: {
        'inverse_head_and_shoulders': ['head_and_shoulders'],
        'descending_triangle': ['ascending_triangle'],
        'symmetrical_triangle': ['ascending_triangle', 'descending_triangle'],
        'bearish_flag': ['bullish_flag'],
        'bearish_pennant': ['bullish_pennant'],
        'falling_wedge': ['rising_wedge'],
        'rounding_top': ['rounding_bottom'],
      },
    );
  }

  /// Gets patterns available for the current learning level
  List<String> getAvailablePatterns(LearningLevel level) {
    switch (level) {
      case LearningLevel.absolute_beginner:
        return patternsByDifficulty[PatternDifficulty.beginner]?.take(2).toList() ?? [];
      case LearningLevel.beginner:
        return patternsByDifficulty[PatternDifficulty.beginner] ?? [];
      case LearningLevel.intermediate:
        return [
          ...patternsByDifficulty[PatternDifficulty.beginner] ?? [],
          ...patternsByDifficulty[PatternDifficulty.intermediate] ?? [],
        ];
      case LearningLevel.advanced:
        return [
          ...patternsByDifficulty[PatternDifficulty.beginner] ?? [],
          ...patternsByDifficulty[PatternDifficulty.intermediate] ?? [],
          ...patternsByDifficulty[PatternDifficulty.advanced]?.take(4).toList() ?? [],
        ];
      case LearningLevel.expert:
        return learningOrder;
    }
  }

  /// Gets the next pattern to learn based on current progress
  String? getNextPattern(Map<String, PatternLearningStage> progress) {
    for (final patternId in learningOrder) {
      final stage = progress[patternId] ?? PatternLearningStage.not_started;
      
      // Check if prerequisites are met
      if (!_arePrerequisitesMet(patternId, progress)) {
        continue;
      }
      
      // Return the first pattern that's not mastered
      if (stage != PatternLearningStage.mastered) {
        return patternId;
      }
    }
    
    return null; // All patterns mastered
  }

  /// Checks if prerequisites for a pattern are met
  bool _arePrerequisitesMet(String patternId, Map<String, PatternLearningStage> progress) {
    final requiredPatterns = prerequisites[patternId] ?? [];
    
    for (final requiredPattern in requiredPatterns) {
      final stage = progress[requiredPattern] ?? PatternLearningStage.not_started;
      if (stage != PatternLearningStage.mastered) {
        return false;
      }
    }
    
    return true;
  }

  /// Gets patterns that need review based on poor performance
  List<String> getPatternsNeedingReview(Map<String, PatternLearningStage> progress) {
    return progress.entries
        .where((entry) => entry.value == PatternLearningStage.needs_review)
        .map((entry) => entry.key)
        .toList();
  }

  /// Calculates overall learning progress (0.0 to 1.0)
  double calculateProgress(Map<String, PatternLearningStage> progress) {
    final availablePatterns = getAvailablePatterns(level);
    if (availablePatterns.isEmpty) return 1.0;
    
    final masteredCount = availablePatterns
        .where((pattern) => progress[pattern] == PatternLearningStage.mastered)
        .length;
    
    return masteredCount / availablePatterns.length;
  }

  /// Suggests the appropriate learning level based on performance
  LearningLevel suggestLearningLevel(Map<String, double> patternAccuracies) {
    final beginnerPatterns = patternsByDifficulty[PatternDifficulty.beginner] ?? [];
    final intermediatePatterns = patternsByDifficulty[PatternDifficulty.intermediate] ?? [];
    final advancedPatterns = patternsByDifficulty[PatternDifficulty.advanced] ?? [];
    
    // Calculate average accuracy for each difficulty level
    final beginnerAccuracy = _calculateAverageAccuracy(beginnerPatterns, patternAccuracies);
    final intermediateAccuracy = _calculateAverageAccuracy(intermediatePatterns, patternAccuracies);
    final advancedAccuracy = _calculateAverageAccuracy(advancedPatterns, patternAccuracies);
    
    // Determine appropriate level
    if (advancedAccuracy >= masteryThreshold) {
      return LearningLevel.expert;
    } else if (intermediateAccuracy >= masteryThreshold && beginnerAccuracy >= masteryThreshold) {
      return LearningLevel.advanced;
    } else if (beginnerAccuracy >= masteryThreshold) {
      return LearningLevel.intermediate;
    } else if (beginnerAccuracy >= 0.6) {
      return LearningLevel.beginner;
    } else {
      return LearningLevel.absolute_beginner;
    }
  }

  /// Calculates average accuracy for a list of patterns
  double _calculateAverageAccuracy(List<String> patterns, Map<String, double> accuracies) {
    if (patterns.isEmpty) return 0.0;
    
    final validAccuracies = patterns
        .map((pattern) => accuracies[pattern])
        .where((accuracy) => accuracy != null)
        .cast<double>()
        .toList();
    
    if (validAccuracies.isEmpty) return 0.0;
    
    return validAccuracies.reduce((a, b) => a + b) / validAccuracies.length;
  }
}

/// Tutorial step for guided learning
class TutorialStep {
  /// Unique identifier for the step
  final String id;
  
  /// Title of the step
  final String title;
  
  /// Description of what to do
  final String description;
  
  /// Type of tutorial step
  final TutorialStepType type;
  
  /// Whether this step is interactive
  final bool isInteractive;
  
  /// Expected user action
  final String? expectedAction;
  
  /// Hint text for the user
  final String? hint;
  
  /// Whether this step is completed
  final bool isCompleted;

  /// Creates a new tutorial step
  const TutorialStep({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.isInteractive = false,
    this.expectedAction,
    this.hint,
    this.isCompleted = false,
  });

  /// Creates a copy with updated properties
  TutorialStep copyWith({
    String? id,
    String? title,
    String? description,
    TutorialStepType? type,
    bool? isInteractive,
    String? expectedAction,
    String? hint,
    bool? isCompleted,
  }) {
    return TutorialStep(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      isInteractive: isInteractive ?? this.isInteractive,
      expectedAction: expectedAction ?? this.expectedAction,
      hint: hint ?? this.hint,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}

/// Types of tutorial steps
enum TutorialStepType {
  /// Introduction to a concept
  introduction,
  
  /// Explanation of pattern characteristics
  explanation,
  
  /// Interactive identification task
  identification,
  
  /// Practice quiz question
  quiz,
  
  /// Summary of learned concepts
  summary,
  
  /// Assessment of understanding
  assessment,
}

/// Guided tutorial for learning a specific pattern
class PatternTutorial {
  /// Pattern being taught
  final String patternId;
  
  /// Tutorial steps
  final List<TutorialStep> steps;
  
  /// Current step index
  final int currentStepIndex;
  
  /// Whether the tutorial is completed
  final bool isCompleted;

  /// Creates a new pattern tutorial
  const PatternTutorial({
    required this.patternId,
    required this.steps,
    this.currentStepIndex = 0,
    this.isCompleted = false,
  });

  /// Gets the current step
  TutorialStep? get currentStep {
    if (currentStepIndex >= 0 && currentStepIndex < steps.length) {
      return steps[currentStepIndex];
    }
    return null;
  }

  /// Gets the progress percentage (0.0 to 1.0)
  double get progress {
    if (steps.isEmpty) return 1.0;
    return currentStepIndex / steps.length;
  }

  /// Creates a copy with updated properties
  PatternTutorial copyWith({
    String? patternId,
    List<TutorialStep>? steps,
    int? currentStepIndex,
    bool? isCompleted,
  }) {
    return PatternTutorial(
      patternId: patternId ?? this.patternId,
      steps: steps ?? this.steps,
      currentStepIndex: currentStepIndex ?? this.currentStepIndex,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}
