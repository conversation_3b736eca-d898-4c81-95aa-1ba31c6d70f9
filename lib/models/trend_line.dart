import 'package:flutter/material.dart';

/// Represents a point on a trend line
class TrendLinePoint {
  /// The date of the point
  final DateTime date;
  
  /// The price at the point
  final double price;
  
  /// Creates a new trend line point
  const TrendLinePoint({
    required this.date,
    required this.price,
  });
}

/// Represents a trend line on a chart
class TrendLine {
  /// The points that make up the trend line
  final List<TrendLinePoint> points;
  
  /// Whether the trend line is bullish (upward)
  final bool isBullish;
  
  /// Whether the trend line is dashed
  final bool isDashed;
  
  /// The color of the trend line
  final Color color;
  
  /// The width of the trend line
  final double width;
  
  /// The label for the trend line (optional)
  final String? label;
  
  /// Creates a new trend line
  const TrendLine({
    required this.points,
    required this.isBullish,
    this.isDashed = false,
    required this.color,
    this.width = 2.0,
    this.label,
  });
  
  /// Creates a trend line from two points
  factory TrendLine.fromPoints({
    required DateTime startDate,
    required double startPrice,
    required DateTime endDate,
    required double endPrice,
    bool isDashed = false,
    Color? color,
    double width = 2.0,
    String? label,
  }) {
    final isBullish = endPrice > startPrice;
    
    return TrendLine(
      points: [
        TrendLinePoint(date: startDate, price: startPrice),
        TrendLinePoint(date: endDate, price: endPrice),
      ],
      isBullish: isBullish,
      isDashed: isDashed,
      color: color ?? (isBullish ? Colors.green : Colors.red),
      width: width,
      label: label,
    );
  }
  
  /// Creates a horizontal trend line
  factory TrendLine.horizontal({
    required DateTime startDate,
    required DateTime endDate,
    required double price,
    bool isDashed = true,
    Color color = Colors.blue,
    double width = 1.5,
    String? label,
  }) {
    return TrendLine(
      points: [
        TrendLinePoint(date: startDate, price: price),
        TrendLinePoint(date: endDate, price: price),
      ],
      isBullish: false,
      isDashed: isDashed,
      color: color,
      width: width,
      label: label,
    );
  }
  
  /// Creates a support line
  factory TrendLine.support({
    required DateTime startDate,
    required DateTime endDate,
    required double price,
    bool isDashed = false,
    Color color = Colors.green,
    double width = 1.5,
    String? label,
  }) {
    return TrendLine.horizontal(
      startDate: startDate,
      endDate: endDate,
      price: price,
      isDashed: isDashed,
      color: color,
      width: width,
      label: label ?? 'Support',
    );
  }
  
  /// Creates a resistance line
  factory TrendLine.resistance({
    required DateTime startDate,
    required DateTime endDate,
    required double price,
    bool isDashed = false,
    Color color = Colors.red,
    double width = 1.5,
    String? label,
  }) {
    return TrendLine.horizontal(
      startDate: startDate,
      endDate: endDate,
      price: price,
      isDashed: isDashed,
      color: color,
      width: width,
      label: label ?? 'Resistance',
    );
  }
}
