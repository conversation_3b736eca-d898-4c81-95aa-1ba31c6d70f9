import 'package:flutter/material.dart';

/// Enhanced annotation types for pattern education
enum AnnotationType {
  /// Key pattern formation point
  keyPoint,
  
  /// Support level annotation
  support,
  
  /// Resistance level annotation
  resistance,
  
  /// Breakout point annotation
  breakout,
  
  /// Entry point annotation
  entry,
  
  /// Stop loss annotation
  stopLoss,
  
  /// Target price annotation
  target,
  
  /// Volume spike annotation
  volumeSpike,
  
  /// Trend line annotation
  trendLine,
  
  /// Pattern completion annotation
  completion,
  
  /// Educational note annotation
  educationalNote,
}

/// Enhanced pattern annotation with educational features
class EnhancedPatternAnnotation {
  /// The date where the annotation should be placed
  final DateTime date;
  
  /// The price level where the annotation should be placed
  final double price;
  
  /// The text of the annotation
  final String text;
  
  /// The type of annotation
  final AnnotationType type;
  
  /// The color of the annotation
  final Color color;
  
  /// Whether the annotation is important
  final bool isImportant;
  
  /// Icon to display with the annotation
  final IconData? icon;
  
  /// Detailed description for educational purposes
  final String? description;
  
  /// Whether to show a callout line
  final bool showCallout;
  
  /// Position of the annotation relative to the point
  final AnnotationPosition position;
  
  /// Size of the annotation
  final AnnotationSize size;

  /// Creates a new enhanced pattern annotation
  const EnhancedPatternAnnotation({
    required this.date,
    required this.price,
    required this.text,
    required this.type,
    required this.color,
    this.isImportant = false,
    this.icon,
    this.description,
    this.showCallout = true,
    this.position = AnnotationPosition.auto,
    this.size = AnnotationSize.medium,
  });

  /// Creates a key point annotation
  factory EnhancedPatternAnnotation.keyPoint({
    required DateTime date,
    required double price,
    required String text,
    String? description,
    Color color = Colors.orange,
    bool isImportant = true,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.keyPoint,
      color: color,
      isImportant: isImportant,
      icon: Icons.star,
      description: description,
      size: AnnotationSize.large,
    );
  }

  /// Creates a support level annotation
  factory EnhancedPatternAnnotation.support({
    required DateTime date,
    required double price,
    String text = 'Support',
    String? description,
    Color color = Colors.green,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.support,
      color: color,
      icon: Icons.trending_up,
      description: description ?? 'Price level where buying interest emerges',
      position: AnnotationPosition.below,
    );
  }

  /// Creates a resistance level annotation
  factory EnhancedPatternAnnotation.resistance({
    required DateTime date,
    required double price,
    String text = 'Resistance',
    String? description,
    Color color = Colors.red,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.resistance,
      color: color,
      icon: Icons.trending_down,
      description: description ?? 'Price level where selling pressure emerges',
      position: AnnotationPosition.above,
    );
  }

  /// Creates a breakout annotation
  factory EnhancedPatternAnnotation.breakout({
    required DateTime date,
    required double price,
    String text = 'Breakout',
    String? description,
    Color color = Colors.purple,
    bool isImportant = true,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.breakout,
      color: color,
      isImportant: isImportant,
      icon: Icons.rocket_launch,
      description: description ?? 'Price breaks through key level with volume',
      size: AnnotationSize.large,
    );
  }

  /// Creates an entry point annotation
  factory EnhancedPatternAnnotation.entry({
    required DateTime date,
    required double price,
    String text = 'Entry',
    String? description,
    Color color = Colors.blue,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.entry,
      color: color,
      icon: Icons.login,
      description: description ?? 'Optimal entry point for the trade',
    );
  }

  /// Creates a stop loss annotation
  factory EnhancedPatternAnnotation.stopLoss({
    required DateTime date,
    required double price,
    String text = 'Stop Loss',
    String? description,
    Color color = Colors.red,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.stopLoss,
      color: color,
      icon: Icons.block,
      description: description ?? 'Risk management level to limit losses',
    );
  }

  /// Creates a target price annotation
  factory EnhancedPatternAnnotation.target({
    required DateTime date,
    required double price,
    String text = 'Target',
    String? description,
    Color color = Colors.green,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.target,
      color: color,
      icon: Icons.flag,
      description: description ?? 'Profit target based on pattern measurement',
    );
  }

  /// Creates an educational note annotation
  factory EnhancedPatternAnnotation.educationalNote({
    required DateTime date,
    required double price,
    required String text,
    required String description,
    Color color = Colors.blue,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.educationalNote,
      color: color,
      icon: Icons.school,
      description: description,
      showCallout: true,
      size: AnnotationSize.large,
    );
  }

  /// Creates a pattern completion annotation
  factory EnhancedPatternAnnotation.completion({
    required DateTime date,
    required double price,
    String text = 'Pattern Complete',
    String? description,
    Color color = Colors.amber,
    bool isImportant = true,
  }) {
    return EnhancedPatternAnnotation(
      date: date,
      price: price,
      text: text,
      type: AnnotationType.completion,
      color: color,
      isImportant: isImportant,
      icon: Icons.check_circle,
      description: description ?? 'Pattern formation is now complete',
      size: AnnotationSize.large,
    );
  }
}

/// Position of annotation relative to the point
enum AnnotationPosition {
  /// Automatically determine best position
  auto,
  
  /// Position above the point
  above,
  
  /// Position below the point
  below,
  
  /// Position to the left of the point
  left,
  
  /// Position to the right of the point
  right,
}

/// Size of the annotation
enum AnnotationSize {
  /// Small annotation
  small,
  
  /// Medium annotation
  medium,
  
  /// Large annotation
  large,
}

/// Pattern overlay configuration
class PatternOverlayConfig {
  /// Whether to show support/resistance lines
  final bool showSupportResistance;
  
  /// Whether to show trend lines
  final bool showTrendLines;
  
  /// Whether to show annotations
  final bool showAnnotations;
  
  /// Whether to show key points
  final bool showKeyPoints;
  
  /// Whether to show entry/exit points
  final bool showTradingPoints;
  
  /// Whether to show educational notes
  final bool showEducationalNotes;
  
  /// Opacity of overlay elements
  final double opacity;

  /// Creates a new pattern overlay configuration
  const PatternOverlayConfig({
    this.showSupportResistance = true,
    this.showTrendLines = true,
    this.showAnnotations = true,
    this.showKeyPoints = true,
    this.showTradingPoints = false,
    this.showEducationalNotes = true,
    this.opacity = 0.8,
  });

  /// Creates a configuration for beginners
  factory PatternOverlayConfig.beginner() {
    return const PatternOverlayConfig(
      showSupportResistance = true,
      showTrendLines = true,
      showAnnotations = true,
      showKeyPoints = true,
      showTradingPoints = false,
      showEducationalNotes = true,
      opacity = 0.9,
    );
  }

  /// Creates a configuration for advanced users
  factory PatternOverlayConfig.advanced() {
    return const PatternOverlayConfig(
      showSupportResistance = true,
      showTrendLines = true,
      showAnnotations = false,
      showKeyPoints = false,
      showTradingPoints = true,
      showEducationalNotes = false,
      opacity = 0.7,
    );
  }

  /// Creates a clean configuration with minimal overlays
  factory PatternOverlayConfig.clean() {
    return const PatternOverlayConfig(
      showSupportResistance = true,
      showTrendLines = false,
      showAnnotations = false,
      showKeyPoints = false,
      showTradingPoints = false,
      showEducationalNotes = false,
      opacity = 0.6,
    );
  }
}
