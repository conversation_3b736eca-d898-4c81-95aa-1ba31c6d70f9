import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// Represents a trading course type
enum CourseType {
  /// Forex trading course
  forex,
  
  /// Cryptocurrency trading course
  crypto,
}

/// Represents a lesson content type
enum ContentType {
  /// Text content
  text,
  
  /// Video content
  video,
  
  /// Interactive chart
  chart,
  
  /// Quiz
  quiz,
}

/// Represents a difficulty level
enum DifficultyLevel {
  /// Beginner level
  beginner,
  
  /// Intermediate level
  intermediate,
  
  /// Advanced level
  advanced,
}

/// Represents a trading course
class TradingCourse {
  /// Unique identifier
  final String id;
  
  /// Course title
  final String title;
  
  /// Course description
  final String description;
  
  /// Course type (forex or crypto)
  final CourseType type;
  
  /// Course difficulty level
  final DifficultyLevel difficulty;
  
  /// Course icon
  final IconData icon;
  
  /// Course color
  final Color color;
  
  /// Course modules
  final List<CourseModule> modules;
  
  /// Whether this is a premium course
  final bool isPremium;
  
  /// Course thumbnail image asset path
  final String? thumbnailPath;
  
  /// Course author
  final String author;
  
  /// Course duration in minutes
  final int durationMinutes;
  
  /// Creates a new trading course
  TradingCourse({
    String? id,
    required this.title,
    required this.description,
    required this.type,
    required this.difficulty,
    required this.icon,
    required this.color,
    required this.modules,
    this.isPremium = false,
    this.thumbnailPath,
    required this.author,
    required this.durationMinutes,
  }) : id = id ?? const Uuid().v4();
  
  /// Gets the total number of lessons in the course
  int get totalLessons {
    return modules.fold(0, (sum, module) => sum + module.lessons.length);
  }
  
  /// Gets the total number of completed lessons
  int getCompletedLessons(Map<String, bool> completionStatus) {
    return modules.fold(0, (sum, module) {
      return sum + module.getCompletedLessons(completionStatus);
    });
  }
  
  /// Gets the completion percentage
  double getCompletionPercentage(Map<String, bool> completionStatus) {
    final total = totalLessons;
    if (total == 0) return 0.0;
    
    final completed = getCompletedLessons(completionStatus);
    return completed / total;
  }
  
  /// Gets the difficulty text
  String get difficultyText {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return 'Beginner';
      case DifficultyLevel.intermediate:
        return 'Intermediate';
      case DifficultyLevel.advanced:
        return 'Advanced';
    }
  }
  
  /// Gets the type text
  String get typeText {
    switch (type) {
      case CourseType.forex:
        return 'Forex';
      case CourseType.crypto:
        return 'Cryptocurrency';
    }
  }
  
  /// Gets the formatted duration
  String get formattedDuration {
    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;
    
    if (hours > 0) {
      return '$hours h ${minutes > 0 ? '$minutes min' : ''}';
    } else {
      return '$minutes min';
    }
  }
}

/// Represents a module within a course
class CourseModule {
  /// Unique identifier
  final String id;
  
  /// Module title
  final String title;
  
  /// Module description
  final String description;
  
  /// Module icon
  final IconData icon;
  
  /// Module lessons
  final List<Lesson> lessons;
  
  /// Creates a new course module
  CourseModule({
    String? id,
    required this.title,
    required this.description,
    required this.icon,
    required this.lessons,
  }) : id = id ?? const Uuid().v4();
  
  /// Gets the number of completed lessons
  int getCompletedLessons(Map<String, bool> completionStatus) {
    return lessons.where((lesson) => 
      completionStatus[lesson.id] == true
    ).length;
  }
  
  /// Gets the completion percentage
  double getCompletionPercentage(Map<String, bool> completionStatus) {
    if (lessons.isEmpty) return 0.0;
    
    final completed = getCompletedLessons(completionStatus);
    return completed / lessons.length;
  }
}

/// Represents a lesson within a module
class Lesson {
  /// Unique identifier
  final String id;
  
  /// Lesson title
  final String title;
  
  /// Lesson content
  final String content;
  
  /// Content type
  final ContentType contentType;
  
  /// Video URL (if content type is video)
  final String? videoUrl;
  
  /// Chart data (if content type is chart)
  final Map<String, dynamic>? chartData;
  
  /// Quiz questions (if content type is quiz)
  final List<Map<String, dynamic>>? quizData;
  
  /// Additional resources
  final List<Map<String, String>>? resources;
  
  /// Estimated time to complete in minutes
  final int durationMinutes;
  
  /// Creates a new lesson
  Lesson({
    String? id,
    required this.title,
    required this.content,
    required this.contentType,
    this.videoUrl,
    this.chartData,
    this.quizData,
    this.resources,
    required this.durationMinutes,
  }) : id = id ?? const Uuid().v4();
  
  /// Gets the content type icon
  IconData get contentTypeIcon {
    switch (contentType) {
      case ContentType.text:
        return Icons.article;
      case ContentType.video:
        return Icons.video_library;
      case ContentType.chart:
        return Icons.candlestick_chart;
      case ContentType.quiz:
        return Icons.quiz;
    }
  }
  
  /// Gets the content type text
  String get contentTypeText {
    switch (contentType) {
      case ContentType.text:
        return 'Reading';
      case ContentType.video:
        return 'Video';
      case ContentType.chart:
        return 'Interactive Chart';
      case ContentType.quiz:
        return 'Quiz';
    }
  }
}
