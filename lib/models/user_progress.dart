import 'package:equatable/equatable.dart';

/// Represents the user's progress in the app
class UserProgress extends Equatable {
  /// Map of pattern IDs to whether they've been learned
  final Map<String, bool> learnedPatterns;
  
  /// Map of pattern IDs to quiz accuracy percentage (0.0-1.0)
  final Map<String, double> quizAccuracy;
  
  /// Current streak of consecutive days practiced
  final int currentStreak;
  
  /// Highest streak achieved
  final int highestStreak;
  
  /// Last date the user practiced (for streak calculation)
  final DateTime? lastPracticeDate;
  
  /// Total number of quizzes completed
  final int totalQuizzesCompleted;
  
  /// Total number of correct answers
  final int totalCorrectAnswers;

  /// Creates a new user progress instance
  const UserProgress({
    this.learnedPatterns = const {},
    this.quizAccuracy = const {},
    this.currentStreak = 0,
    this.highestStreak = 0,
    this.lastPracticeDate,
    this.totalQuizzesCompleted = 0,
    this.totalCorrectAnswers = 0,
  });

  /// Factory constructor to create an empty progress instance
  factory UserProgress.empty() {
    return const UserProgress();
  }

  /// Overall completion percentage (0.0-1.0)
  double get completionPercentage {
    if (learnedPatterns.isEmpty) return 0.0;
    
    final learnedCount = learnedPatterns.values.where((learned) => learned).length;
    return learnedCount / learnedPatterns.length;
  }

  /// Overall quiz accuracy percentage (0.0-1.0)
  double get overallQuizAccuracy {
    if (totalQuizzesCompleted == 0) return 0.0;
    return totalCorrectAnswers / totalQuizzesCompleted;
  }

  /// Whether the user has a current streak
  bool get hasStreak => currentStreak > 0;

  /// Creates a copy of this progress with the given fields replaced with new values
  UserProgress copyWith({
    Map<String, bool>? learnedPatterns,
    Map<String, double>? quizAccuracy,
    int? currentStreak,
    int? highestStreak,
    DateTime? lastPracticeDate,
    bool clearLastPracticeDate = false,
    int? totalQuizzesCompleted,
    int? totalCorrectAnswers,
  }) {
    return UserProgress(
      learnedPatterns: learnedPatterns ?? this.learnedPatterns,
      quizAccuracy: quizAccuracy ?? this.quizAccuracy,
      currentStreak: currentStreak ?? this.currentStreak,
      highestStreak: highestStreak ?? this.highestStreak,
      lastPracticeDate: clearLastPracticeDate ? null : (lastPracticeDate ?? this.lastPracticeDate),
      totalQuizzesCompleted: totalQuizzesCompleted ?? this.totalQuizzesCompleted,
      totalCorrectAnswers: totalCorrectAnswers ?? this.totalCorrectAnswers,
    );
  }

  /// Marks a pattern as learned
  UserProgress markPatternAsLearned(String patternId) {
    final updatedLearnedPatterns = Map<String, bool>.from(learnedPatterns);
    updatedLearnedPatterns[patternId] = true;
    
    return copyWith(
      learnedPatterns: updatedLearnedPatterns,
      lastPracticeDate: DateTime.now(),
    );
  }

  /// Records a quiz result
  UserProgress recordQuizResult(String patternId, bool isCorrect) {
    final updatedQuizAccuracy = Map<String, double>.from(quizAccuracy);
    
    // Update pattern-specific accuracy
    final currentAccuracy = quizAccuracy[patternId] ?? 0.0;
    final currentAttempts = quizAccuracy.containsKey(patternId) ? 1 : 0;
    final newAccuracy = (currentAccuracy * currentAttempts + (isCorrect ? 1.0 : 0.0)) / (currentAttempts + 1);
    updatedQuizAccuracy[patternId] = newAccuracy;
    
    return copyWith(
      quizAccuracy: updatedQuizAccuracy,
      totalQuizzesCompleted: totalQuizzesCompleted + 1,
      totalCorrectAnswers: totalCorrectAnswers + (isCorrect ? 1 : 0),
      lastPracticeDate: DateTime.now(),
    );
  }

  /// Updates the streak based on the current date
  UserProgress updateStreak() {
    final now = DateTime.now();
    
    // If no previous practice, start a new streak
    if (lastPracticeDate == null) {
      return copyWith(
        currentStreak: 1,
        highestStreak: 1,
        lastPracticeDate: now,
      );
    }
    
    // Check if the last practice was yesterday
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final lastPracticeDay = DateTime(
      lastPracticeDate!.year,
      lastPracticeDate!.month,
      lastPracticeDate!.day,
    );
    
    // If practiced today already, no change
    if (lastPracticeDay.year == now.year && 
        lastPracticeDay.month == now.month && 
        lastPracticeDay.day == now.day) {
      return this;
    }
    
    // If practiced yesterday, increment streak
    if (lastPracticeDay.year == yesterday.year && 
        lastPracticeDay.month == yesterday.month && 
        lastPracticeDay.day == yesterday.day) {
      final newStreak = currentStreak + 1;
      return copyWith(
        currentStreak: newStreak,
        highestStreak: newStreak > highestStreak ? newStreak : highestStreak,
        lastPracticeDate: now,
      );
    }
    
    // Otherwise, reset streak
    return copyWith(
      currentStreak: 1,
      lastPracticeDate: now,
    );
  }

  @override
  List<Object?> get props => [
        learnedPatterns,
        quizAccuracy,
        currentStreak,
        highestStreak,
        lastPracticeDate,
        totalQuizzesCompleted,
        totalCorrectAnswers,
      ];

  @override
  String toString() {
    return 'UserProgress(learnedPatterns: ${learnedPatterns.length}, '
        'quizAccuracy: ${quizAccuracy.length}, '
        'currentStreak: $currentStreak, '
        'highestStreak: $highestStreak, '
        'lastPracticeDate: $lastPracticeDate, '
        'totalQuizzesCompleted: $totalQuizzesCompleted, '
        'totalCorrectAnswers: $totalCorrectAnswers)';
  }
}
