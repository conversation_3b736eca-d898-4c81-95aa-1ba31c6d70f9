import 'package:flutter/material.dart';

/// Enum representing the type of premium content
enum PremiumContentType {
  /// Advanced chart pattern analysis
  advancedPatterns,

  /// Professional trading strategies
  tradingStrategies,

  /// Risk management systems
  riskManagement,

  /// Market psychology mastery
  marketPsychology,

  /// Algorithmic trading basics
  algorithmicTrading,

  /// Institutional trading insights
  institutionalTrading,
}

/// Model for premium content items
class PremiumContent {
  /// Unique identifier for the content
  final String id;

  /// Title of the content
  final String title;

  /// Short description of the content
  final String description;

  /// Detailed content
  final String content;

  /// Icon to display
  final IconData icon;

  /// Color for the icon and accents
  final Color color;

  /// Type of premium content
  final PremiumContentType type;

  /// Whether this content requires the advanced patterns purchase
  final bool requiresAdvancedPatterns;

  /// Whether this content requires the strategy guides purchase
  final bool requiresStrategyGuides;

  /// Creates a new premium content item
  const PremiumContent({
    required this.id,
    required this.title,
    required this.description,
    required this.content,
    required this.icon,
    required this.color,
    required this.type,
    this.requiresAdvancedPatterns = false,
    this.requiresStrategyGuides = false,
  });

  /// Get all premium content items
  static List<PremiumContent> getAll() {
    return [
      // Advanced Chart Pattern Analysis
      PremiumContent(
        id: 'harmonic_patterns',
        title: 'Harmonic Patterns',
        description: 'Advanced Fibonacci-based patterns for precise entries and exits',
        content: '''
# Harmonic Patterns

Harmonic patterns are advanced chart formations that use precise Fibonacci measurements to identify potential reversal points in the market. Unlike basic chart patterns, harmonic patterns follow specific mathematical ratios that must be met for the pattern to be valid.

## Key Harmonic Patterns

### The Gartley Pattern
The Gartley pattern, first described by H.M. Gartley in his 1935 book "Profits in the Stock Market," is characterized by:
- Point X to A: Initial price move
- Point A to B: Retracement of 61.8% of XA
- Point B to C: Retracement of 38.2% or 88.6% of AB
- Point C to D: Extension of 127.2% or 161.8% of BC

The completion of point D offers a high-probability trading opportunity in the direction of point A to D.

### The Butterfly Pattern
The Butterfly pattern offers extreme price extensions:
- Point X to A: Initial price move
- Point A to B: Retracement of 78.6% of XA
- Point B to C: Extension of 127.2% or 161.8% of AB
- Point C to D: Retracement of 78.6% of XC

### The Bat Pattern
The Bat pattern is known for its precision:
- Point X to A: Initial price move
- Point A to B: Retracement of 38.2% or 50% of XA
- Point B to C: Retracement of 38.2% to 88.6% of AB
- Point C to D: Extension of 161.8% of BC

### The Crab Pattern
The Crab pattern features the deepest retracement:
- Point X to A: Initial price move
- Point A to B: Retracement of 38.2% to 61.8% of XA
- Point B to C: Retracement of 38.2% to 88.6% of AB
- Point C to D: Extension of 261.8% of BC

## Trading Harmonic Patterns

1. **Pattern Identification**: Use Fibonacci tools to confirm the pattern meets the required ratios
2. **Potential Reversal Zone (PRZ)**: The area where point D is expected to form
3. **Entry Strategy**: Enter when price reaches the PRZ and shows reversal confirmation
4. **Stop Loss**: Place stops just beyond the PRZ
5. **Take Profit**: Use Fibonacci extensions of the AD leg or previous swing points

## Advanced Considerations

- **Confluence**: Look for harmonic patterns that align with key support/resistance levels
- **Timeframe Analysis**: Patterns on higher timeframes have greater significance
- **Volume Confirmation**: Volume should decrease during pattern formation and increase at reversal points
- **Divergence**: Look for RSI or MACD divergence at point D for stronger signals

Harmonic patterns require precision and practice but can provide high-probability trading opportunities when mastered.
''',
        icon: Icons.auto_graph,
        color: Colors.purple,
        type: PremiumContentType.advancedPatterns,
        requiresAdvancedPatterns: true,
      ),
      
      // Professional Trading Strategies
      PremiumContent(
        id: 'multi_timeframe_analysis',
        title: 'Multi-Timeframe Analysis',
        description: 'How to analyze markets across multiple timeframes for better entries and exits',
        content: '''
# Multi-Timeframe Analysis

Multi-timeframe analysis is a powerful approach that involves examining price action across different timeframes to gain a comprehensive view of the market. This technique helps traders align with the dominant trend while finding optimal entry and exit points.

## The Timeframe Hierarchy

### Higher Timeframes (HTF)
- Monthly, Weekly, Daily charts
- Determine the primary trend direction
- Identify major support/resistance levels
- Lower noise, higher reliability

### Intermediate Timeframes (ITF)
- 4-Hour, 1-Hour charts
- Confirm the trend from HTF
- Identify medium-term support/resistance
- Spot potential reversal patterns

### Lower Timeframes (LTF)
- 15-Minute, 5-Minute, 1-Minute charts
- Fine-tune entry and exit points
- Identify short-term price patterns
- Higher noise, lower reliability

## The Top-Down Analysis Process

1. **Identify the Primary Trend (HTF)**
   - Determine if the market is in an uptrend, downtrend, or range
   - Mark major support/resistance levels and price zones
   - Note any significant chart patterns forming

2. **Confirm the Trend (ITF)**
   - Verify the trend aligns with the higher timeframe
   - Look for pullbacks or consolidations within the primary trend
   - Identify potential entry zones

3. **Execute Precise Entries (LTF)**
   - Wait for price action confirmation in your entry zone
   - Look for candlestick patterns, divergences, or indicator signals
   - Set precise stop-loss and take-profit levels

## Advanced Multi-Timeframe Strategies

### The 3-Timeframe Confluence Strategy
1. Identify trend direction on the daily chart
2. Find pullbacks to key levels on the 4-hour chart
3. Enter on reversal signals on the 15-minute chart

### The Timeframe Breakout Confirmation
1. Identify a breakout pattern on a higher timeframe
2. Wait for a retest of the breakout level on an intermediate timeframe
3. Enter when price action confirms support/resistance on a lower timeframe

### The Trend-Pullback-Continuation Strategy
1. Identify the trend on a weekly or daily chart
2. Find pullbacks to moving averages on the 4-hour chart
3. Enter when momentum indicators show continuation on the 1-hour chart

## Common Mistakes to Avoid

- **Timeframe Conflict**: Trading against the higher timeframe trend
- **Analysis Paralysis**: Overthinking due to conflicting signals across timeframes
- **Recency Bias**: Giving too much weight to lower timeframe signals
- **Inconsistent Application**: Switching between timeframes without a systematic approach

Multi-timeframe analysis requires discipline and practice but significantly improves trading decisions by providing context and precision that single-timeframe analysis cannot offer.
''',
        icon: Icons.stacked_line_chart,
        color: Colors.blue,
        type: PremiumContentType.tradingStrategies,
        requiresStrategyGuides: true,
      ),
      
      // Risk Management Systems
      PremiumContent(
        id: 'position_sizing',
        title: 'Advanced Position Sizing',
        description: 'Professional techniques for determining optimal trade size',
        content: '''
# Advanced Position Sizing

Position sizing is perhaps the most critical aspect of risk management in trading. While basic risk management suggests risking a fixed percentage per trade, advanced position sizing methods can significantly improve your risk-adjusted returns.

## Fixed Percentage Risk

The foundation of position sizing starts with the fixed percentage risk model:

```
Position Size = (Account Size × Risk Percentage) ÷ (Entry Price - Stop Loss Price)
```

Example: With a \$10,000 account, risking 1% per trade, and a \$1 stop loss:
```
Position Size = (\$10,000 × 0.01) ÷ \$1 = 100 units
```

## Advanced Position Sizing Methods

### 1. Volatility-Based Position Sizing

Instead of using a fixed dollar amount for your stop loss, adjust position size based on market volatility:

```
Position Size = (Account Size × Risk Percentage) ÷ (ATR × ATR Multiplier)
```

Where:
- ATR = Average True Range (typically 14-period)
- ATR Multiplier = Factor to determine stop distance (typically 2-3)

This method automatically reduces position size in volatile markets and increases it in calm markets.

### 2. Kelly Criterion

The Kelly formula optimizes position size based on your edge:

```
Kelly Percentage = W - [(1 - W) ÷ R]
```

Where:
- W = Winning percentage (decimal)
- R = Win/loss ratio (average win ÷ average loss)

Example: With a 60% win rate and 1.5 risk/reward ratio:
```
Kelly = 0.60 - [(1 - 0.60) ÷ 1.5] = 0.60 - 0.267 = 0.333 or 33.3%
```

Most professionals use a "Half Kelly" or "Quarter Kelly" approach to reduce drawdowns.

### 3. Optimal f

Developed by Ralph Vince, Optimal f calculates the position size that would have maximized returns over your trading history:

```
Optimal f = (TWR - 1) ÷ (Largest Losing Trade)
```

Where:
- TWR = Terminal Wealth Relative (ending equity ÷ starting equity)

### 4. Tiered Position Sizing

Increase position size as your account grows and decrease it during drawdowns:

- Base tier: 1% risk per trade (account at all-time high)
- Tier 1: 0.75% risk (account down 10% from peak)
- Tier 2: 0.5% risk (account down 20% from peak)
- Tier 3: 0.25% risk (account down 30% from peak)
- Tier 4: Stop trading (account down 40% from peak)

### 5. Correlation-Based Position Sizing

Adjust position size based on correlation between open positions:

```
Adjusted Risk = Base Risk × Correlation Factor
```

Where:
- Correlation Factor = 1 - (Average correlation between positions)

Example: If you have three positions with an average correlation of 0.7:
```
Adjusted Risk = 1% × (1 - 0.7) = 0.3%
```

## Implementation Guidelines

1. **Track Your Metrics**: Maintain detailed records of win rate, average win/loss, and maximum drawdown
2. **Start Conservative**: Begin with fixed percentage risk before advancing to complex methods
3. **Backtest Thoroughly**: Test position sizing models on historical data before live implementation
4. **Combine Methods**: Use different approaches for different market conditions
5. **Review Regularly**: Adjust your position sizing model as your trading performance evolves

Remember: The best position sizing method is one that you can apply consistently and that aligns with your psychological tolerance for drawdowns.
''',
        icon: Icons.balance,
        color: Colors.green,
        type: PremiumContentType.riskManagement,
        requiresStrategyGuides: true,
      ),
      
      // Market Psychology Mastery
      PremiumContent(
        id: 'trading_psychology',
        title: 'Trading Psychology Mastery',
        description: 'Mental frameworks and techniques used by professional traders',
        content: '''
# Trading Psychology Mastery

The difference between consistently profitable traders and those who struggle often comes down to psychology. While most traders focus on technical and fundamental analysis, mastering your mental game is equally important.

## Common Psychological Biases in Trading

### 1. Loss Aversion
- The tendency to feel losses more strongly than equivalent gains
- Leads to cutting winners too early and letting losers run
- **Solution**: Use predetermined exit points and automate exits when possible

### 2. Confirmation Bias
- Seeking information that confirms existing beliefs
- Ignoring contradictory evidence to your trade thesis
- **Solution**: Actively seek disconfirming evidence before entering trades

### 3. Recency Bias
- Overweighting recent events and underweighting historical patterns
- Chasing the latest "hot" setup or strategy
- **Solution**: Maintain a trading journal and regularly review long-term results

### 4. Anchoring
- Fixating on a specific price point or analysis
- Refusing to adjust when new information emerges
- **Solution**: Reassess your analysis when price breaks key levels

### 5. The Disposition Effect
- Tendency to sell winners too early and hold losers too long
- Driven by seeking pride and avoiding regret
- **Solution**: Follow a mechanical exit strategy for both winning and losing trades

## Building Mental Resilience

### 1. Developing a Trading Process
A robust trading process shifts focus from outcomes to execution:

1. **Pre-Market Routine**
   - Market overview and key levels identification
   - Review of economic calendar
   - Mental preparation exercises

2. **Trade Selection**
   - Systematic scanning for setups that meet your criteria
   - Multi-timeframe confirmation
   - Risk/reward assessment

3. **Trade Management**
   - Mechanical stop-loss placement
   - Predetermined exit strategy
   - Position sizing based on volatility

4. **Post-Trade Analysis**
   - Journaling regardless of outcome
   - Identifying process improvements
   - Emotional state assessment

### 2. Mindfulness Techniques for Traders

- **Focused Breathing**: 4-7-8 technique before making trading decisions
- **Metacognitive Awareness**: Observing your thoughts without judgment
- **Visualization**: Mentally rehearsing both successful trades and proper responses to losses
- **Meditation**: Regular practice to improve focus and emotional regulation

### 3. The Ideal Performance State

Elite traders operate in what psychologists call "the zone" or "flow state":

- Relaxed concentration
- Detachment from outcomes
- Present-moment awareness
- Absence of self-judgment

Triggers to enter this state:
- Consistent pre-trading routines
- Optimal challenge level (not too easy, not too hard)
- Clear goals and immediate feedback
- Elimination of distractions

## Advanced Psychological Frameworks

### 1. The Internal Scorecard
Evaluate yourself on process metrics rather than profit/loss:
- Adherence to trading plan
- Quality of analysis
- Emotional management
- Learning and adaptation

### 2. The Trading Tribe Approach
Developed by Ed Seykota, this approach involves:
- Acknowledging emotions rather than suppressing them
- Identifying the beliefs behind emotional responses
- Recognizing how these beliefs affect trading decisions
- Developing new, more resourceful beliefs

### 3. Cognitive Restructuring
Systematically identify and challenge negative thought patterns:
1. Identify automatic thoughts during trading
2. Recognize cognitive distortions
3. Challenge distorted thoughts with evidence
4. Replace with more balanced thinking

Remember: Trading psychology isn't about eliminating emotions but developing awareness and managing them effectively. The goal is not to feel good all the time but to make good decisions regardless of how you feel.
''',
        icon: Icons.psychology,
        color: Colors.orange,
        type: PremiumContentType.marketPsychology,
        requiresStrategyGuides: true,
      ),
      
      // Algorithmic Trading Basics
      PremiumContent(
        id: 'algo_trading_basics',
        title: 'Algorithmic Trading Basics',
        description: 'Introduction to creating and using trading algorithms',
        content: '''
# Algorithmic Trading Basics

Algorithmic trading uses computer programs to execute trades based on predefined rules. This approach removes emotional decision-making and allows for backtesting and optimization of strategies.

## Foundations of Algorithmic Trading

### Components of a Trading Algorithm

1. **Data Collection**
   - Price data (OHLCV)
   - Market indicators
   - Fundamental data
   - Alternative data sources

2. **Signal Generation**
   - Technical indicators
   - Pattern recognition
   - Statistical arbitrage
   - Machine learning models

3. **Risk Management**
   - Position sizing
   - Stop-loss mechanisms
   - Portfolio allocation
   - Correlation analysis

4. **Execution Logic**
   - Entry and exit conditions
   - Order types
   - Execution timing
   - Slippage handling

## Building Your First Trading Algorithm

### Step 1: Define Your Strategy
Start with a simple, rule-based strategy:

```
IF 50-day moving average crosses above 200-day moving average
AND RSI(14) is below 70
THEN buy 100 shares
SET stop-loss at 5% below entry price
SET take-profit at 15% above entry price
```

### Step 2: Backtesting
Test your strategy on historical data:

1. **Data Preparation**
   - Clean and normalize data
   - Handle missing values
   - Create training and testing periods

2. **Performance Metrics**
   - Total return
   - Sharpe ratio (risk-adjusted return)
   - Maximum drawdown
   - Win/loss ratio
   - Profit factor

3. **Optimization**
   - Parameter tuning (e.g., moving average periods)
   - Walk-forward analysis
   - Monte Carlo simulations

### Step 3: Implementation Tools

**Programming Languages:**
- Python (pandas, numpy, scikit-learn)
- R (quantmod, TTR)
- C++ (for high-frequency trading)

**Platforms and Libraries:**
- Backtrader (Python)
- QuantConnect
- MetaTrader with MQL
- TradingView with Pine Script

**Example Python Code (Simple Moving Average Crossover):**
```python
import pandas as pd
import numpy as np

# Load data
data = pd.read_csv('EURUSD.csv')

# Calculate indicators
data['SMA50'] = data['Close'].rolling(window=50).mean()
data['SMA200'] = data['Close'].rolling(window=200).mean()

# Generate signals
data['Signal'] = 0
data.loc[data['SMA50'] > data['SMA200'], 'Signal'] = 1
data.loc[data['SMA50'] < data['SMA200'], 'Signal'] = -1

# Calculate returns
data['Returns'] = data['Close'].pct_change()
data['Strategy'] = data['Signal'].shift(1) * data['Returns']

# Calculate cumulative returns
data['Cumulative_Returns'] = (1 + data['Strategy']).cumprod()

# Print performance
print(f"Total Return: {data['Cumulative_Returns'].iloc[-1]}")
print(f"Sharpe Ratio: {data['Strategy'].mean() / data['Strategy'].std() * np.sqrt(252)}")
```

## Advanced Concepts

### 1. Machine Learning in Algorithmic Trading
- Supervised learning for price prediction
- Reinforcement learning for optimal execution
- Feature engineering and selection
- Avoiding overfitting

### 2. High-Frequency Trading (HFT)
- Market microstructure
- Order book analysis
- Latency optimization
- Colocation services

### 3. Risk Management Systems
- Kelly criterion implementation
- Dynamic position sizing
- Correlation-based portfolio construction
- Volatility-adjusted stops

## Common Pitfalls and Solutions

1. **Overfitting**
   - Use out-of-sample testing
   - Implement cross-validation
   - Prefer simpler models

2. **Survivorship Bias**
   - Use point-in-time databases
   - Include delisted securities

3. **Look-Ahead Bias**
   - Ensure data availability at decision time
   - Implement proper time indexing

4. **Transaction Costs**
   - Include realistic commission models
   - Account for slippage
   - Consider market impact

Remember: Successful algorithmic trading requires a combination of market knowledge, programming skills, and statistical understanding. Start simple, focus on robust strategies, and gradually increase complexity as you gain experience.
''',
        icon: Icons.code,
        color: Colors.indigo,
        type: PremiumContentType.algorithmicTrading,
        requiresStrategyGuides: true,
      ),
      
      // Institutional Trading Insights
      PremiumContent(
        id: 'institutional_trading',
        title: 'Institutional Trading Insights',
        description: 'How professional institutions approach the markets',
        content: '''
# Institutional Trading Insights

Understanding how institutional traders operate gives retail traders a significant edge. Institutions move markets, and learning to identify their footprints can help you trade with the "smart money" rather than against it.

## The Institutional Landscape

### Types of Institutional Players

1. **Investment Banks**
   - Proprietary trading desks
   - Market making operations
   - Client order execution

2. **Hedge Funds**
   - Macro funds (global economic trends)
   - Quantitative funds (algorithm-driven)
   - Event-driven funds (mergers, earnings)
   - Long/short equity funds

3. **Asset Managers**
   - Mutual funds
   - Pension funds
   - Insurance companies

4. **Central Banks**
   - Monetary policy implementation
   - Currency intervention
   - Reserve management

## How Institutions Trade Differently

### 1. Order Execution
Institutions can't simply enter and exit positions with a single order due to their size:

- **Order Splitting**: Breaking large orders into smaller pieces
- **TWAP/VWAP Algorithms**: Time-weighted or volume-weighted average price execution
- **Iceberg Orders**: Showing only a small portion of the total order size
- **Dark Pools**: Private exchanges where orders are hidden from public view

### 2. Time Horizons
- **Position Holding Periods**: Days to months rather than minutes to hours
- **Overnight Exposure**: Willingness to hold positions through significant events
- **Strategic vs. Tactical**: Combination of long-term positioning and short-term adjustments

### 3. Risk Management
- **Portfolio Approach**: Trading correlated instruments as a basket
- **Hedging**: Using options, futures, or correlated assets to reduce risk
- **Stress Testing**: Preparing for extreme market scenarios
- **VaR Models**: Value-at-Risk calculations to quantify potential losses

## Identifying Institutional Activity

### 1. Volume Analysis
- **Volume Spikes**: Sudden increases in volume often indicate institutional activity
- **Volume Divergence**: Price moving with decreasing volume suggests lack of institutional interest
- **Volume Profile**: Identifying price levels with the highest traded volume

### 2. Order Flow Analysis
- **Absorption**: Large buy orders being absorbed without price rising (accumulation)
- **Stopping Volume**: Sharp reversal on high volume after a trend
- **Tape Reading**: Analyzing the sequence and size of trades

### 3. Market Microstructure
- **Bid-Ask Spread Widening/Narrowing**: Changes in liquidity conditions
- **Order Book Imbalances**: Significantly more orders on one side
- **Block Trades**: Large single transactions often reported separately

## Institutional Trading Strategies

### 1. Liquidity Provision
Institutions often act as liquidity providers, profiting from the bid-ask spread:
- Identifying when institutions are likely to provide liquidity
- Trading during institutional rebalancing periods (month-end, quarter-end)

### 2. Momentum Ignition
Institutions may push prices to trigger stop orders or algorithmic responses:
- Identifying potential stop-hunt levels
- Recognizing false breakouts

### 3. Smart Money Divergence
When price and institutional activity move in opposite directions:
- Declining prices with institutional accumulation (bullish)
- Rising prices with institutional distribution (bearish)

## Practical Applications for Retail Traders

### 1. Trading With Institutional Support
- Identify key support/resistance levels with high volume
- Look for signs of accumulation at support levels
- Enter with institutions after confirmation of their interest

### 2. Avoiding Institutional Traps
- Be cautious of low volume breakouts
- Watch for stop hunts before major moves
- Avoid trading against clear institutional positioning

### 3. Market Context Awareness
- Monitor institutional positioning reports (COT, 13F filings)
- Track institutional fund flows into different sectors
- Be aware of major option expiration dates and levels

Remember: While you can't compete with institutions in terms of resources or information, understanding their behavior allows you to align your trading with their activity rather than becoming their counterparty.
''',
        icon: Icons.business,
        color: Colors.teal,
        type: PremiumContentType.institutionalTrading,
        requiresStrategyGuides: true,
      ),
    ];
  }
}
