import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Type of support/resistance level
enum LevelType {
  /// Support level (price tends to bounce up from this level)
  support,

  /// Resistance level (price tends to bounce down from this level)
  resistance,

  /// Trend line (diagonal line showing price direction)
  trendLine,

  /// Fibon<PERSON>ci level
  fibonacci,

  /// Pivot level
  pivot,

  /// Psychological level (round numbers)
  psychological,

  /// Moving average level
  movingAverage,
}

/// Represents a support or resistance level on a chart
class SupportResistanceLevel extends Equatable {
  /// The price level where support/resistance occurs
  final double price;

  /// The type of level (support, resistance, trendLine, etc.)
  final LevelType type;

  /// Whether this is a strong level
  final bool isStrong;

  /// The start index in the candlestick list (for rendering)
  final int? startIndex;

  /// The end index in the candlestick list (for rendering)
  final int? endIndex;

  /// Optional strength indicator (1-10, with 10 being strongest)
  final int? strength;

  /// Optional label for the level
  final String? label;

  /// Optional start date for trend lines (required for trend lines)
  final DateTime? startDate;

  /// Optional end date for trend lines (required for trend lines)
  final DateTime? endDate;

  /// Optional start price for trend lines (required for trend lines)
  final double? startPrice;

  /// Optional end price for trend lines (required for trend lines)
  final double? endPrice;

  /// Optional color for the level line
  final Color? color;

  /// Creates a new support/resistance level
  const SupportResistanceLevel({
    required this.price,
    required this.type,
    this.isStrong = false,
    this.startIndex,
    this.endIndex,
    this.strength,
    this.label,
    this.startDate,
    this.endDate,
    this.startPrice,
    this.endPrice,
    this.color,
  }) : assert(
          (type != LevelType.trendLine) ||
              (startDate != null &&
                  endDate != null &&
                  startPrice != null &&
                  endPrice != null),
          'Trend lines require startDate, endDate, startPrice, and endPrice',
        );

  /// Creates a horizontal support level
  factory SupportResistanceLevel.support({
    required double price,
    bool isStrong = false,
    int? startIndex,
    int? endIndex,
    int? strength,
    String? label,
    Color? color,
  }) {
    return SupportResistanceLevel(
      price: price,
      type: LevelType.support,
      isStrong: isStrong,
      startIndex: startIndex,
      endIndex: endIndex,
      strength: strength,
      label: label,
      color: color ?? Colors.green,
    );
  }

  /// Creates a horizontal resistance level
  factory SupportResistanceLevel.resistance({
    required double price,
    bool isStrong = false,
    int? startIndex,
    int? endIndex,
    int? strength,
    String? label,
    Color? color,
  }) {
    return SupportResistanceLevel(
      price: price,
      type: LevelType.resistance,
      isStrong: isStrong,
      startIndex: startIndex,
      endIndex: endIndex,
      strength: strength,
      label: label,
      color: color ?? Colors.red,
    );
  }

  /// Creates a diagonal trend line
  factory SupportResistanceLevel.trendLine({
    required DateTime startDate,
    required DateTime endDate,
    required double startPrice,
    required double endPrice,
    bool isStrong = false,
    int? startIndex,
    int? endIndex,
    int? strength,
    String? label,
    Color? color,
  }) {
    return SupportResistanceLevel(
      price: 0, // Not used for trend lines
      type: LevelType.trendLine,
      isStrong: isStrong,
      startIndex: startIndex,
      endIndex: endIndex,
      strength: strength,
      label: label,
      startDate: startDate,
      endDate: endDate,
      startPrice: startPrice,
      endPrice: endPrice,
      color: color ?? Colors.blue,
    );
  }

  /// Creates a Fibonacci level
  factory SupportResistanceLevel.fibonacci({
    required double price,
    required double fibLevel,
    bool isStrong = false,
    int? startIndex,
    int? endIndex,
    int? strength,
    String? label,
    Color? color,
  }) {
    // Determine color based on Fibonacci level
    Color levelColor = color ?? Colors.purple;
    switch (fibLevel) {
      case 0.236:
        levelColor = color ?? Colors.purple.shade300;
        break;
      case 0.382:
        levelColor = color ?? Colors.purple.shade400;
        break;
      case 0.5:
        levelColor = color ?? Colors.purple.shade500;
        break;
      case 0.618:
        levelColor = color ?? Colors.purple.shade600;
        break;
      case 0.786:
        levelColor = color ?? Colors.purple.shade700;
        break;
      case 1.0:
        levelColor = color ?? Colors.purple.shade800;
        break;
    }

    return SupportResistanceLevel(
      price: price,
      type: LevelType.fibonacci,
      isStrong: isStrong || fibLevel == 0.618 || fibLevel == 0.5,
      startIndex: startIndex,
      endIndex: endIndex,
      strength: strength,
      label: label ?? 'Fib ${fibLevel.toStringAsFixed(3)}',
      color: levelColor,
    );
  }

  /// Creates a pivot level
  factory SupportResistanceLevel.pivot({
    required double price,
    int? startIndex,
    int? endIndex,
    int? strength,
    String? label,
    Color? color,
  }) {
    return SupportResistanceLevel(
      price: price,
      type: LevelType.pivot,
      isStrong: true,
      startIndex: startIndex,
      endIndex: endIndex,
      strength: strength ?? 10,
      label: label ?? 'Pivot',
      color: color ?? Colors.blue,
    );
  }

  /// Creates a psychological level
  factory SupportResistanceLevel.psychological({
    required double price,
    int? startIndex,
    int? endIndex,
    int? strength,
    String? label,
    Color? color,
  }) {
    return SupportResistanceLevel(
      price: price,
      type: LevelType.psychological,
      isStrong: true,
      startIndex: startIndex,
      endIndex: endIndex,
      strength: strength ?? 8,
      label: label ?? 'Psychological',
      color: color ?? Colors.orange,
    );
  }

  /// Creates a moving average level
  factory SupportResistanceLevel.movingAverage({
    required double price,
    required int period,
    int? startIndex,
    int? endIndex,
    int? strength,
    String? label,
    Color? color,
  }) {
    // Determine color based on moving average period
    Color maColor;
    if (color != null) {
      maColor = color;
    } else if (period <= 20) {
      maColor = Colors.blue;
    } else if (period <= 50) {
      maColor = Colors.purple;
    } else if (period <= 100) {
      maColor = Colors.orange;
    } else {
      maColor = Colors.red;
    }

    return SupportResistanceLevel(
      price: price,
      type: LevelType.movingAverage,
      isStrong: period == 200 || period == 50,
      startIndex: startIndex,
      endIndex: endIndex,
      strength: strength ?? (period == 200 ? 9 : period == 50 ? 7 : 5),
      label: label ?? 'MA $period',
      color: maColor,
    );
  }

  @override
  List<Object?> get props => [
        price,
        type,
        isStrong,
        startIndex,
        endIndex,
        strength,
        label,
        startDate,
        endDate,
        startPrice,
        endPrice,
        color,
      ];

  @override
  String toString() {
    return 'SupportResistanceLevel(price: $price, type: $type, isStrong: $isStrong, strength: $strength, label: $label)';
  }
}
