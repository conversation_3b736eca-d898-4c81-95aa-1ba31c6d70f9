import 'package:equatable/equatable.dart';

/// Represents a single candlestick with OHLC (Open, High, Low, Close) data.
class CandlestickData extends Equatable {
  /// The opening price of the candlestick
  final double open;
  
  /// The highest price during the period
  final double high;
  
  /// The lowest price during the period
  final double low;
  
  /// The closing price of the candlestick
  final double close;
  
  /// The date/time of the candlestick
  final DateTime date;
  
  /// Optional volume data
  final double? volume;

  /// Creates a new candlestick data instance
  const CandlestickData({
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.date,
    this.volume,
  });

  /// Whether this candlestick is bullish (close > open)
  bool get isBullish => close > open;

  /// Whether this candlestick is bearish (close < open)
  bool get isBearish => close < open;

  /// Whether this candlestick is neutral (close == open)
  bool get isNeutral => close == open;

  /// The body height of the candlestick (absolute difference between open and close)
  double get bodyHeight => (close - open).abs();

  /// The upper wick height (difference between high and the higher of open/close)
  double get upperWickHeight => high - (open > close ? open : close);

  /// The lower wick height (difference between the lower of open/close and low)
  double get lowerWickHeight => (open < close ? open : close) - low;

  /// The total height of the candlestick (high - low)
  double get totalHeight => high - low;

  /// Creates a copy of this candlestick with the given fields replaced with new values
  CandlestickData copyWith({
    double? open,
    double? high,
    double? low,
    double? close,
    DateTime? date,
    double? volume,
  }) {
    return CandlestickData(
      open: open ?? this.open,
      high: high ?? this.high,
      low: low ?? this.low,
      close: close ?? this.close,
      date: date ?? this.date,
      volume: volume ?? this.volume,
    );
  }

  @override
  List<Object?> get props => [open, high, low, close, date, volume];

  @override
  String toString() {
    return 'CandlestickData(date: $date, open: $open, high: $high, low: $low, close: $close, volume: $volume)';
  }
}
