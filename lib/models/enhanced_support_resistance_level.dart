import 'package:flutter/material.dart';

/// Represents a support or resistance level on a chart
class EnhancedSupportResistanceLevel {
  /// The price level
  final double price;
  
  /// Whether this is a support level (true) or resistance level (false)
  final bool isSupport;
  
  /// Whether this is a strong level
  final bool isStrong;
  
  /// The start index in the candlestick list
  final int startIndex;
  
  /// The end index in the candlestick list
  final int endIndex;
  
  /// The color of the level
  final Color? color;
  
  /// The label for the level
  final String? label;
  
  /// Creates a new support or resistance level
  const EnhancedSupportResistanceLevel({
    required this.price,
    required this.isSupport,
    this.isStrong = false,
    required this.startIndex,
    required this.endIndex,
    this.color,
    this.label,
  });
  
  /// Creates a support level
  factory EnhancedSupportResistanceLevel.support({
    required double price,
    bool isStrong = false,
    required int startIndex,
    required int endIndex,
    Color color = Colors.green,
    String? label,
  }) {
    return EnhancedSupportResistanceLevel(
      price: price,
      isSupport: true,
      isStrong: isStrong,
      startIndex: startIndex,
      endIndex: endIndex,
      color: color,
      label: label ?? 'Support',
    );
  }
  
  /// Creates a resistance level
  factory EnhancedSupportResistanceLevel.resistance({
    required double price,
    bool isStrong = false,
    required int startIndex,
    required int endIndex,
    Color color = Colors.red,
    String? label,
  }) {
    return EnhancedSupportResistanceLevel(
      price: price,
      isSupport: false,
      isStrong: isStrong,
      startIndex: startIndex,
      endIndex: endIndex,
      color: color,
      label: label ?? 'Resistance',
    );
  }
  
  /// Creates a pivot level
  factory EnhancedSupportResistanceLevel.pivot({
    required double price,
    required int startIndex,
    required int endIndex,
    Color color = Colors.blue,
    String? label,
  }) {
    return EnhancedSupportResistanceLevel(
      price: price,
      isSupport: true,
      isStrong: true,
      startIndex: startIndex,
      endIndex: endIndex,
      color: color,
      label: label ?? 'Pivot',
    );
  }
  
  /// Creates a Fibonacci level
  factory EnhancedSupportResistanceLevel.fibonacci({
    required double price,
    required double fibLevel,
    required int startIndex,
    required int endIndex,
    Color? color,
    String? label,
  }) {
    // Determine color based on Fibonacci level
    Color levelColor = color ?? Colors.purple;
    switch (fibLevel) {
      case 0.236:
        levelColor = color ?? Colors.purple.shade300;
        break;
      case 0.382:
        levelColor = color ?? Colors.purple.shade400;
        break;
      case 0.5:
        levelColor = color ?? Colors.purple.shade500;
        break;
      case 0.618:
        levelColor = color ?? Colors.purple.shade600;
        break;
      case 0.786:
        levelColor = color ?? Colors.purple.shade700;
        break;
      case 1.0:
        levelColor = color ?? Colors.purple.shade800;
        break;
    }
    
    return EnhancedSupportResistanceLevel(
      price: price,
      isSupport: true,
      isStrong: fibLevel == 0.618 || fibLevel == 0.5,
      startIndex: startIndex,
      endIndex: endIndex,
      color: levelColor,
      label: label ?? 'Fib ${fibLevel.toStringAsFixed(3)}',
    );
  }
  
  /// Creates a psychological level
  factory EnhancedSupportResistanceLevel.psychological({
    required double price,
    required int startIndex,
    required int endIndex,
    Color color = Colors.orange,
    String? label,
  }) {
    return EnhancedSupportResistanceLevel(
      price: price,
      isSupport: true,
      isStrong: true,
      startIndex: startIndex,
      endIndex: endIndex,
      color: color,
      label: label ?? 'Psychological',
    );
  }
  
  /// Creates a trend line level
  factory EnhancedSupportResistanceLevel.trendLine({
    required double price,
    required bool isSupport,
    required int startIndex,
    required int endIndex,
    Color? color,
    String? label,
  }) {
    return EnhancedSupportResistanceLevel(
      price: price,
      isSupport: isSupport,
      isStrong: false,
      startIndex: startIndex,
      endIndex: endIndex,
      color: color ?? (isSupport ? Colors.green : Colors.red),
      label: label ?? (isSupport ? 'Trend Support' : 'Trend Resistance'),
    );
  }
  
  /// Creates a moving average level
  factory EnhancedSupportResistanceLevel.movingAverage({
    required double price,
    required int period,
    required int startIndex,
    required int endIndex,
    Color? color,
    String? label,
  }) {
    // Determine color based on moving average period
    Color maColor;
    if (color != null) {
      maColor = color;
    } else if (period <= 20) {
      maColor = Colors.blue;
    } else if (period <= 50) {
      maColor = Colors.purple;
    } else if (period <= 100) {
      maColor = Colors.orange;
    } else {
      maColor = Colors.red;
    }
    
    return EnhancedSupportResistanceLevel(
      price: price,
      isSupport: true,
      isStrong: period == 200 || period == 50,
      startIndex: startIndex,
      endIndex: endIndex,
      color: maColor,
      label: label ?? 'MA $period',
    );
  }
}
