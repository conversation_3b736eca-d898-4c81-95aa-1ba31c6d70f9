import 'package:equatable/equatable.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';
import 'package:uuid/uuid.dart';

/// Represents a quiz question about chart patterns
class QuizQuestion extends Equatable {
  /// Unique identifier for the question
  final String id;
  
  /// The candlestick data to display for the question
  final List<CandlestickData> candlesticks;
  
  /// Support and resistance levels to display (optional)
  final List<SupportResistanceLevel>? supportResistanceLevels;
  
  /// The correct pattern ID that matches the candlesticks
  final String correctPatternId;
  
  /// List of pattern IDs to offer as options (including the correct one)
  final List<String> optionPatternIds;
  
  /// Explanation of why the correct pattern is correct
  final String explanation;
  
  /// Difficulty level of the question (1-5, with 5 being hardest)
  final int difficulty;

  /// Creates a new quiz question
  QuizQuestion({
    String? id,
    required this.candlesticks,
    this.supportResistanceLevels,
    required this.correctPatternId,
    required this.optionPatternIds,
    required this.explanation,
    this.difficulty = 3,
  }) : id = id ?? const Uuid().v4(),
       assert(
         optionPatternIds.contains(correctPatternId),
         'Options must include the correct pattern ID',
       ),
       assert(
         optionPatternIds.length >= 2,
         'Must provide at least 2 options',
       );

  /// Creates a copy of this question with the given fields replaced with new values
  QuizQuestion copyWith({
    String? id,
    List<CandlestickData>? candlesticks,
    List<SupportResistanceLevel>? supportResistanceLevels,
    String? correctPatternId,
    List<String>? optionPatternIds,
    String? explanation,
    int? difficulty,
  }) {
    return QuizQuestion(
      id: id ?? this.id,
      candlesticks: candlesticks ?? this.candlesticks,
      supportResistanceLevels: supportResistanceLevels ?? this.supportResistanceLevels,
      correctPatternId: correctPatternId ?? this.correctPatternId,
      optionPatternIds: optionPatternIds ?? this.optionPatternIds,
      explanation: explanation ?? this.explanation,
      difficulty: difficulty ?? this.difficulty,
    );
  }

  @override
  List<Object?> get props => [
        id,
        candlesticks,
        supportResistanceLevels,
        correctPatternId,
        optionPatternIds,
        explanation,
        difficulty,
      ];

  @override
  String toString() {
    return 'QuizQuestion(id: $id, correctPatternId: $correctPatternId, difficulty: $difficulty)';
  }
}
