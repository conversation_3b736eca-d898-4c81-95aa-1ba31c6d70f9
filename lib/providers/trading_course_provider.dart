import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/data/crypto_course_data.dart';
import 'package:learn_chart_patterns/data/forex_course_data.dart';
import 'package:learn_chart_patterns/models/trading_course.dart';
import 'package:learn_chart_patterns/services/storage_service.dart';

/// Provider for trading courses
class TradingCourseProvider extends ChangeNotifier {
  /// Storage service
  final StorageService _storageService;

  /// Available courses
  final List<TradingCourse> _courses = [];

  /// Completion status for lessons
  final Map<String, bool> _completionStatus = {};

  /// Whether courses are loading
  bool _isLoading = true;

  /// Creates a new trading course provider
  TradingCourseProvider(this._storageService);

  /// Initialize the provider
  Future<void> init() async {
    _isLoading = true;
    notifyListeners();

    // Load completion status first (fast operation)
    await _loadCompletionStatus();

    // Load courses in the background
    Future.microtask(() {
      _loadCourses();
      _isLoading = false;
      notifyListeners();
    });
  }

  /// Load courses
  void _loadCourses() {
    // Load forex courses
    _courses.addAll(ForexCourseData.getCourses());

    // Load crypto courses
    _courses.addAll(CryptoCourseData.getCourses());
  }

  /// Load completion status
  Future<void> _loadCompletionStatus() async {
    final Map<String, dynamic>? data = await _storageService.getMap('lesson_completion');

    if (data != null) {
      data.forEach((key, value) {
        if (value is bool) {
          _completionStatus[key] = value;
        }
      });
    }
  }

  /// Save completion status
  Future<void> _saveCompletionStatus() async {
    await _storageService.setMap('lesson_completion', _completionStatus);
  }

  /// Mark a lesson as completed
  Future<void> markLessonCompleted(String lessonId) async {
    _completionStatus[lessonId] = true;
    await _saveCompletionStatus();
    notifyListeners();
  }

  /// Mark a lesson as not completed
  Future<void> markLessonNotCompleted(String lessonId) async {
    _completionStatus[lessonId] = false;
    await _saveCompletionStatus();
    notifyListeners();
  }

  /// Reset progress for a course
  Future<void> resetCourseProgress(String courseId) async {
    final course = getCourseById(courseId);
    if (course == null) return;

    for (final module in course.modules) {
      for (final lesson in module.lessons) {
        _completionStatus[lesson.id] = false;
      }
    }

    await _saveCompletionStatus();
    notifyListeners();
  }

  /// Get all courses
  List<TradingCourse> get courses => _courses;

  /// Get forex courses
  List<TradingCourse> get forexCourses =>
      _courses.where((c) => c.type == CourseType.forex).toList();

  /// Get crypto courses
  List<TradingCourse> get cryptoCourses =>
      _courses.where((c) => c.type == CourseType.crypto).toList();

  /// Get beginner courses
  List<TradingCourse> get beginnerCourses =>
      _courses.where((c) => c.difficulty == DifficultyLevel.beginner).toList();

  /// Get intermediate courses
  List<TradingCourse> get intermediateCourses =>
      _courses.where((c) => c.difficulty == DifficultyLevel.intermediate).toList();

  /// Get advanced courses
  List<TradingCourse> get advancedCourses =>
      _courses.where((c) => c.difficulty == DifficultyLevel.advanced).toList();

  /// Get premium courses
  List<TradingCourse> get premiumCourses =>
      _courses.where((c) => c.isPremium).toList();

  /// Get free courses
  List<TradingCourse> get freeCourses =>
      _courses.where((c) => !c.isPremium).toList();

  /// Get a course by ID
  TradingCourse? getCourseById(String id) {
    try {
      return _courses.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Check if a lesson is completed
  bool isLessonCompleted(String lessonId) {
    return _completionStatus[lessonId] == true;
  }

  /// Get completion status
  Map<String, bool> get completionStatus => _completionStatus;

  /// Get whether courses are loading
  bool get isLoading => _isLoading;


}
