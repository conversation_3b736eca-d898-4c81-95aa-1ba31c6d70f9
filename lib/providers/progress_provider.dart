import 'package:flutter/foundation.dart';
import 'package:learn_chart_patterns/models/user_progress.dart';
import 'package:learn_chart_patterns/services/progress_service.dart';

/// Provider for managing user progress
class ProgressProvider extends ChangeNotifier {
  /// Progress service for persisting progress
  final ProgressService _progressService;
  
  /// Current user progress
  UserProgress get progress => _progressService.progress;

  /// Creates a new progress provider
  ProgressProvider(this._progressService);

  /// Marks a pattern as learned
  Future<void> markPatternAsLearned(String patternId) async {
    await _progressService.markPatternAsLearned(patternId);
    notifyListeners();
  }

  /// Records a quiz result
  Future<void> recordQuizResult(String patternId, bool isCorrect) async {
    await _progressService.recordQuizResult(patternId, isCorrect);
    notifyListeners();
  }

  /// Updates the streak based on the current date
  Future<void> updateStreak() async {
    await _progressService.updateStreak();
    notifyListeners();
  }

  /// Resets all progress
  Future<void> resetProgress() async {
    await _progressService.resetProgress();
    notifyListeners();
  }

  /// Checks if a pattern has been learned
  bool isPatternLearned(String patternId) {
    return progress.learnedPatterns[patternId] ?? false;
  }

  /// Gets the accuracy for a pattern
  double getPatternAccuracy(String patternId) {
    return progress.quizAccuracy[patternId] ?? 0.0;
  }

  /// Gets the overall completion percentage
  double get completionPercentage => progress.completionPercentage;

  /// Gets the overall quiz accuracy
  double get overallQuizAccuracy => progress.overallQuizAccuracy;

  /// Gets the current streak
  int get currentStreak => progress.currentStreak;

  /// Gets the highest streak
  int get highestStreak => progress.highestStreak;

  /// Gets the last practice date
  DateTime? get lastPracticeDate => progress.lastPracticeDate;

  /// Gets the total number of quizzes completed
  int get totalQuizzesCompleted => progress.totalQuizzesCompleted;

  /// Gets the total number of correct answers
  int get totalCorrectAnswers => progress.totalCorrectAnswers;

  /// Gets whether the user has a current streak
  bool get hasStreak => progress.hasStreak;
}
