import 'package:flutter/foundation.dart';
import 'package:learn_chart_patterns/services/notification_service.dart';

/// Provider for managing notifications
class NotificationProvider extends ChangeNotifier {
  /// Notification service for managing notifications
  final NotificationService _notificationService;

  /// Creates a new notification provider
  NotificationProvider(this._notificationService);

  /// Gets whether notifications are enabled
  bool get notificationsEnabled => _notificationService.notificationsEnabled;

  /// Gets the notification hour
  int get notificationHour => _notificationService.notificationHour;

  /// Gets the notification minute
  int get notificationMinute => _notificationService.notificationMinute;

  /// Requests notification permissions
  Future<bool> requestPermissions() async {
    return _notificationService.requestPermissions();
  }

  /// Enables or disables notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    await _notificationService.setNotificationsEnabled(enabled);
    notifyListeners();
  }

  /// Sets the notification time
  Future<void> setNotificationTime(int hour, int minute) async {
    await _notificationService.setNotificationTime(hour, minute);
    notifyListeners();
  }

  /// Schedules a notification
  Future<void> scheduleNotification() async {
    await _notificationService.scheduleNotification();
  }
}
