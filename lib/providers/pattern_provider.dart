import 'package:flutter/foundation.dart';
import 'package:learn_chart_patterns/constants/pattern_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/quiz_question.dart';

/// Provider for managing chart patterns
class PatternProvider extends ChangeNotifier {
  /// Gets all chart patterns
  List<ChartPattern> get allPatterns => PatternData.allPatterns;

  /// Gets a pattern by ID
  ChartPattern getPatternById(String id) {
    return allPatterns.firstWhere(
      (pattern) => pattern.id == id,
      orElse: () => throw Exception('Pattern not found: $id'),
    );
  }

  /// Gets patterns by trend
  List<ChartPattern> getPatternsByTrend(PatternTrend trend) {
    return allPatterns.where((pattern) => pattern.trend == trend).toList();
  }

  /// Gets patterns by difficulty
  List<ChartPattern> getPatternsByDifficulty(PatternDifficulty difficulty) {
    return allPatterns.where((pattern) => pattern.difficulty == difficulty).toList();
  }

  /// Gets beginner patterns
  List<ChartPattern> get beginnerPatterns => getPatternsByDifficulty(PatternDifficulty.beginner);

  /// Gets intermediate patterns
  List<ChartPattern> get intermediatePatterns => getPatternsByDifficulty(PatternDifficulty.intermediate);

  /// Gets advanced patterns
  List<ChartPattern> get advancedPatterns => getPatternsByDifficulty(PatternDifficulty.advanced);

  /// Gets bullish patterns
  List<ChartPattern> get bullishPatterns => getPatternsByTrend(PatternTrend.bullish);

  /// Gets bearish patterns
  List<ChartPattern> get bearishPatterns => getPatternsByTrend(PatternTrend.bearish);

  /// Gets neutral patterns
  List<ChartPattern> get neutralPatterns => getPatternsByTrend(PatternTrend.neutral);

  /// Generates a random quiz question
  QuizQuestion generateRandomQuestion({
    int optionCount = 4,
    int difficulty = 3,
    List<String>? excludePatternIds,
  }) {
    // This is a placeholder. The actual implementation will be in the quiz generator.
    // For now, we'll return a dummy question.
    final correctPattern = allPatterns[0];
    final options = allPatterns.take(optionCount).map((p) => p.id).toList();
    
    return QuizQuestion(
      candlesticks: [],
      correctPatternId: correctPattern.id,
      optionPatternIds: options,
      explanation: 'This is a placeholder explanation.',
      difficulty: difficulty,
    );
  }
}
