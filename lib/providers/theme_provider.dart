import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/services/storage_service.dart';
import 'package:learn_chart_patterns/themes/app_theme.dart';

/// Provider for managing app theme
class ThemeProvider extends ChangeNotifier {
  /// Current theme mode
  ThemeMode _themeMode = ThemeMode.system;
  
  /// Storage service for persisting theme preference
  final StorageService _storageService;

  /// Creates a new theme provider
  ThemeProvider(this._storageService) {
    _loadThemeMode();
  }

  /// Gets the current theme mode
  ThemeMode get themeMode => _themeMode;

  /// Gets the current theme data based on the theme mode and platform brightness
  ThemeData getTheme(Brightness platformBrightness) {
    final isDark = _themeMode == ThemeMode.dark ||
        (_themeMode == ThemeMode.system && platformBrightness == Brightness.dark);
    return isDark ? AppTheme.darkTheme() : AppTheme.lightTheme();
  }

  /// Gets the current chart colors based on the theme mode and platform brightness
  ChartColors getChartColors(Brightness platformBrightness) {
    final isDark = _themeMode == ThemeMode.dark ||
        (_themeMode == ThemeMode.system && platformBrightness == Brightness.dark);
    return isDark ? AppTheme.darkChartColors() : AppTheme.lightChartColors();
  }

  /// Sets the theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    notifyListeners();
    
    await _saveThemeMode();
  }

  /// Toggles between light and dark theme
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      await setThemeMode(ThemeMode.dark);
    } else if (_themeMode == ThemeMode.dark) {
      await setThemeMode(ThemeMode.light);
    } else {
      // If system, check the current brightness and toggle to the opposite
      final currentBrightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
      if (currentBrightness == Brightness.dark) {
        await setThemeMode(ThemeMode.light);
      } else {
        await setThemeMode(ThemeMode.dark);
      }
    }
  }

  /// Loads the theme mode from storage
  Future<void> _loadThemeMode() async {
    final themeModeString = await _storageService.getString(AppConstants.prefKeyThemeMode);
    
    if (themeModeString != null) {
      switch (themeModeString) {
        case 'light':
          _themeMode = ThemeMode.light;
          break;
        case 'dark':
          _themeMode = ThemeMode.dark;
          break;
        default:
          _themeMode = ThemeMode.system;
      }
      
      notifyListeners();
    }
  }

  /// Saves the theme mode to storage
  Future<void> _saveThemeMode() async {
    String themeModeString;
    
    switch (_themeMode) {
      case ThemeMode.light:
        themeModeString = 'light';
        break;
      case ThemeMode.dark:
        themeModeString = 'dark';
        break;
      default:
        themeModeString = 'system';
    }
    
    await _storageService.setString(AppConstants.prefKeyThemeMode, themeModeString);
  }
}
