import 'package:flutter/foundation.dart';
import 'package:learn_chart_patterns/models/learning_progression.dart';
import 'package:learn_chart_patterns/services/pattern_tutorial_service.dart';

/// Provider for managing tutorial state and learning progression
class TutorialProvider extends ChangeNotifier {
  /// Current learning progression
  LearningProgression _progression = LearningProgression.defaultProgression();
  
  /// Current learning level
  LearningLevel _currentLevel = LearningLevel.beginner;
  
  /// Pattern learning stages
  final Map<String, PatternLearningStage> _patternProgress = {};
  
  /// Pattern accuracies
  final Map<String, double> _patternAccuracies = {};
  
  /// Current tutorial (if any)
  PatternTutorial? _currentTutorial;
  
  /// Whether tutorial mode is enabled
  bool _tutorialModeEnabled = true;
  
  /// Tutorial service for generating tutorials
  final PatternTutorialService _tutorialService = PatternTutorialService();

  // Getters
  LearningProgression get progression => _progression;
  LearningLevel get currentLevel => _currentLevel;
  Map<String, PatternLearningStage> get patternProgress => Map.unmodifiable(_patternProgress);
  Map<String, double> get patternAccuracies => Map.unmodifiable(_patternAccuracies);
  PatternTutorial? get currentTutorial => _currentTutorial;
  bool get tutorialModeEnabled => _tutorialModeEnabled;
  bool get hasTutorialInProgress => _currentTutorial != null && !_currentTutorial!.isCompleted;

  /// Gets patterns available for the current learning level
  List<String> get availablePatterns => _progression.getAvailablePatterns(_currentLevel);

  /// Gets the next pattern to learn
  String? get nextPatternToLearn => _progression.getNextPattern(_patternProgress);

  /// Gets patterns that need review
  List<String> get patternsNeedingReview => _progression.getPatternsNeedingReview(_patternProgress);

  /// Gets overall learning progress (0.0 to 1.0)
  double get overallProgress => _progression.calculateProgress(_patternProgress);

  /// Gets the learning stage for a specific pattern
  PatternLearningStage getPatternStage(String patternId) {
    return _patternProgress[patternId] ?? PatternLearningStage.not_started;
  }

  /// Gets the accuracy for a specific pattern
  double getPatternAccuracy(String patternId) {
    return _patternAccuracies[patternId] ?? 0.0;
  }

  /// Checks if a pattern is available for learning
  bool isPatternAvailable(String patternId) {
    return availablePatterns.contains(patternId);
  }

  /// Checks if a pattern is mastered
  bool isPatternMastered(String patternId) {
    return getPatternStage(patternId) == PatternLearningStage.mastered;
  }

  /// Updates the learning level
  void updateLearningLevel(LearningLevel level) {
    if (_currentLevel != level) {
      _currentLevel = level;
      notifyListeners();
    }
  }

  /// Updates pattern accuracy and learning stage
  void updatePatternAccuracy(String patternId, double accuracy) {
    _patternAccuracies[patternId] = accuracy;
    
    // Update learning stage based on accuracy
    final currentStage = getPatternStage(patternId);
    PatternLearningStage newStage = currentStage;
    
    if (accuracy >= _progression.masteryThreshold) {
      newStage = PatternLearningStage.mastered;
    } else if (accuracy >= 0.6) {
      newStage = PatternLearningStage.practice;
    } else if (accuracy < 0.4 && currentStage != PatternLearningStage.not_started) {
      newStage = PatternLearningStage.needs_review;
    }
    
    if (newStage != currentStage) {
      _patternProgress[patternId] = newStage;
    }
    
    // Check if learning level should be updated
    final suggestedLevel = _progression.suggestLearningLevel(_patternAccuracies);
    if (suggestedLevel != _currentLevel) {
      updateLearningLevel(suggestedLevel);
    }
    
    notifyListeners();
  }

  /// Starts a tutorial for a specific pattern
  Future<void> startPatternTutorial(String patternId) async {
    if (!isPatternAvailable(patternId)) {
      throw Exception('Pattern $patternId is not available for current learning level');
    }
    
    // Mark pattern as in introduction stage
    _patternProgress[patternId] = PatternLearningStage.introduction;
    
    // Generate tutorial steps
    final tutorial = await _tutorialService.generateTutorial(patternId);
    _currentTutorial = tutorial;
    
    notifyListeners();
  }

  /// Advances to the next tutorial step
  void nextTutorialStep() {
    if (_currentTutorial == null) return;
    
    final nextIndex = _currentTutorial!.currentStepIndex + 1;
    
    if (nextIndex >= _currentTutorial!.steps.length) {
      // Tutorial completed
      _currentTutorial = _currentTutorial!.copyWith(
        isCompleted: true,
      );
      
      // Update pattern stage to practice
      final patternId = _currentTutorial!.patternId;
      _patternProgress[patternId] = PatternLearningStage.practice;
    } else {
      // Move to next step
      _currentTutorial = _currentTutorial!.copyWith(
        currentStepIndex: nextIndex,
      );
    }
    
    notifyListeners();
  }

  /// Goes back to the previous tutorial step
  void previousTutorialStep() {
    if (_currentTutorial == null) return;
    
    final prevIndex = _currentTutorial!.currentStepIndex - 1;
    
    if (prevIndex >= 0) {
      _currentTutorial = _currentTutorial!.copyWith(
        currentStepIndex: prevIndex,
        isCompleted: false,
      );
      notifyListeners();
    }
  }

  /// Completes the current tutorial step
  void completeTutorialStep() {
    if (_currentTutorial?.currentStep == null) return;
    
    final currentStep = _currentTutorial!.currentStep!;
    final updatedStep = currentStep.copyWith(isCompleted: true);
    
    final updatedSteps = List<TutorialStep>.from(_currentTutorial!.steps);
    updatedSteps[_currentTutorial!.currentStepIndex] = updatedStep;
    
    _currentTutorial = _currentTutorial!.copyWith(steps: updatedSteps);
    notifyListeners();
  }

  /// Exits the current tutorial
  void exitTutorial() {
    _currentTutorial = null;
    notifyListeners();
  }

  /// Enables or disables tutorial mode
  void setTutorialMode(bool enabled) {
    if (_tutorialModeEnabled != enabled) {
      _tutorialModeEnabled = enabled;
      notifyListeners();
    }
  }

  /// Resets a pattern's learning progress
  void resetPatternProgress(String patternId) {
    _patternProgress.remove(patternId);
    _patternAccuracies.remove(patternId);
    notifyListeners();
  }

  /// Resets all learning progress
  void resetAllProgress() {
    _patternProgress.clear();
    _patternAccuracies.clear();
    _currentLevel = LearningLevel.beginner;
    _currentTutorial = null;
    notifyListeners();
  }

  /// Gets recommended patterns for practice
  List<String> getRecommendedPatterns() {
    final recommendations = <String>[];
    
    // Add patterns that need review first
    recommendations.addAll(patternsNeedingReview);
    
    // Add the next pattern to learn
    final nextPattern = nextPatternToLearn;
    if (nextPattern != null && !recommendations.contains(nextPattern)) {
      recommendations.add(nextPattern);
    }
    
    // Add other available patterns that aren't mastered
    for (final pattern in availablePatterns) {
      if (!isPatternMastered(pattern) && !recommendations.contains(pattern)) {
        recommendations.add(pattern);
      }
    }
    
    return recommendations;
  }

  /// Gets learning statistics
  Map<String, dynamic> getLearningStats() {
    final availableCount = availablePatterns.length;
    final masteredCount = availablePatterns.where(isPatternMastered).length;
    final inProgressCount = availablePatterns
        .where((p) => getPatternStage(p) != PatternLearningStage.not_started && !isPatternMastered(p))
        .length;
    final needReviewCount = patternsNeedingReview.length;
    
    return {
      'totalAvailable': availableCount,
      'mastered': masteredCount,
      'inProgress': inProgressCount,
      'needReview': needReviewCount,
      'overallProgress': overallProgress,
      'currentLevel': _currentLevel.name,
    };
  }

  /// Loads learning progress from storage
  void loadProgress(Map<String, dynamic> data) {
    try {
      // Load learning level
      final levelName = data['currentLevel'] as String?;
      if (levelName != null) {
        _currentLevel = LearningLevel.values.firstWhere(
          (level) => level.name == levelName,
          orElse: () => LearningLevel.beginner,
        );
      }
      
      // Load pattern progress
      final progressData = data['patternProgress'] as Map<String, dynamic>?;
      if (progressData != null) {
        _patternProgress.clear();
        progressData.forEach((patternId, stageName) {
          final stage = PatternLearningStage.values.firstWhere(
            (s) => s.name == stageName,
            orElse: () => PatternLearningStage.not_started,
          );
          _patternProgress[patternId] = stage;
        });
      }
      
      // Load pattern accuracies
      final accuracyData = data['patternAccuracies'] as Map<String, dynamic>?;
      if (accuracyData != null) {
        _patternAccuracies.clear();
        accuracyData.forEach((patternId, accuracy) {
          _patternAccuracies[patternId] = (accuracy as num).toDouble();
        });
      }
      
      // Load tutorial mode setting
      final tutorialMode = data['tutorialModeEnabled'] as bool?;
      if (tutorialMode != null) {
        _tutorialModeEnabled = tutorialMode;
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading tutorial progress: $e');
    }
  }

  /// Saves learning progress to storage
  Map<String, dynamic> saveProgress() {
    return {
      'currentLevel': _currentLevel.name,
      'patternProgress': _patternProgress.map((k, v) => MapEntry(k, v.name)),
      'patternAccuracies': _patternAccuracies,
      'tutorialModeEnabled': _tutorialModeEnabled,
    };
  }
}
