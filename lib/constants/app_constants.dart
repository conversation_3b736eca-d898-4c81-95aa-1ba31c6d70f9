/// Constants used throughout the app
class AppConstants {
  /// App name
  static const String appName = 'Chart Patterns Trainer';

  /// App version
  static const String appVersion = '1.0.0';

  /// Default animation duration
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);

  /// Slow animation duration
  static const Duration slowAnimationDuration = Duration(milliseconds: 500);

  /// Fast animation duration
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);

  /// Default number of candlesticks to generate
  static const int defaultCandlestickCount = 30;

  /// Default number of quiz options
  static const int defaultQuizOptionCount = 4;

  /// Default quiz difficulty (1-5)
  static const int defaultQuizDifficulty = 3;

  /// Minimum streak to show fire emoji
  static const int minStreakForFire = 3;

  /// Shared preferences keys
  static const String prefKeyUserProgress = 'user_progress';
  static const String prefKeyThemeMode = 'theme_mode';
  static const String prefKeyNotificationsEnabled = 'notifications_enabled';
  static const String prefKeyNotificationTime = 'notification_time';

  /// Notification IDs
  static const int dailyReminderNotificationId = 1;

  /// Notification channels
  static const String reminderChannelId = 'reminders';
  static const String reminderChannelName = 'Practice Reminders';
  static const String reminderChannelDescription = 'Daily reminders to practice chart patterns';

  /// Default notification time (8:00 PM)
  static const int defaultNotificationHour = 20;
  static const int defaultNotificationMinute = 0;

  /// Minimum accuracy to consider a pattern "mastered" (80%)
  static const double masteryThreshold = 0.8;

  /// Default chart height
  static const double defaultChartHeight = 300.0; // Increased height for better visibility

  /// Default chart aspect ratio
  static const double defaultChartAspectRatio = 1.6; // Adjusted for better proportions

  /// Default padding values
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;

  /// Default border radius
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;

  /// Default elevation values
  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;

  /// Default icon sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;

  /// Default font sizes
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 20.0;
  static const double fontSizeXXLarge = 24.0;

  /// Default spacing values
  static const double spacingSmall = 4.0;
  static const double spacingMedium = 8.0;
  static const double spacingLarge = 16.0;

  /// Default button heights
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightMedium = 44.0;
  static const double buttonHeightLarge = 52.0;

  /// Default button widths
  static const double buttonWidthSmall = 80.0;
  static const double buttonWidthMedium = 120.0;
  static const double buttonWidthLarge = 160.0;

  /// Default card heights
  static const double cardHeightSmall = 80.0;
  static const double cardHeightMedium = 120.0;
  static const double cardHeightLarge = 160.0;

  /// Default card widths
  static const double cardWidthSmall = 160.0;
  static const double cardWidthMedium = 240.0;
  static const double cardWidthLarge = 320.0;

  /// Default avatar sizes
  static const double avatarSizeSmall = 32.0;
  static const double avatarSizeMedium = 48.0;
  static const double avatarSizeLarge = 64.0;

  /// Default divider thickness
  static const double dividerThickness = 1.0;

  /// Default progress indicator size
  static const double progressIndicatorSizeSmall = 16.0;
  static const double progressIndicatorSizeMedium = 24.0;
  static const double progressIndicatorSizeLarge = 32.0;

  /// Default tooltip padding
  static const double tooltipPadding = 8.0;

  /// Default tooltip margin
  static const double tooltipMargin = 8.0;

  /// Default tooltip border radius
  static const double tooltipBorderRadius = 4.0;

  /// Default tooltip font size
  static const double tooltipFontSize = 12.0;

  /// Default tooltip max width
  static const double tooltipMaxWidth = 240.0;

  /// Default tooltip height
  static const double tooltipHeight = 32.0;

  /// Default tooltip offset
  static const double tooltipOffset = 8.0;

  /// Default tooltip duration
  static const Duration tooltipDuration = Duration(seconds: 2);

  /// Default snackbar duration
  static const Duration snackbarDuration = Duration(seconds: 3);

  /// Default dialog width
  static const double dialogWidth = 320.0;

  /// Default dialog height
  static const double dialogHeight = 240.0;

  /// Default dialog padding
  static const double dialogPadding = 16.0;

  /// Default dialog border radius
  static const double dialogBorderRadius = 8.0;

  /// Default dialog elevation
  static const double dialogElevation = 8.0;

  /// Default bottom sheet height
  static const double bottomSheetHeight = 320.0;

  /// Default bottom sheet padding
  static const double bottomSheetPadding = 16.0;

  /// Default bottom sheet border radius
  static const double bottomSheetBorderRadius = 16.0;

  /// Default bottom sheet elevation
  static const double bottomSheetElevation = 8.0;

  /// Default app bar height
  static const double appBarHeight = 56.0;

  /// Default bottom navigation bar height
  static const double bottomNavBarHeight = 56.0;

  /// Default tab bar height
  static const double tabBarHeight = 48.0;

  /// Default drawer width
  static const double drawerWidth = 280.0;

  /// Default drawer header height
  static const double drawerHeaderHeight = 160.0;

  /// Default drawer item height
  static const double drawerItemHeight = 48.0;

  /// Default drawer padding
  static const double drawerPadding = 16.0;

  /// Default drawer edge drag width
  static const double drawerEdgeDragWidth = 20.0;

  /// Default drawer elevation
  static const double drawerElevation = 16.0;

  /// Default fab size
  static const double fabSize = 56.0;

  /// Default fab mini size
  static const double fabMiniSize = 40.0;

  /// Default fab elevation
  static const double fabElevation = 6.0;

  /// Default fab padding
  static const double fabPadding = 16.0;

  /// Default fab border radius
  static const double fabBorderRadius = 28.0;

  /// Default fab mini border radius
  static const double fabMiniBorderRadius = 20.0;

  /// Default fab margin
  static const double fabMargin = 16.0;

  /// Default fab mini margin
  static const double fabMiniMargin = 8.0;

  /// Default fab elevation when pressed
  static const double fabElevationPressed = 12.0;

  /// Default fab mini elevation when pressed
  static const double fabMiniElevationPressed = 8.0;

  /// Default fab animation duration
  static const Duration fabAnimationDuration = Duration(milliseconds: 200);

  /// Default fab mini animation duration
  static const Duration fabMiniAnimationDuration = Duration(milliseconds: 150);

  /// Default fab animation curve
  static const String fabAnimationCurve = 'easeInOut';

  /// Default fab mini animation curve
  static const String fabMiniAnimationCurve = 'easeInOut';

  /// Default fab shadow color
  static const String fabShadowColor = '0x33000000';

  /// Default fab mini shadow color
  static const String fabMiniShadowColor = '0x33000000';

  /// Default fab shadow offset
  static const double fabShadowOffset = 2.0;

  /// Default fab mini shadow offset
  static const double fabMiniShadowOffset = 1.0;

  /// Default fab shadow blur radius
  static const double fabShadowBlurRadius = 6.0;

  /// Default fab mini shadow blur radius
  static const double fabMiniShadowBlurRadius = 4.0;

  /// Default fab shadow spread radius
  static const double fabShadowSpreadRadius = 0.0;

  /// Default fab mini shadow spread radius
  static const double fabMiniShadowSpreadRadius = 0.0;

  /// Private constructor to prevent instantiation
  AppConstants._();
}
