import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';

/// Premium pattern data
class PremiumPatternData {
  /// Harmonic patterns
  static final List<ChartPattern> harmonicPatterns = [
    const ChartPattern(
      id: 'gartley',
      name: '<PERSON><PERSON><PERSON>',
      description: 'The Gartley pattern is a harmonic chart pattern, based on Fibonacci numbers and ratios, that helps traders identify reaction highs and lows.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 5,
      category: 'Harmonic',
      color: Color(0xFF9C27B0),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'butterfly',
      name: '<PERSON> Pattern',
      description: 'The Butterfly pattern is a reversal pattern that occurs at the end of a trend and signals a potential change in price direction.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 5,
      category: 'Harmonic',
      color: Color(0xFF9C27B0),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'bat',
      name: '<PERSON> Pattern',
      description: 'The Bat pattern is a harmonic pattern that helps traders identify potential reversal zones with specific Fibonacci measurements.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 5,
      category: 'Harmonic',
      color: Color(0xFF9C27B0),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'crab',
      name: 'Crab Pattern',
      description: 'The Crab pattern is a precise harmonic pattern with extreme projections, offering potentially larger rewards with smaller risk.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 5,
      category: 'Harmonic',
      color: Color(0xFF9C27B0),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'shark',
      name: 'Shark Pattern',
      description: 'The Shark pattern is a five-point reversal pattern that offers early entry before completion of a larger harmonic pattern.',
      trend: PatternTrend.reversal,
      reliability: 3,
      complexity: 5,
      category: 'Harmonic',
      color: Color(0xFF9C27B0),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'cypher',
      name: 'Cypher Pattern',
      description: 'The Cypher pattern is a complex harmonic pattern that signals potential reversals with specific Fibonacci relationships.',
      trend: PatternTrend.reversal,
      reliability: 3,
      complexity: 5,
      category: 'Harmonic',
      color: Color(0xFF9C27B0),
      isPremium: true,
    ),
  ];

  /// Elliott Wave patterns
  static final List<ChartPattern> elliottWavePatterns = [
    const ChartPattern(
      id: 'elliott_impulse',
      name: 'Elliott Impulse Wave',
      description: 'The Elliott Impulse Wave consists of five waves in the direction of the trend, forming the basis of Elliott Wave Theory.',
      trend: PatternTrend.continuation,
      reliability: 4,
      complexity: 5,
      category: 'Elliott Wave',
      color: Color(0xFF3F51B5),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'elliott_corrective',
      name: 'Elliott Corrective Wave',
      description: 'The Elliott Corrective Wave consists of three waves that move against the trend, following an impulse wave.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 5,
      category: 'Elliott Wave',
      color: Color(0xFF3F51B5),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'elliott_diagonal',
      name: 'Elliott Diagonal Triangle',
      description: 'The Elliott Diagonal Triangle is a motive pattern that appears in wave 5 or C positions, signaling the end of a larger movement.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 5,
      category: 'Elliott Wave',
      color: Color(0xFF3F51B5),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'elliott_triangle',
      name: 'Elliott Triangle',
      description: 'The Elliott Triangle is a corrective pattern that forms in wave 4 or B positions, consisting of five overlapping waves.',
      trend: PatternTrend.continuation,
      reliability: 3,
      complexity: 5,
      category: 'Elliott Wave',
      color: Color(0xFF3F51B5),
      isPremium: true,
    ),
  ];

  /// Advanced candlestick patterns
  static final List<ChartPattern> advancedCandlestickPatterns = [
    const ChartPattern(
      id: 'three_line_strike',
      name: 'Three Line Strike',
      description: 'The Three Line Strike pattern consists of three bearish candles followed by a bullish candle that engulfs all three previous candles.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 3,
      category: 'Candlestick',
      color: Color(0xFFFF5722),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'three_black_crows',
      name: 'Three Black Crows',
      description: 'The Three Black Crows pattern consists of three consecutive bearish candles, each opening within the previous candle\'s body and closing near its low.',
      trend: PatternTrend.bearish,
      reliability: 4,
      complexity: 3,
      category: 'Candlestick',
      color: Color(0xFFFF5722),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'three_white_soldiers',
      name: 'Three White Soldiers',
      description: 'The Three White Soldiers pattern consists of three consecutive bullish candles, each opening within the previous candle\'s body and closing near its high.',
      trend: PatternTrend.bullish,
      reliability: 4,
      complexity: 3,
      category: 'Candlestick',
      color: Color(0xFFFF5722),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'morning_star',
      name: 'Morning Star',
      description: 'The Morning Star is a bullish reversal pattern consisting of a large bearish candle, a small-bodied candle, and a large bullish candle.',
      trend: PatternTrend.bullish,
      reliability: 4,
      complexity: 3,
      category: 'Candlestick',
      color: Color(0xFFFF5722),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'evening_star',
      name: 'Evening Star',
      description: 'The Evening Star is a bearish reversal pattern consisting of a large bullish candle, a small-bodied candle, and a large bearish candle.',
      trend: PatternTrend.bearish,
      reliability: 4,
      complexity: 3,
      category: 'Candlestick',
      color: Color(0xFFFF5722),
      isPremium: true,
    ),
  ];

  /// Advanced chart patterns
  static final List<ChartPattern> advancedChartPatterns = [
    const ChartPattern(
      id: 'wolfe_waves',
      name: 'Wolfe Waves',
      description: 'Wolfe Waves are naturally occurring patterns that predict where price is heading and when it will arrive, based on the principle of action and reaction.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 5,
      category: 'Advanced',
      color: Color(0xFF009688),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'three_drives',
      name: 'Three Drives Pattern',
      description: 'The Three Drives pattern consists of three consecutive price movements of similar length, with specific Fibonacci measurements.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 4,
      category: 'Advanced',
      color: Color(0xFF009688),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'abcd',
      name: 'ABCD Pattern',
      description: 'The ABCD pattern is a four-point harmonic pattern where each leg has specific Fibonacci relationships to the others.',
      trend: PatternTrend.reversal,
      reliability: 4,
      complexity: 3,
      category: 'Advanced',
      color: Color(0xFF009688),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'diamond',
      name: 'Diamond Pattern',
      description: 'The Diamond pattern is a complex formation that resembles a diamond shape, often appearing at market tops and bottoms.',
      trend: PatternTrend.reversal,
      reliability: 3,
      complexity: 4,
      category: 'Advanced',
      color: Color(0xFF009688),
      isPremium: true,
    ),
    const ChartPattern(
      id: 'broadening_formation',
      name: 'Broadening Formation',
      description: 'The Broadening Formation is characterized by increasing price volatility and diverging trendlines, often signaling market tops.',
      trend: PatternTrend.reversal,
      reliability: 3,
      complexity: 4,
      category: 'Advanced',
      color: Color(0xFF009688),
      isPremium: true,
    ),
  ];

  /// All premium patterns
  static List<ChartPattern> get allPremiumPatterns => [
    ...harmonicPatterns,
    ...elliottWavePatterns,
    ...advancedCandlestickPatterns,
    ...advancedChartPatterns,
  ];
}
