import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';

/// Static data for chart patterns
class PatternData {
  /// List of all chart patterns
  static final List<ChartPattern> allPatterns = [
    headAndShoulders,
    inverseHeadAndShoulders,
    doubleTop,
    doubleBottom,
    tripleTop,
    tripleBottom,
    cupAndHandle,
    ascendingTriangle,
    descendingTriangle,
    symmetricalTriangle,
    bullishFlag,
    bearishFlag,
    bullishPennant,
    bearishPennant,
    risingWedge,
    fallingWedge,
    rectangle,
    roundingBottom,
    roundingTop,
  ];

  /// Head and Shoulders pattern (bearish reversal)
  static final ChartPattern headAndShoulders = ChartPattern(
    id: 'head_and_shoulders',
    name: 'Head and Shoulders',
    description: 'A powerful bearish reversal pattern that signals a potential trend change from bullish to bearish. Think of it as three mountain peaks, where the middle peak (head) is taller than the two side peaks (shoulders). The "neckline" is like a support floor connecting the valleys between these peaks. When price falls through this floor after forming the right shoulder, it confirms the pattern and suggests further downside.',
    trend: PatternTrend.bearish,
    difficulty: PatternDifficulty.beginner,
    identificationPoints: [
      'Look for three distinct peaks in an uptrend',
      'The middle peak (head) must be noticeably higher than the two side peaks (shoulders)',
      'Draw a straight line (neckline) connecting the lows between the peaks',
      'The pattern is only valid when price breaks below the neckline after forming the right shoulder',
      'Volume often decreases during pattern formation and increases significantly during the breakdown',
    ],
    tradingStrategies: [
      'Wait for confirmation: Only enter a short position after price clearly breaks below the neckline',
      'Place a stop loss slightly above the right shoulder peak for protection',
      'Measure the distance from the head to the neckline, then project this same distance down from the breakout point to set your target',
      'Be aware that price may briefly return to test the neckline from below before continuing downward',
      'For safer entry, wait for a retest of the neckline from below before entering',
    ],
    icon: Icons.trending_down,
    color: Colors.red,
  );

  /// Inverse Head and Shoulders pattern (bullish reversal)
  static final ChartPattern inverseHeadAndShoulders = ChartPattern(
    id: 'inverse_head_and_shoulders',
    name: 'Inverse Head and Shoulders',
    description: 'A powerful bullish reversal pattern that signals a potential trend change from bearish to bullish. Imagine three valleys, where the middle valley (head) is deeper than the two side valleys (shoulders). The "neckline" is like a resistance ceiling connecting the peaks between these valleys. When price breaks through this ceiling after forming the right shoulder, it confirms the pattern and suggests further upside.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.beginner,
    identificationPoints: [
      'Look for three distinct valleys in a downtrend',
      'The middle valley (head) must be noticeably lower than the two side valleys (shoulders)',
      'Draw a straight line (neckline) connecting the highs between the valleys',
      'The pattern is only valid when price breaks above the neckline after forming the right shoulder',
      'Volume often decreases during pattern formation and increases significantly during the breakout',
    ],
    tradingStrategies: [
      'Wait for confirmation: Only enter a long position after price clearly breaks above the neckline',
      'Place a stop loss slightly below the right shoulder trough for protection',
      'Measure the distance from the head to the neckline, then project this same distance up from the breakout point to set your target',
      'Be aware that price may briefly return to test the neckline from above before continuing upward',
      'For safer entry, wait for a retest of the neckline from above before entering',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Double Top pattern (bearish reversal)
  static final ChartPattern doubleTop = ChartPattern(
    id: 'double_top',
    name: 'Double Top',
    description: 'A common bearish reversal pattern that signals a potential trend change from bullish to bearish. Picture price climbing to a peak, pulling back, then rising again to form a second peak at roughly the same level as the first. This creates an "M" shape with a support level at the valley between the peaks. When price drops below this support after the second peak, it suggests buyers have failed twice at the same level and sellers are taking control.',
    trend: PatternTrend.bearish,
    difficulty: PatternDifficulty.beginner,
    identificationPoints: [
      'Look for two distinct peaks reaching approximately the same price level in an uptrend',
      'The peaks should be separated by a noticeable valley (at least 10-15% below the peaks)',
      'The second peak often shows less buying momentum than the first (lower volume)',
      'Draw a horizontal support line at the bottom of the valley between peaks',
      'The pattern is only confirmed when price breaks below this support level after the second peak',
    ],
    tradingStrategies: [
      'Wait for confirmation: Only enter a short position after price clearly breaks below the support level',
      'Place a stop loss slightly above the second peak for protection',
      'Measure the height from peaks to support level, then project this same distance down from the breakout point to set your target',
      'Be prepared for price to potentially retest the broken support level from below before continuing downward',
      'Look for increasing volume on the breakdown to confirm seller strength',
    ],
    icon: Icons.trending_down,
    color: Colors.red,
  );

  /// Double Bottom pattern (bullish reversal)
  static final ChartPattern doubleBottom = ChartPattern(
    id: 'double_bottom',
    name: 'Double Bottom',
    description: 'A common bullish reversal pattern that signals a potential trend change from bearish to bullish. Imagine price falling to a low point, bouncing up, then dropping again to form a second low at roughly the same level as the first. This creates a "W" shape with a resistance level at the peak between the valleys. When price rises above this resistance after the second bottom, it suggests sellers have failed twice at the same level and buyers are taking control.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.beginner,
    identificationPoints: [
      'Look for two distinct bottoms reaching approximately the same price level in a downtrend',
      'The bottoms should be separated by a noticeable peak (at least 10-15% above the bottoms)',
      'The second bottom often shows less selling pressure than the first (higher volume on the bounce)',
      'Draw a horizontal resistance line at the top of the peak between bottoms',
      'The pattern is only confirmed when price breaks above this resistance level after the second bottom',
    ],
    tradingStrategies: [
      'Wait for confirmation: Only enter a long position after price clearly breaks above the resistance level',
      'Place a stop loss slightly below the second bottom for protection',
      'Measure the height from bottoms to resistance level, then project this same distance up from the breakout point to set your target',
      'Be prepared for price to potentially retest the broken resistance level from above before continuing upward',
      'Look for increasing volume on the breakout to confirm buyer strength',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Triple Top pattern (bearish reversal)
  static final ChartPattern tripleTop = ChartPattern(
    id: 'triple_top',
    name: 'Triple Top',
    description: 'A bearish reversal pattern that consists of three peaks at approximately the same level. The pattern is confirmed when the price breaks below the support level established by the troughs between the peaks.',
    trend: PatternTrend.bearish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Three peaks at approximately the same level',
      'Troughs between the peaks forming a support level',
      'Volume typically decreases on each subsequent peak',
      'Price breaks below the support level to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter short position when price breaks below the support level',
      'Set stop loss above the third peak',
      'Target price is the distance from peaks to support, projected downward from the breakout point',
      'Watch for potential retest of the support level from below',
    ],
    icon: Icons.trending_down,
    color: Colors.red,
  );

  /// Triple Bottom pattern (bullish reversal)
  static final ChartPattern tripleBottom = ChartPattern(
    id: 'triple_bottom',
    name: 'Triple Bottom',
    description: 'A bullish reversal pattern that consists of three troughs at approximately the same level. The pattern is confirmed when the price breaks above the resistance level established by the peaks between the troughs.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Three troughs at approximately the same level',
      'Peaks between the troughs forming a resistance level',
      'Volume typically increases on each subsequent trough',
      'Price breaks above the resistance level to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter long position when price breaks above the resistance level',
      'Set stop loss below the third trough',
      'Target price is the distance from troughs to resistance, projected upward from the breakout point',
      'Watch for potential retest of the resistance level from above',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Cup and Handle pattern (bullish continuation)
  static final ChartPattern cupAndHandle = ChartPattern(
    id: 'cup_and_handle',
    name: 'Cup and Handle',
    description: 'A powerful bullish continuation pattern resembling a teacup with a small handle on the right side. The cup forms a rounded "U" shape as price gradually descends and then rises back to the previous high, creating a resistance level. The handle forms as a minor pullback (typically a small flag or pennant) before price breaks above resistance. This pattern represents a period of consolidation before continuing the previous uptrend and is one of the most reliable continuation patterns when properly formed.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Look for a clear prior uptrend before the pattern begins',
      'The cup should form a rounded, smooth "U" shape (not a sharp "V")',
      'Cup depth should typically be between 10-50% of the previous advance',
      'The handle forms as a slight downward drift or consolidation (usually a flag or pennant)',
      'Handle should not drop into the lower half of the cup (ideally staying in the upper third)',
      'Handle should not be too deep or too long compared to the cup',
      'Volume typically decreases during cup formation, dries up in the handle, and increases significantly on breakout',
      'Pattern is only confirmed when price breaks above the resistance level (cup rim) with increased volume',
    ],
    tradingStrategies: [
      'Wait for confirmation: Only enter a long position after price clearly breaks above the resistance level (cup rim)',
      'Look for increased volume on the breakout to confirm strength',
      'Place a stop loss below the lowest point of the handle for protection',
      'Measure the depth from cup bottom to rim, then project this same distance up from the breakout point to set your target',
      'For a more conservative entry, wait for a retest of the broken resistance level before entering',
      'Consider scaling into the position, adding on successful tests of new support',
      'Be patient - the cup can take weeks or months to form in larger timeframes',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Ascending Triangle pattern (bullish continuation)
  static final ChartPattern ascendingTriangle = ChartPattern(
    id: 'ascending_triangle',
    name: 'Ascending Triangle',
    description: 'A powerful bullish continuation pattern characterized by a flat upper resistance line and a rising lower support line that converge to form a triangle. This pattern shows buyers becoming increasingly aggressive (creating higher lows) while sellers remain consistent at a fixed price level. As the pattern tightens, pressure builds until price typically breaks through the upper resistance, continuing the previous uptrend with renewed momentum.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Look for a clear prior uptrend before the pattern begins',
      'Draw a horizontal resistance line connecting at least two peaks at approximately the same price level',
      'Draw an upward-sloping support line connecting at least two troughs, each higher than the previous one',
      'The two lines should converge to form a triangle shape',
      'Price should test both the resistance and support lines at least twice each',
      'Volume typically decreases as the pattern develops (consolidation)',
      'The pattern is only confirmed when price decisively breaks above the horizontal resistance line with increased volume',
      'The most reliable patterns form over several weeks rather than just a few days',
    ],
    tradingStrategies: [
      'Wait for confirmation: Only enter a long position after price clearly breaks above the horizontal resistance line',
      'Look for increased volume on the breakout to confirm strength',
      'Place a stop loss below the most recent higher low (the last touch of the ascending support line)',
      'Measure the height of the triangle at its widest point, then project this same distance up from the breakout point to set your target',
      'For a more conservative entry, wait for a retest of the broken resistance (now support) before entering',
      'Be aware that the longer the pattern takes to form, the more significant the breakout is likely to be',
      'Consider trailing your stop loss as price moves in your favor to protect profits',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Descending Triangle pattern (bearish continuation)
  static final ChartPattern descendingTriangle = ChartPattern(
    id: 'descending_triangle',
    name: 'Descending Triangle',
    description: 'A powerful bearish continuation pattern characterized by a flat lower support line and a falling upper resistance line that converge to form a triangle. This pattern shows sellers becoming increasingly aggressive (creating lower highs) while buyers remain consistent at a fixed price level. As the pattern tightens, pressure builds until price typically breaks through the lower support, continuing the previous downtrend with renewed momentum.',
    trend: PatternTrend.bearish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Look for a clear prior downtrend before the pattern begins',
      'Draw a horizontal support line connecting at least two troughs at approximately the same price level',
      'Draw a downward-sloping resistance line connecting at least two peaks, each lower than the previous one',
      'The two lines should converge to form a triangle shape',
      'Price should test both the support and resistance lines at least twice each',
      'Volume typically decreases as the pattern develops (consolidation)',
      'The pattern is only confirmed when price decisively breaks below the horizontal support line with increased volume',
      'The most reliable patterns form over several weeks rather than just a few days',
    ],
    tradingStrategies: [
      'Wait for confirmation: Only enter a short position after price clearly breaks below the horizontal support line',
      'Look for increased volume on the breakdown to confirm strength',
      'Place a stop loss above the most recent lower high (the last touch of the descending resistance line)',
      'Measure the height of the triangle at its widest point, then project this same distance down from the breakout point to set your target',
      'For a more conservative entry, wait for a retest of the broken support (now resistance) before entering',
      'Be aware that the longer the pattern takes to form, the more significant the breakdown is likely to be',
      'Consider trailing your stop loss as price moves in your favor to protect profits',
    ],
    icon: Icons.trending_down,
    color: Colors.red,
  );

  /// Symmetrical Triangle pattern (neutral continuation)
  static final ChartPattern symmetricalTriangle = ChartPattern(
    id: 'symmetrical_triangle',
    name: 'Symmetrical Triangle',
    description: 'A neutral continuation pattern that consists of a converging upward-sloping support line and downward-sloping resistance line. The pattern can break in either direction, but typically continues in the direction of the prior trend.',
    trend: PatternTrend.neutral,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Converging upward-sloping support line and downward-sloping resistance line',
      'At least two points needed to draw each line',
      'Volume typically decreases during formation and increases on breakout',
      'Price breaks out of the triangle to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter position in the direction of the breakout',
      'Set stop loss at the opposite side of the triangle',
      'Target price is the height of the triangle at its widest point, projected from the breakout point',
      'Watch for potential retest of the breakout level',
    ],
    icon: Icons.trending_flat,
    color: Colors.blue,
  );

  /// Bullish Flag pattern (bullish continuation)
  static final ChartPattern bullishFlag = ChartPattern(
    id: 'bullish_flag',
    name: 'Bullish Flag',
    description: 'A bullish continuation pattern that consists of a strong upward move (the flagpole) followed by a rectangular consolidation pattern (the flag) that slopes downward. The pattern is confirmed when the price breaks above the upper trendline of the flag.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Strong upward move forming the flagpole',
      'Rectangular consolidation pattern sloping downward',
      'Volume typically decreases during flag formation and increases on breakout',
      'Price breaks above the upper trendline of the flag to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter long position when price breaks above the upper trendline of the flag',
      'Set stop loss below the lower trendline of the flag',
      'Target price is the length of the flagpole, projected upward from the breakout point',
      'Watch for potential retest of the upper trendline from above',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Bearish Flag pattern (bearish continuation)
  static final ChartPattern bearishFlag = ChartPattern(
    id: 'bearish_flag',
    name: 'Bearish Flag',
    description: 'A bearish continuation pattern that consists of a strong downward move (the flagpole) followed by a rectangular consolidation pattern (the flag) that slopes upward. The pattern is confirmed when the price breaks below the lower trendline of the flag.',
    trend: PatternTrend.bearish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Strong downward move forming the flagpole',
      'Rectangular consolidation pattern sloping upward',
      'Volume typically decreases during flag formation and increases on breakdown',
      'Price breaks below the lower trendline of the flag to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter short position when price breaks below the lower trendline of the flag',
      'Set stop loss above the upper trendline of the flag',
      'Target price is the length of the flagpole, projected downward from the breakout point',
      'Watch for potential retest of the lower trendline from below',
    ],
    icon: Icons.trending_down,
    color: Colors.red,
  );

  /// Bullish Pennant pattern (bullish continuation)
  static final ChartPattern bullishPennant = ChartPattern(
    id: 'bullish_pennant',
    name: 'Bullish Pennant',
    description: 'A bullish continuation pattern that consists of a strong upward move (the flagpole) followed by a small symmetrical triangle (the pennant). The pattern is confirmed when the price breaks above the upper trendline of the pennant.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Strong upward move forming the flagpole',
      'Small symmetrical triangle forming the pennant',
      'Volume typically decreases during pennant formation and increases on breakout',
      'Price breaks above the upper trendline of the pennant to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter long position when price breaks above the upper trendline of the pennant',
      'Set stop loss below the lower trendline of the pennant',
      'Target price is the length of the flagpole, projected upward from the breakout point',
      'Watch for potential retest of the upper trendline from above',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Bearish Pennant pattern (bearish continuation)
  static final ChartPattern bearishPennant = ChartPattern(
    id: 'bearish_pennant',
    name: 'Bearish Pennant',
    description: 'A bearish continuation pattern that consists of a strong downward move (the flagpole) followed by a small symmetrical triangle (the pennant). The pattern is confirmed when the price breaks below the lower trendline of the pennant.',
    trend: PatternTrend.bearish,
    difficulty: PatternDifficulty.intermediate,
    identificationPoints: [
      'Strong downward move forming the flagpole',
      'Small symmetrical triangle forming the pennant',
      'Volume typically decreases during pennant formation and increases on breakdown',
      'Price breaks below the lower trendline of the pennant to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter short position when price breaks below the lower trendline of the pennant',
      'Set stop loss above the upper trendline of the pennant',
      'Target price is the length of the flagpole, projected downward from the breakout point',
      'Watch for potential retest of the lower trendline from below',
    ],
    icon: Icons.trending_down,
    color: Colors.red,
  );

  /// Rising Wedge pattern (bearish reversal)
  static final ChartPattern risingWedge = ChartPattern(
    id: 'rising_wedge',
    name: 'Rising Wedge',
    description: 'A bearish reversal pattern that consists of two upward-sloping trendlines, with the lower trendline having a steeper slope than the upper trendline. The pattern is confirmed when the price breaks below the lower trendline.',
    trend: PatternTrend.bearish,
    difficulty: PatternDifficulty.advanced,
    identificationPoints: [
      'Two upward-sloping trendlines converging',
      'Lower trendline has a steeper slope than the upper trendline',
      'Volume typically decreases during formation and increases on breakdown',
      'Price breaks below the lower trendline to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter short position when price breaks below the lower trendline',
      'Set stop loss above the upper trendline',
      'Target price is the height of the wedge at its widest point, projected downward from the breakout point',
      'Watch for potential retest of the lower trendline from below',
    ],
    icon: Icons.trending_down,
    color: Colors.red,
  );

  /// Falling Wedge pattern (bullish reversal)
  static final ChartPattern fallingWedge = ChartPattern(
    id: 'falling_wedge',
    name: 'Falling Wedge',
    description: 'A bullish reversal pattern that consists of two downward-sloping trendlines, with the upper trendline having a steeper slope than the lower trendline. The pattern is confirmed when the price breaks above the upper trendline.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.advanced,
    identificationPoints: [
      'Two downward-sloping trendlines converging',
      'Upper trendline has a steeper slope than the lower trendline',
      'Volume typically decreases during formation and increases on breakout',
      'Price breaks above the upper trendline to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter long position when price breaks above the upper trendline',
      'Set stop loss below the lower trendline',
      'Target price is the height of the wedge at its widest point, projected upward from the breakout point',
      'Watch for potential retest of the upper trendline from above',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Rectangle pattern (neutral continuation)
  static final ChartPattern rectangle = ChartPattern(
    id: 'rectangle',
    name: 'Rectangle',
    description: 'A neutral continuation pattern that consists of horizontal support and resistance lines. The pattern can break in either direction, but typically continues in the direction of the prior trend.',
    trend: PatternTrend.neutral,
    difficulty: PatternDifficulty.beginner,
    identificationPoints: [
      'Horizontal support line connecting at least two troughs',
      'Horizontal resistance line connecting at least two peaks',
      'Price bounces between support and resistance at least twice',
      'Price breaks out of the rectangle to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter position in the direction of the breakout',
      'Set stop loss at the opposite side of the rectangle',
      'Target price is the height of the rectangle, projected from the breakout point',
      'Watch for potential retest of the breakout level',
    ],
    icon: Icons.trending_flat,
    color: Colors.blue,
  );

  /// Rounding Bottom pattern (bullish reversal)
  static final ChartPattern roundingBottom = ChartPattern(
    id: 'rounding_bottom',
    name: 'Rounding Bottom',
    description: 'A bullish reversal pattern that resembles a bowl or saucer shape. The pattern represents a gradual shift from bearish to bullish sentiment and is confirmed when the price breaks above the resistance level established by the right rim of the bowl.',
    trend: PatternTrend.bullish,
    difficulty: PatternDifficulty.advanced,
    identificationPoints: [
      'Bowl or saucer shape with a rounded bottom',
      'Gradual shift from bearish to bullish sentiment',
      'Volume typically follows the price action, decreasing as price reaches the bottom and increasing as price rises',
      'Price breaks above the resistance level to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter long position when price breaks above the resistance level',
      'Set stop loss below the lowest point of the pattern',
      'Target price is the depth of the bowl, projected upward from the breakout point',
      'Watch for potential retest of the resistance level from above',
    ],
    icon: Icons.trending_up,
    color: Colors.green,
  );

  /// Rounding Top pattern (bearish reversal)
  static final ChartPattern roundingTop = ChartPattern(
    id: 'rounding_top',
    name: 'Rounding Top',
    description: 'A bearish reversal pattern that resembles an inverted bowl or dome shape. The pattern represents a gradual shift from bullish to bearish sentiment and is confirmed when the price breaks below the support level established by the right rim of the bowl.',
    trend: PatternTrend.bearish,
    difficulty: PatternDifficulty.advanced,
    identificationPoints: [
      'Inverted bowl or dome shape with a rounded top',
      'Gradual shift from bullish to bearish sentiment',
      'Volume typically follows the price action, decreasing as price reaches the top and increasing as price falls',
      'Price breaks below the support level to confirm the pattern',
    ],
    tradingStrategies: [
      'Enter short position when price breaks below the support level',
      'Set stop loss above the highest point of the pattern',
      'Target price is the height of the bowl, projected downward from the breakout point',
      'Watch for potential retest of the support level from below',
    ],
    icon: Icons.trending_down,
    color: Colors.red,
  );

  /// Private constructor to prevent instantiation
  PatternData._();
}
