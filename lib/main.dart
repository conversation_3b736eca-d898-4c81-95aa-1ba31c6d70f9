import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/providers/trade_journal_provider.dart';
import 'package:learn_chart_patterns/features/trade_journal/services/database_service.dart';
import 'package:learn_chart_patterns/providers/notification_provider.dart';
import 'package:learn_chart_patterns/providers/pattern_provider.dart';
import 'package:learn_chart_patterns/providers/progress_provider.dart';
import 'package:learn_chart_patterns/providers/theme_provider.dart';
import 'package:learn_chart_patterns/providers/trading_course_provider.dart';
import 'package:learn_chart_patterns/screens/home_screen.dart';
import 'package:learn_chart_patterns/widgets/app_icon.dart';
import 'package:learn_chart_patterns/services/ad_service.dart';
import 'package:learn_chart_patterns/services/notification_service.dart';
import 'package:learn_chart_patterns/services/premium_content_service.dart';
import 'package:learn_chart_patterns/services/progress_service.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';
import 'package:learn_chart_patterns/services/storage_service.dart';
import 'package:learn_chart_patterns/services/strategic_ad_service.dart';
import 'package:learn_chart_patterns/services/visual_enhancement_service.dart';
import 'package:provider/provider.dart';

void main() {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Run the app immediately with a loading indicator
  runApp(const AppLoader());
}

/// Loader widget that initializes services in the background
class AppLoader extends StatefulWidget {
  /// Creates a new app loader
  const AppLoader({super.key});

  @override
  State<AppLoader> createState() => _AppLoaderState();
}

class _AppLoaderState extends State<AppLoader> {
  /// Whether services are initialized
  bool _initialized = false;

  /// Services
  late StorageService _storageService;
  late ProgressService _progressService;
  late NotificationService _notificationService;
  late AdService _adService;
  late PurchaseService _purchaseService;
  late StrategicAdService _strategicAdService;
  late VisualEnhancementService _visualEnhancementService;
  late PremiumContentService _premiumContentService;
  late DatabaseService _databaseService;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// Initialize essential services and then launch the main app
  Future<void> _initializeServices() async {
    // Initialize storage service first (required by other services)
    _storageService = StorageService();
    await _storageService.init();

    // Initialize other services
    _progressService = ProgressService(_storageService);
    _notificationService = NotificationService(_storageService);
    _adService = AdService();
    _purchaseService = PurchaseService();
    _strategicAdService = StrategicAdService();
    _visualEnhancementService = VisualEnhancementService();
    _premiumContentService = PremiumContentService();
    _databaseService = DatabaseService();

    // Start initialization in parallel
    final futures = [
      _progressService.init(),
      _notificationService.init(),
      _adService.initialize(),
      _purchaseService.initialize(_storageService),
      _strategicAdService.initialize(_storageService),
      _visualEnhancementService.initialize(_storageService),
      _databaseService.init(),
    ];

    // Wait for all services to initialize
    await Future.wait(futures);

    // Mark as initialized and rebuild to show the main app
    setState(() {
      _initialized = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator until services are initialized
    if (!_initialized) {
      return MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const AppIcon(
                  size: 100,
                ),
                const SizedBox(height: 24),
                const CircularProgressIndicator(),
              ],
            ),
          ),
        ),
      );
    }

    // Show the main app once services are initialized
    return MultiProvider(
      providers: [
        // Services
        Provider<StorageService>.value(value: _storageService),
        Provider<ProgressService>.value(value: _progressService),
        Provider<NotificationService>.value(value: _notificationService),
        Provider<AdService>.value(value: _adService),
        Provider<PurchaseService>.value(value: _purchaseService),
        Provider<StrategicAdService>.value(value: _strategicAdService),
        Provider<VisualEnhancementService>.value(value: _visualEnhancementService),
        Provider<PremiumContentService>.value(value: _premiumContentService),
        Provider<DatabaseService>.value(value: _databaseService),

        // Providers
        ChangeNotifierProvider(create: (_) => ThemeProvider(_storageService)),
        ChangeNotifierProvider(create: (_) => ProgressProvider(_progressService)),
        ChangeNotifierProvider(create: (_) => NotificationProvider(_notificationService)),
        ChangeNotifierProvider(create: (_) => PatternProvider()),
        ChangeNotifierProvider(create: (_) => TradingCourseProvider(_storageService)),
        ChangeNotifierProvider(create: (_) => TradeJournalProvider(_databaseService, _purchaseService)),
      ],
      child: const MyApp(),
    );
  }
}

/// The main app widget
class MyApp extends StatelessWidget {
  /// Creates a new app
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = context.watch<ThemeProvider>();

    return MaterialApp(
      title: AppConstants.appName,
      theme: themeProvider.getTheme(Brightness.light),
      darkTheme: themeProvider.getTheme(Brightness.dark),
      themeMode: themeProvider.themeMode,
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
