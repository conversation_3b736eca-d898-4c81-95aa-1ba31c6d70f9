import 'package:learn_chart_patterns/generators/base_generator.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';

/// Generator for Ascending Triangle pattern
class AscendingTriangleGenerator extends BaseGenerator {
  /// Creates a new Ascending Triangle generator
  AscendingTriangleGenerator({
    int? seed,
    double basePrice = 100.0,
    double volatility = 0.5,
    bool isBullish = true, // Ascending Triangle is typically bullish
  }) : super(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish,
        );

  @override
  List<CandlestickData> generateCandlesticks({
    int length = 30,
    DateTime? startDate,
  }) {
    // Use provided start date or default to now
    final DateTime start = startDate ?? DateTime.now().subtract(const Duration(days: 30));

    // Allocate candles for each part of the pattern - making the pattern more pronounced
    final int firstTouchLength = 4; // First touch of resistance - increased for better visibility
    final int firstPullbackLength = 4; // First pullback to support - increased for better visibility
    final int secondTouchLength = 4; // Second touch of resistance - increased for better visibility
    final int secondPullbackLength = 4; // Second pullback to support - increased for better visibility
    final int thirdTouchLength = 4; // Third touch of resistance - increased for better visibility
    final int breakoutLength = 5; // Breakout above resistance - increased for better visibility

    // Calculate remaining candles for pre and post pattern
    final int patternLength = firstTouchLength + firstPullbackLength + secondTouchLength +
                             secondPullbackLength + thirdTouchLength + breakoutLength;

    final int prePatternLength = (length - patternLength) ~/ 2;
    final int postPatternLength = length - patternLength - prePatternLength;

    // Define price levels with accurate proportions for realistic ascending triangle
    final double resistancePrice = basePrice * 1.12; // Realistic resistance level for clear identification
    final double firstSupportPrice = basePrice * 0.94; // First support level
    final double secondSupportPrice = basePrice * 0.99; // Ascending support (higher lows)
    final double prePatternStartPrice = basePrice * 0.88; // Realistic uptrend start
    final double postPatternTargetPrice = basePrice * 1.24; // Measured move target (triangle height)

    // Generate pre-pattern uptrend with clearer trend
    final List<CandlestickData> prePattern = generateTrend(
      startDate: start,
      length: prePatternLength,
      startPrice: prePatternStartPrice,
      endPrice: basePrice,
      volatilityFactor: 0.6, // Reduced volatility for clearer trend
    );

    // Generate first touch of resistance - more pronounced
    final DateTime firstTouchStart = start.add(Duration(days: prePatternLength));
    // Use prePattern.last.close if available, otherwise use a default value
    final double startPrice = prePattern.isNotEmpty
        ? prePattern.last.close
        : basePrice;
    final List<CandlestickData> firstTouch = generateTrend(
      startDate: firstTouchStart,
      length: firstTouchLength,
      startPrice: startPrice,
      endPrice: resistancePrice,
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate first pullback to support - more pronounced
    final DateTime firstPullbackStart = firstTouchStart.add(Duration(days: firstTouchLength));
    final List<CandlestickData> firstPullback = generateTrend(
      startDate: firstPullbackStart,
      length: firstPullbackLength,
      startPrice: firstTouch.last.close,
      endPrice: firstSupportPrice,
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate second touch of resistance - more pronounced
    final DateTime secondTouchStart = firstPullbackStart.add(Duration(days: firstPullbackLength));
    final List<CandlestickData> secondTouch = generateTrend(
      startDate: secondTouchStart,
      length: secondTouchLength,
      startPrice: firstPullback.last.close,
      endPrice: resistancePrice * 0.99, // Very close to resistance
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate second pullback to higher support - more pronounced ascending pattern
    final DateTime secondPullbackStart = secondTouchStart.add(Duration(days: secondTouchLength));
    final List<CandlestickData> secondPullback = generateTrend(
      startDate: secondPullbackStart,
      length: secondPullbackLength,
      startPrice: secondTouch.last.close,
      endPrice: secondSupportPrice,
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate third touch of resistance - more pronounced
    final DateTime thirdTouchStart = secondPullbackStart.add(Duration(days: secondPullbackLength));
    final List<CandlestickData> thirdTouch = generateTrend(
      startDate: thirdTouchStart,
      length: thirdTouchLength,
      startPrice: secondPullback.last.close,
      endPrice: resistancePrice * 0.998, // Even closer to resistance
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate breakout above resistance - more pronounced
    final DateTime breakoutStart = thirdTouchStart.add(Duration(days: thirdTouchLength));
    final List<CandlestickData> breakout = generateTrend(
      startDate: breakoutStart,
      length: breakoutLength,
      startPrice: thirdTouch.last.close,
      endPrice: resistancePrice * 1.08, // More pronounced breakout for better visibility
      volatilityFactor: 0.6, // Moderate volatility for realistic breakout
    );

    // Generate post-pattern uptrend with clear continuation
    final DateTime postPatternStart = breakoutStart.add(Duration(days: breakoutLength));
    final List<CandlestickData> postPattern = generateTrend(
      startDate: postPatternStart,
      length: postPatternLength,
      startPrice: breakout.last.close,
      endPrice: postPatternTargetPrice,
      volatilityFactor: 0.7, // Moderate volatility for realistic follow-through
    );

    // Combine all segments
    final List<CandlestickData> candlesticks = [
      ...prePattern,
      ...firstTouch,
      ...firstPullback,
      ...secondTouch,
      ...secondPullback,
      ...thirdTouch,
      ...breakout,
      ...postPattern,
    ];

    // Add minimal noise to keep the pattern clear while still realistic
    return addNoise(candlesticks, 0.003); // Reduced noise for clearer pattern
  }

  @override
  List<SupportResistanceLevel> generateSupportResistanceLevels(
    List<CandlestickData> candlesticks,
  ) {
    // Find the resistance level (horizontal line)
    final int patternStart = candlesticks.length ~/ 4;
    final int patternEnd = patternStart + (candlesticks.length ~/ 2);

    final List<CandlestickData> patternCandles = candlesticks.sublist(patternStart, patternEnd);

    // Find the highest points to establish the resistance line
    final List<double> peaks = [];

    for (int i = 1; i < patternCandles.length - 1; i++) {
      if (patternCandles[i].high > patternCandles[i - 1].high &&
          patternCandles[i].high > patternCandles[i + 1].high) {
        peaks.add(patternCandles[i].high);
      }
    }

    // Sort peaks and take the top ones
    peaks.sort((a, b) => b.compareTo(a)); // Sort in descending order
    final List<double> topPeaks = peaks.take(3).toList();

    // Calculate average peak level for resistance
    final double resistancePrice = topPeaks.isEmpty
        ? patternCandles.first.high
        : topPeaks.reduce((a, b) => a + b) / topPeaks.length;

    // Find the support points to establish the ascending support line
    final List<CandlestickData> firstThird = patternCandles.sublist(0, patternCandles.length ~/ 3);
    final List<CandlestickData> lastThird = patternCandles.sublist(
      (patternCandles.length * 2) ~/ 3,
      patternCandles.length,
    );

    // Find the lowest points in the first and last third
    final double firstLow = firstThird.fold(
      double.infinity,
      (lowest, candle) => lowest < candle.low ? lowest : candle.low,
    );

    final double lastLow = lastThird.fold(
      double.infinity,
      (lowest, candle) => lowest < candle.low ? lowest : candle.low,
    );

    // Calculate the dates for the support trend line
    final DateTime startDate = firstThird[firstThird.length ~/ 2].date;
    final DateTime endDate = lastThird[lastThird.length ~/ 2].date;

    // Calculate the target price (measured move)
    final double targetPrice = resistancePrice + (resistancePrice - firstLow);

    return [
      // Horizontal resistance line - more prominent
      SupportResistanceLevel.resistance(
        price: resistancePrice,
        strength: 10, // Increased strength for emphasis
        label: 'Resistance',
      ),

      // Ascending support trend line - more prominent
      SupportResistanceLevel.trendLine(
        startDate: startDate,
        endDate: endDate,
        startPrice: firstLow,
        endPrice: lastLow,
        strength: 9, // Increased strength for emphasis
        label: 'Ascending Support',
      ),

      // First support level - added for educational value
      SupportResistanceLevel.support(
        price: firstLow,
        strength: 7,
        label: 'First Support',
      ),

      // Second support level - added for educational value
      SupportResistanceLevel.support(
        price: lastLow,
        strength: 8,
        label: 'Second Support',
      ),

      // Target level (measured move) - added for educational value
      SupportResistanceLevel.resistance(
        price: targetPrice,
        strength: 8,
        label: 'Price Target',
      ),
    ];
  }
}
