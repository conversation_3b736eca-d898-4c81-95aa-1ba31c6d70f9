import 'package:learn_chart_patterns/generators/base_generator.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';

/// Generator for Head and Shoulders pattern
class HeadAndShouldersGenerator extends BaseGenerator {
  /// Creates a new Head and Shoulders generator
  HeadAndShouldersGenerator({
    int? seed,
    double basePrice = 100.0,
    double volatility = 0.5,
    bool isBullish = false, // Head and Shoulders is typically bearish
  }) : super(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish,
        );

  @override
  List<CandlestickData> generateCandlesticks({
    int length = 30,
    DateTime? startDate,
  }) {
    // Use provided start date or default to now
    final DateTime start = startDate ?? DateTime.now().subtract(const Duration(days: 30));

    // Allocate candles for each part of the pattern - making the pattern more pronounced
    final int uptrend1Length = 5; // Initial uptrend to left shoulder
    final int leftShoulderLength = 4; // Left shoulder formation - increased for visibility
    final int downtrend1Length = 3; // Downtrend from left shoulder to neckline
    final int uptrend2Length = 5; // Uptrend from neckline to head - increased for visibility
    final int headLength = 4; // Head formation - increased for visibility
    final int downtrend2Length = 4; // Downtrend from head to neckline
    final int uptrend3Length = 4; // Uptrend from neckline to right shoulder - increased for visibility
    final int rightShoulderLength = 4; // Right shoulder formation - increased for visibility
    final int breakdown1Length = 3; // Initial breakdown below neckline - increased for clarity

    // Calculate remaining candles for pre and post pattern
    final int patternLength = uptrend1Length + leftShoulderLength + downtrend1Length +
                             uptrend2Length + headLength + downtrend2Length +
                             uptrend3Length + rightShoulderLength + breakdown1Length;

    final int prePatternLength = (length - patternLength) ~/ 2;
    final int postPatternLength = length - patternLength - prePatternLength;

    // Define price levels with more accurate proportions for better pattern recognition
    final double necklinePrice = basePrice;
    final double leftShoulderPeak = necklinePrice * 1.18; // Optimized height for clear visibility
    final double headPeak = necklinePrice * 1.35; // Prominent head for clear identification
    final double rightShoulderPeak = necklinePrice * 1.16; // Slightly lower than left shoulder for accuracy
    final double prePatternStartPrice = necklinePrice * 0.82; // Realistic uptrend start
    final double postPatternTargetPrice = necklinePrice * 0.65; // Measured move target (head height)

    // Generate pre-pattern uptrend with clearer trend
    final List<CandlestickData> prePattern = generateTrend(
      startDate: start,
      length: prePatternLength,
      startPrice: prePatternStartPrice,
      endPrice: necklinePrice * 0.95,
      volatilityFactor: 0.6, // Reduced volatility for clearer trend
    );

    // Generate initial uptrend to left shoulder - more pronounced
    final DateTime uptrend1Start = start.add(Duration(days: prePatternLength));
    // Use prePattern.last.close if available, otherwise use a default value
    final double startPrice = prePattern.isNotEmpty
        ? prePattern.last.close
        : necklinePrice * 0.95;
    final List<CandlestickData> uptrend1 = generateTrend(
      startDate: uptrend1Start,
      length: uptrend1Length,
      startPrice: startPrice,
      endPrice: leftShoulderPeak * 0.95,
      volatilityFactor: 0.5, // Reduced volatility for clearer trend
    );

    // Generate left shoulder with more pronounced peak
    final DateTime leftShoulderStart = uptrend1Start.add(Duration(days: uptrend1Length));
    final List<CandlestickData> leftShoulder = generateTrend(
      startDate: leftShoulderStart,
      length: leftShoulderLength,
      startPrice: uptrend1.last.close,
      endPrice: leftShoulderPeak,
      volatilityFactor: 0.4, // Reduced volatility for clearer pattern
    );

    // Generate downtrend from left shoulder to neckline
    final DateTime downtrend1Start = leftShoulderStart.add(Duration(days: leftShoulderLength));
    final List<CandlestickData> downtrend1 = generateTrend(
      startDate: downtrend1Start,
      length: downtrend1Length,
      startPrice: leftShoulder.last.close,
      endPrice: necklinePrice * 1.02, // Slightly above neckline for realism
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate uptrend from neckline to head - more pronounced
    final DateTime uptrend2Start = downtrend1Start.add(Duration(days: downtrend1Length));
    final List<CandlestickData> uptrend2 = generateTrend(
      startDate: uptrend2Start,
      length: uptrend2Length,
      startPrice: downtrend1.last.close,
      endPrice: headPeak * 0.95,
      volatilityFactor: 0.5, // Reduced volatility for clearer trend
    );

    // Generate head with more pronounced peak
    final DateTime headStart = uptrend2Start.add(Duration(days: uptrend2Length));
    final List<CandlestickData> head = generateTrend(
      startDate: headStart,
      length: headLength,
      startPrice: uptrend2.last.close,
      endPrice: headPeak,
      volatilityFactor: 0.4, // Reduced volatility for clearer pattern
    );

    // Generate downtrend from head to neckline
    final DateTime downtrend2Start = headStart.add(Duration(days: headLength));
    final List<CandlestickData> downtrend2 = generateTrend(
      startDate: downtrend2Start,
      length: downtrend2Length,
      startPrice: head.last.close,
      endPrice: necklinePrice * 1.01, // Slightly above neckline for realism
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate uptrend from neckline to right shoulder
    final DateTime uptrend3Start = downtrend2Start.add(Duration(days: downtrend2Length));
    final List<CandlestickData> uptrend3 = generateTrend(
      startDate: uptrend3Start,
      length: uptrend3Length,
      startPrice: downtrend2.last.close,
      endPrice: rightShoulderPeak * 0.95,
      volatilityFactor: 0.5, // Reduced volatility for clearer trend
    );

    // Generate right shoulder with more pronounced peak
    final DateTime rightShoulderStart = uptrend3Start.add(Duration(days: uptrend3Length));
    final List<CandlestickData> rightShoulder = generateTrend(
      startDate: rightShoulderStart,
      length: rightShoulderLength,
      startPrice: uptrend3.last.close,
      endPrice: rightShoulderPeak,
      volatilityFactor: 0.4, // Reduced volatility for clearer pattern
    );

    // Generate breakdown below neckline - more pronounced for clarity
    final DateTime breakdown1Start = rightShoulderStart.add(Duration(days: rightShoulderLength));
    final List<CandlestickData> breakdown1 = generateTrend(
      startDate: breakdown1Start,
      length: breakdown1Length,
      startPrice: rightShoulder.last.close,
      endPrice: necklinePrice * 0.90, // More pronounced breakdown for clarity
      volatilityFactor: 0.6, // Moderate volatility for realistic breakdown
    );

    // Generate post-pattern downtrend with clear continuation
    final DateTime postPatternStart = breakdown1Start.add(Duration(days: breakdown1Length));
    final List<CandlestickData> postPattern = generateTrend(
      startDate: postPatternStart,
      length: postPatternLength,
      startPrice: breakdown1.last.close,
      endPrice: postPatternTargetPrice,
      volatilityFactor: 0.7, // Moderate volatility for realistic follow-through
    );

    // Combine all segments
    final List<CandlestickData> candlesticks = [
      ...prePattern,
      ...uptrend1,
      ...leftShoulder,
      ...downtrend1,
      ...uptrend2,
      ...head,
      ...downtrend2,
      ...uptrend3,
      ...rightShoulder,
      ...breakdown1,
      ...postPattern,
    ];

    // Add minimal noise to keep the pattern clear while still realistic
    return addNoise(candlesticks, 0.003); // Reduced noise for clearer pattern
  }

  @override
  List<SupportResistanceLevel> generateSupportResistanceLevels(
    List<CandlestickData> candlesticks,
  ) {
    // Find the neckline by looking at the troughs between shoulders and head
    final int patternStart = candlesticks.length ~/ 3;
    final int patternEnd = patternStart + (candlesticks.length ~/ 3) * 2;

    final List<CandlestickData> patternCandles = candlesticks.sublist(patternStart, patternEnd);

    // Find the lowest points in the pattern to establish the neckline
    final List<double> troughs = [];

    for (int i = 1; i < patternCandles.length - 1; i++) {
      if (patternCandles[i].low < patternCandles[i - 1].low &&
          patternCandles[i].low < patternCandles[i + 1].low) {
        troughs.add(patternCandles[i].low);
      }
    }

    // Sort troughs and take the middle ones to avoid outliers
    troughs.sort();
    final List<double> middleTroughs = troughs.length > 4
        ? troughs.sublist(1, troughs.length - 1)
        : troughs;

    // Calculate average trough level for neckline
    final double necklinePrice = middleTroughs.isEmpty
        ? patternCandles.first.low
        : middleTroughs.reduce((a, b) => a + b) / middleTroughs.length;

    // Find the highest point for the head
    final double headPeak = patternCandles.fold(
      double.negativeInfinity,
      (highest, candle) => highest > candle.high ? highest : candle.high,
    );

    // Find left and right shoulder peaks
    double leftShoulderPeak = 0;
    double rightShoulderPeak = 0;

    // First third of pattern candles for left shoulder
    final int leftThird = patternCandles.length ~/ 3;
    leftShoulderPeak = patternCandles.sublist(0, leftThird).fold(
      double.negativeInfinity,
      (highest, candle) => highest > candle.high ? highest : candle.high,
    );

    // Last third of pattern candles for right shoulder
    final int rightThirdStart = patternCandles.length - leftThird;
    rightShoulderPeak = patternCandles.sublist(rightThirdStart).fold(
      double.negativeInfinity,
      (highest, candle) => highest > candle.high ? highest : candle.high,
    );

    return [
      // Neckline (support that becomes resistance)
      SupportResistanceLevel.support(
        price: necklinePrice,
        strength: 10, // Increased strength for emphasis
        label: 'Neckline',
      ),

      // Head level (resistance)
      SupportResistanceLevel.resistance(
        price: headPeak,
        strength: 8, // Increased strength for emphasis
        label: 'Head',
      ),

      // Left shoulder level
      SupportResistanceLevel.resistance(
        price: leftShoulderPeak,
        strength: 6,
        label: 'Left Shoulder',
      ),

      // Right shoulder level
      SupportResistanceLevel.resistance(
        price: rightShoulderPeak,
        strength: 6,
        label: 'Right Shoulder',
      ),

      // Target level (measured move)
      SupportResistanceLevel.support(
        price: necklinePrice - (headPeak - necklinePrice),
        strength: 7,
        label: 'Target',
      ),
    ];
  }
}
