import 'dart:math';

import 'package:learn_chart_patterns/generators/base_generator.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';

/// Generator for Cup and Handle pattern
class CupAndHandleGenerator extends BaseGenerator {
  /// Creates a new Cup and Handle generator
  CupAndHandleGenerator({
    int? seed,
    double basePrice = 100.0,
    double volatility = 0.5,
    bool isBullish = true, // Cup and Handle is typically bullish
  }) : super(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish,
        );

  @override
  List<CandlestickData> generateCandlesticks({
    int length = 30,
    DateTime? startDate,
  }) {
    // Use provided start date or default to now
    final DateTime start = startDate ?? DateTime.now().subtract(const Duration(days: 30));

    // Allocate candles for each part of the pattern - making the pattern more pronounced
    final int leftRimLength = 4; // Left rim of the cup - increased for better visibility
    final int leftCupLength = 6; // Left side of the cup - increased for better visibility
    final int cupBottomLength = 4; // Bottom of the cup - increased for better visibility
    final int rightCupLength = 6; // Right side of the cup - increased for better visibility
    final int rightRimLength = 4; // Right rim of the cup - increased for better visibility
    final int handleLength = 5; // Handle formation - increased for better visibility
    final int breakoutLength = 4; // Breakout above resistance - increased for better visibility

    // Calculate remaining candles for pre and post pattern
    final int patternLength = leftRimLength + leftCupLength + cupBottomLength +
                             rightCupLength + rightRimLength + handleLength + breakoutLength;

    final int prePatternLength = (length - patternLength) ~/ 2;
    final int postPatternLength = length - patternLength - prePatternLength;

    // Define price levels with accurate proportions for realistic cup and handle pattern
    final double resistancePrice = basePrice * 1.12; // Realistic resistance level for clear identification
    final double cupBottomPrice = basePrice * 0.88; // Realistic cup depth (12% retracement)
    final double handleBottomPrice = basePrice * 1.06; // Handle pullback (50% of cup depth)
    final double prePatternStartPrice = basePrice * 0.92; // Realistic uptrend start
    final double postPatternTargetPrice = basePrice * 1.24; // Measured move target (cup height)

    // Generate pre-pattern uptrend with clearer trend
    final List<CandlestickData> prePattern = generateTrend(
      startDate: start,
      length: prePatternLength,
      startPrice: prePatternStartPrice,
      endPrice: basePrice,
      volatilityFactor: 0.6, // Reduced volatility for clearer trend
    );

    // Generate left rim of the cup - more pronounced
    final DateTime leftRimStart = start.add(Duration(days: prePatternLength));
    // Use prePattern.last.close if available, otherwise use a default value
    final double startPrice = prePattern.isNotEmpty
        ? prePattern.last.close
        : basePrice;
    final List<CandlestickData> leftRim = generateTrend(
      startDate: leftRimStart,
      length: leftRimLength,
      startPrice: startPrice,
      endPrice: resistancePrice,
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate left side of the cup (downtrend) - more pronounced
    final DateTime leftCupStart = leftRimStart.add(Duration(days: leftRimLength));
    final List<CandlestickData> leftCup = generateTrend(
      startDate: leftCupStart,
      length: leftCupLength,
      startPrice: leftRim.last.close,
      endPrice: cupBottomPrice * 1.05,
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate cup bottom (consolidation) - more pronounced and clearer
    final DateTime cupBottomStart = leftCupStart.add(Duration(days: leftCupLength));
    final List<CandlestickData> cupBottom = [];

    for (int i = 0; i < cupBottomLength; i++) {
      final DateTime date = cupBottomStart.add(Duration(days: i));

      // Generate a random price around the cup bottom level with reduced volatility
      // Use generateRandomPrice instead of direct _random access
      final double randomFactor = 1.0 + ((generateRandomPrice(0, 1) * 2 - 1) * 0.02); // Reduced volatility
      final double open = cupBottomPrice * randomFactor;
      final double close = cupBottomPrice * (1.0 + ((generateRandomPrice(0, 1) * 2 - 1) * 0.02)); // Reduced volatility

      // Generate high and low with small wicks for clearer pattern
      final double high = max(open, close) * (1.0 + (generateRandomPrice(0, 1) * 0.015)); // Smaller wicks
      final double low = min(open, close) * (1.0 - (generateRandomPrice(0, 1) * 0.015)); // Smaller wicks

      // Generate random volume (higher at the bottom for realism)
      final double volume = generateRandomVolume(2000); // Increased volume at bottom

      final candlestick = CandlestickData(
        date: date,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: volume,
      );

      cupBottom.add(candlestick);
    }

    // Generate right side of the cup (uptrend) - more pronounced
    final DateTime rightCupStart = cupBottomStart.add(Duration(days: cupBottomLength));
    final List<CandlestickData> rightCup = generateTrend(
      startDate: rightCupStart,
      length: rightCupLength,
      startPrice: cupBottom.last.close,
      endPrice: resistancePrice * 0.97, // Closer to resistance for better visibility
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate right rim of the cup - more pronounced
    final DateTime rightRimStart = rightCupStart.add(Duration(days: rightCupLength));
    final List<CandlestickData> rightRim = generateTrend(
      startDate: rightRimStart,
      length: rightRimLength,
      startPrice: rightCup.last.close,
      endPrice: resistancePrice,
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate handle (small pullback) - more pronounced and clearer
    final DateTime handleStart = rightRimStart.add(Duration(days: rightRimLength));
    final List<CandlestickData> handle = generateTrend(
      startDate: handleStart,
      length: handleLength,
      startPrice: rightRim.last.close,
      endPrice: handleBottomPrice,
      volatilityFactor: 0.4, // Reduced volatility for clearer pattern
    );

    // Generate breakout above resistance - more pronounced
    final DateTime breakoutStart = handleStart.add(Duration(days: handleLength));
    final List<CandlestickData> breakout = generateTrend(
      startDate: breakoutStart,
      length: breakoutLength,
      startPrice: handle.last.close,
      endPrice: resistancePrice * 1.08, // More pronounced breakout for better visibility
      volatilityFactor: 0.6, // Moderate volatility for realistic breakout
    );

    // Generate post-pattern uptrend with clear continuation
    final DateTime postPatternStart = breakoutStart.add(Duration(days: breakoutLength));
    final List<CandlestickData> postPattern = generateTrend(
      startDate: postPatternStart,
      length: postPatternLength,
      startPrice: breakout.last.close,
      endPrice: postPatternTargetPrice,
      volatilityFactor: 0.7, // Moderate volatility for realistic follow-through
    );

    // Combine all segments
    final List<CandlestickData> candlesticks = [
      ...prePattern,
      ...leftRim,
      ...leftCup,
      ...cupBottom,
      ...rightCup,
      ...rightRim,
      ...handle,
      ...breakout,
      ...postPattern,
    ];

    // Add minimal noise to keep the pattern clear while still realistic
    return addNoise(candlesticks, 0.003); // Reduced noise for clearer pattern
  }

  @override
  List<SupportResistanceLevel> generateSupportResistanceLevels(
    List<CandlestickData> candlesticks,
  ) {
    // Find the resistance level at the cup rim
    final int patternStart = candlesticks.length ~/ 4;
    final int patternEnd = patternStart + (candlesticks.length ~/ 2);

    final List<CandlestickData> patternCandles = candlesticks.sublist(patternStart, patternEnd);

    // Find the highest points for the cup rims
    final List<CandlestickData> firstQuarter = patternCandles.sublist(0, patternCandles.length ~/ 4);
    final List<CandlestickData> lastQuarter = patternCandles.sublist(
      (patternCandles.length * 3) ~/ 4,
      patternCandles.length,
    );

    final double leftRimPrice = firstQuarter.fold(
      double.negativeInfinity,
      (highest, candle) => highest > candle.high ? highest : candle.high,
    );

    final double rightRimPrice = lastQuarter.fold(
      double.negativeInfinity,
      (highest, candle) => highest > candle.high ? highest : candle.high,
    );

    // Use the average of the two rims for the resistance level
    final double resistancePrice = (leftRimPrice + rightRimPrice) / 2;

    // Find the lowest point in the middle of the pattern for the cup bottom
    final List<CandlestickData> middleCandles = patternCandles.sublist(
      patternCandles.length ~/ 3,
      (patternCandles.length * 2) ~/ 3,
    );

    final double cupBottomPrice = middleCandles.fold(
      double.infinity,
      (lowest, candle) => lowest < candle.low ? lowest : candle.low,
    );

    // Calculate the target price (measured move)
    final double targetPrice = resistancePrice + (resistancePrice - cupBottomPrice);

    // Calculate handle bottom price
    final double handleLowPrice = (resistancePrice + cupBottomPrice) / 2;

    return [
      // Resistance level at the cup rim - more prominent
      SupportResistanceLevel.resistance(
        price: resistancePrice,
        strength: 10, // Increased strength for emphasis
        label: 'Cup Rim (Resistance)',
      ),

      // Support level at the cup bottom - more prominent
      SupportResistanceLevel.support(
        price: cupBottomPrice,
        strength: 8, // Increased strength for emphasis
        label: 'Cup Bottom',
      ),

      // Handle support level - added for educational value
      SupportResistanceLevel.support(
        price: handleLowPrice,
        strength: 7,
        label: 'Handle Support',
      ),

      // Target level (measured move) - added for educational value
      SupportResistanceLevel.resistance(
        price: targetPrice,
        strength: 9,
        label: 'Price Target',
      ),
    ];
  }
}
