import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/enhanced_pattern_annotation.dart';

/// Generator for creating educational pattern annotations
class PatternAnnotationGenerator {
  /// Generates annotations for a specific pattern
  static List<EnhancedPatternAnnotation> generateAnnotations(
    ChartPattern pattern,
    List<CandlestickData> candlesticks,
  ) {
    switch (pattern.id) {
      case 'head_and_shoulders':
        return _generateHeadAndShouldersAnnotations(candlesticks);
      case 'inverse_head_and_shoulders':
        return _generateInverseHeadAndShouldersAnnotations(candlesticks);
      case 'double_top':
        return _generateDoubleTopAnnotations(candlesticks);
      case 'double_bottom':
        return _generateDoubleBottomAnnotations(candlesticks);
      case 'ascending_triangle':
        return _generateAscendingTriangleAnnotations(candlesticks);
      case 'descending_triangle':
        return _generateDescendingTriangleAnnotations(candlesticks);
      case 'cup_and_handle':
        return _generateCupAndHandleAnnotations(candlesticks);
      default:
        return _generateGenericAnnotations(candlesticks);
    }
  }

  /// Generates annotations for Head and Shoulders pattern
  static List<EnhancedPatternAnnotation> _generateHeadAndShouldersAnnotations(
    List<CandlestickData> candlesticks,
  ) {
    final annotations = <EnhancedPatternAnnotation>[];
    
    // Find key points
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);
    
    if (peaks.length >= 3) {
      // Left shoulder
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: peaks[0].date,
        price: peaks[0].high,
        text: 'Left Shoulder',
        description: 'First peak in the pattern - establishes initial resistance',
      ));
      
      // Head
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: peaks[1].date,
        price: peaks[1].high,
        text: 'Head',
        description: 'Highest peak - should be higher than both shoulders',
      ));
      
      // Right shoulder
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: peaks[2].date,
        price: peaks[2].high,
        text: 'Right Shoulder',
        description: 'Final peak - should be roughly equal to left shoulder',
      ));
    }
    
    if (troughs.length >= 2) {
      // Neckline
      final necklinePrice = (troughs[0].low + troughs[1].low) / 2;
      annotations.add(EnhancedPatternAnnotation.support(
        date: troughs[1].date,
        price: necklinePrice,
        text: 'Neckline',
        description: 'Key support level connecting the two troughs',
      ));
      
      // Breakout point (after pattern completion)
      if (candlesticks.length > peaks.length + troughs.length + 5) {
        final breakoutIndex = candlesticks.length - 5;
        annotations.add(EnhancedPatternAnnotation.breakout(
          date: candlesticks[breakoutIndex].date,
          price: necklinePrice * 0.95,
          text: 'Neckline Break',
          description: 'Bearish confirmation when price breaks below neckline',
        ));
      }
    }
    
    return annotations;
  }

  /// Generates annotations for Inverse Head and Shoulders pattern
  static List<EnhancedPatternAnnotation> _generateInverseHeadAndShouldersAnnotations(
    List<CandlestickData> candlesticks,
  ) {
    final annotations = <EnhancedPatternAnnotation>[];
    
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);
    
    if (troughs.length >= 3) {
      // Left shoulder
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: troughs[0].date,
        price: troughs[0].low,
        text: 'Left Shoulder',
        description: 'First trough - establishes initial support',
      ));
      
      // Head
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: troughs[1].date,
        price: troughs[1].low,
        text: 'Head',
        description: 'Lowest trough - should be lower than both shoulders',
      ));
      
      // Right shoulder
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: troughs[2].date,
        price: troughs[2].low,
        text: 'Right Shoulder',
        description: 'Final trough - should be roughly equal to left shoulder',
      ));
    }
    
    if (peaks.length >= 2) {
      // Neckline
      final necklinePrice = (peaks[0].high + peaks[1].high) / 2;
      annotations.add(EnhancedPatternAnnotation.resistance(
        date: peaks[1].date,
        price: necklinePrice,
        text: 'Neckline',
        description: 'Key resistance level connecting the two peaks',
      ));
    }
    
    return annotations;
  }

  /// Generates annotations for Double Top pattern
  static List<EnhancedPatternAnnotation> _generateDoubleTopAnnotations(
    List<CandlestickData> candlesticks,
  ) {
    final annotations = <EnhancedPatternAnnotation>[];
    
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);
    
    if (peaks.length >= 2) {
      // First peak
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: peaks[0].date,
        price: peaks[0].high,
        text: 'First Peak',
        description: 'Initial resistance test - establishes the double top level',
      ));
      
      // Second peak
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: peaks[1].date,
        price: peaks[1].high,
        text: 'Second Peak',
        description: 'Confirmation peak - should be roughly equal to first peak',
      ));
      
      // Resistance level
      final resistancePrice = (peaks[0].high + peaks[1].high) / 2;
      annotations.add(EnhancedPatternAnnotation.resistance(
        date: peaks[1].date,
        price: resistancePrice,
        text: 'Double Top Resistance',
        description: 'Strong resistance level formed by two equal peaks',
      ));
    }
    
    if (troughs.isNotEmpty) {
      // Support level
      annotations.add(EnhancedPatternAnnotation.support(
        date: troughs[0].date,
        price: troughs[0].low,
        text: 'Support',
        description: 'Key support level between the two peaks',
      ));
    }
    
    return annotations;
  }

  /// Generates annotations for Double Bottom pattern
  static List<EnhancedPatternAnnotation> _generateDoubleBottomAnnotations(
    List<CandlestickData> candlesticks,
  ) {
    final annotations = <EnhancedPatternAnnotation>[];
    
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);
    
    if (troughs.length >= 2) {
      // First bottom
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: troughs[0].date,
        price: troughs[0].low,
        text: 'First Bottom',
        description: 'Initial support test - establishes the double bottom level',
      ));
      
      // Second bottom
      annotations.add(EnhancedPatternAnnotation.keyPoint(
        date: troughs[1].date,
        price: troughs[1].low,
        text: 'Second Bottom',
        description: 'Confirmation bottom - should be roughly equal to first bottom',
      ));
      
      // Support level
      final supportPrice = (troughs[0].low + troughs[1].low) / 2;
      annotations.add(EnhancedPatternAnnotation.support(
        date: troughs[1].date,
        price: supportPrice,
        text: 'Double Bottom Support',
        description: 'Strong support level formed by two equal bottoms',
      ));
    }
    
    if (peaks.isNotEmpty) {
      // Resistance level
      annotations.add(EnhancedPatternAnnotation.resistance(
        date: peaks[0].date,
        price: peaks[0].high,
        text: 'Resistance',
        description: 'Key resistance level between the two bottoms',
      ));
    }
    
    return annotations;
  }

  /// Generates annotations for Ascending Triangle pattern
  static List<EnhancedPatternAnnotation> _generateAscendingTriangleAnnotations(
    List<CandlestickData> candlesticks,
  ) {
    final annotations = <EnhancedPatternAnnotation>[];
    
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);
    
    if (peaks.length >= 2) {
      // Horizontal resistance
      final resistancePrice = peaks.fold(0.0, (sum, peak) => sum + peak.high) / peaks.length;
      annotations.add(EnhancedPatternAnnotation.resistance(
        date: peaks.last.date,
        price: resistancePrice,
        text: 'Horizontal Resistance',
        description: 'Flat resistance line formed by equal highs',
      ));
    }
    
    if (troughs.length >= 2) {
      // Ascending support
      annotations.add(EnhancedPatternAnnotation.support(
        date: troughs.last.date,
        price: troughs.last.low,
        text: 'Ascending Support',
        description: 'Rising support line formed by higher lows',
      ));
      
      // Educational note about the pattern
      annotations.add(EnhancedPatternAnnotation.educationalNote(
        date: troughs.last.date,
        price: (troughs.last.low + peaks.last.high) / 2,
        text: 'Triangle Formation',
        description: 'Price is squeezed between horizontal resistance and rising support',
      ));
    }
    
    return annotations;
  }

  /// Generates annotations for Descending Triangle pattern
  static List<EnhancedPatternAnnotation> _generateDescendingTriangleAnnotations(
    List<CandlestickData> candlesticks,
  ) {
    final annotations = <EnhancedPatternAnnotation>[];
    
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);
    
    if (troughs.length >= 2) {
      // Horizontal support
      final supportPrice = troughs.fold(0.0, (sum, trough) => sum + trough.low) / troughs.length;
      annotations.add(EnhancedPatternAnnotation.support(
        date: troughs.last.date,
        price: supportPrice,
        text: 'Horizontal Support',
        description: 'Flat support line formed by equal lows',
      ));
    }
    
    if (peaks.length >= 2) {
      // Descending resistance
      annotations.add(EnhancedPatternAnnotation.resistance(
        date: peaks.last.date,
        price: peaks.last.high,
        text: 'Descending Resistance',
        description: 'Falling resistance line formed by lower highs',
      ));
    }
    
    return annotations;
  }

  /// Generates annotations for Cup and Handle pattern
  static List<EnhancedPatternAnnotation> _generateCupAndHandleAnnotations(
    List<CandlestickData> candlesticks,
  ) {
    final annotations = <EnhancedPatternAnnotation>[];
    
    // Find the highest and lowest points
    final highestPoint = candlesticks.fold(
      double.negativeInfinity,
      (highest, candle) => highest > candle.high ? highest : candle.high,
    );
    final lowestPoint = candlesticks.fold(
      double.infinity,
      (lowest, candle) => lowest < candle.low ? lowest : candle.low,
    );
    
    // Find the dates for these points
    final highestCandle = candlesticks.firstWhere((c) => c.high == highestPoint);
    final lowestCandle = candlesticks.firstWhere((c) => c.low == lowestPoint);
    
    // Cup rim
    annotations.add(EnhancedPatternAnnotation.resistance(
      date: highestCandle.date,
      price: highestPoint,
      text: 'Cup Rim',
      description: 'Resistance level at the top of the cup formation',
    ));
    
    // Cup bottom
    annotations.add(EnhancedPatternAnnotation.support(
      date: lowestCandle.date,
      price: lowestPoint,
      text: 'Cup Bottom',
      description: 'Support level at the bottom of the U-shaped cup',
    ));
    
    // Educational note about the pattern
    final midPoint = candlesticks[candlesticks.length ~/ 2];
    annotations.add(EnhancedPatternAnnotation.educationalNote(
      date: midPoint.date,
      price: (highestPoint + lowestPoint) / 2,
      text: 'Cup Formation',
      description: 'U-shaped price pattern showing gradual recovery from decline',
    ));
    
    return annotations;
  }

  /// Generates generic annotations for unknown patterns
  static List<EnhancedPatternAnnotation> _generateGenericAnnotations(
    List<CandlestickData> candlesticks,
  ) {
    final annotations = <EnhancedPatternAnnotation>[];
    
    final peaks = _findPeaks(candlesticks);
    final troughs = _findTroughs(candlesticks);
    
    // Add basic support and resistance levels
    if (peaks.isNotEmpty) {
      final highestPeak = peaks.reduce((a, b) => a.high > b.high ? a : b);
      annotations.add(EnhancedPatternAnnotation.resistance(
        date: highestPeak.date,
        price: highestPeak.high,
        text: 'Resistance',
        description: 'Key resistance level',
      ));
    }
    
    if (troughs.isNotEmpty) {
      final lowestTrough = troughs.reduce((a, b) => a.low < b.low ? a : b);
      annotations.add(EnhancedPatternAnnotation.support(
        date: lowestTrough.date,
        price: lowestTrough.low,
        text: 'Support',
        description: 'Key support level',
      ));
    }
    
    return annotations;
  }

  /// Finds peaks in the candlestick data
  static List<CandlestickData> _findPeaks(List<CandlestickData> candlesticks) {
    final peaks = <CandlestickData>[];
    
    for (int i = 2; i < candlesticks.length - 2; i++) {
      if (candlesticks[i].high > candlesticks[i - 1].high &&
          candlesticks[i].high > candlesticks[i + 1].high &&
          candlesticks[i].high > candlesticks[i - 2].high &&
          candlesticks[i].high > candlesticks[i + 2].high) {
        peaks.add(candlesticks[i]);
      }
    }
    
    return peaks;
  }

  /// Finds troughs in the candlestick data
  static List<CandlestickData> _findTroughs(List<CandlestickData> candlesticks) {
    final troughs = <CandlestickData>[];
    
    for (int i = 2; i < candlesticks.length - 2; i++) {
      if (candlesticks[i].low < candlesticks[i - 1].low &&
          candlesticks[i].low < candlesticks[i + 1].low &&
          candlesticks[i].low < candlesticks[i - 2].low &&
          candlesticks[i].low < candlesticks[i + 2].low) {
        troughs.add(candlesticks[i]);
      }
    }
    
    return troughs;
  }
}
