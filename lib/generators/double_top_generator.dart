import 'package:learn_chart_patterns/generators/base_generator.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';

/// Generator for Double Top pattern
class DoubleTopGenerator extends BaseGenerator {
  /// Creates a new Double Top generator
  DoubleTopGenerator({
    int? seed,
    double basePrice = 100.0,
    double volatility = 0.5,
    bool isBullish = false, // Double Top is typically bearish
  }) : super(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish,
        );

  @override
  List<CandlestickData> generateCandlesticks({
    int length = 30,
    DateTime? startDate,
  }) {
    // Use provided start date or default to now
    final DateTime start = startDate ?? DateTime.now().subtract(const Duration(days: 30));

    // Allocate candles for each part of the pattern - making the pattern more pronounced
    final int uptrendLength = 7; // Initial uptrend - increased for better visibility
    final int firstPeakLength = 4; // First peak formation - increased for better visibility
    final int retracementLength = 5; // Retracement to support - increased for better visibility
    final int secondUptrendLength = 5; // Second uptrend - increased for better visibility
    final int secondPeakLength = 4; // Second peak formation - increased for better visibility
    final int breakdownLength = 5; // Breakdown below support - increased for better visibility

    // Calculate remaining candles for pre and post pattern
    final int patternLength = uptrendLength + firstPeakLength + retracementLength +
                             secondUptrendLength + secondPeakLength + breakdownLength;

    final int prePatternLength = (length - patternLength) ~/ 2;
    final int postPatternLength = length - patternLength - prePatternLength;

    // Define price levels with accurate proportions for realistic double top pattern
    final double supportPrice = basePrice;
    final double peakPrice = supportPrice * 1.22; // Realistic peak height for clear identification
    final double prePatternStartPrice = supportPrice * 0.83; // Realistic uptrend start
    final double postPatternTargetPrice = supportPrice * 0.78; // Measured move target (pattern height)

    // Generate pre-pattern uptrend with clearer trend
    final List<CandlestickData> prePattern = generateTrend(
      startDate: start,
      length: prePatternLength,
      startPrice: prePatternStartPrice,
      endPrice: supportPrice * 0.95,
      volatilityFactor: 0.6, // Reduced volatility for clearer trend
    );

    // Generate initial uptrend - more pronounced
    final DateTime uptrendStart = start.add(Duration(days: prePatternLength));
    // Use prePattern.last.close if available, otherwise use a default value
    final double startPrice = prePattern.isNotEmpty
        ? prePattern.last.close
        : supportPrice * 0.95;
    final List<CandlestickData> uptrend = generateTrend(
      startDate: uptrendStart,
      length: uptrendLength,
      startPrice: startPrice,
      endPrice: peakPrice * 0.95,
      volatilityFactor: 0.5, // Reduced volatility for clearer trend
    );

    // Generate first peak with more pronounced peak
    final DateTime firstPeakStart = uptrendStart.add(Duration(days: uptrendLength));
    final List<CandlestickData> firstPeak = generateTrend(
      startDate: firstPeakStart,
      length: firstPeakLength,
      startPrice: uptrend.last.close,
      endPrice: peakPrice,
      volatilityFactor: 0.4, // Reduced volatility for clearer pattern
    );

    // Generate retracement to support - more pronounced
    final DateTime retracementStart = firstPeakStart.add(Duration(days: firstPeakLength));
    final List<CandlestickData> retracement = generateTrend(
      startDate: retracementStart,
      length: retracementLength,
      startPrice: firstPeak.last.close,
      endPrice: supportPrice * 1.02, // Slightly above support for realism
      volatilityFactor: 0.5, // Reduced volatility for clearer pattern
    );

    // Generate second uptrend - more pronounced
    final DateTime secondUptrendStart = retracementStart.add(Duration(days: retracementLength));
    final List<CandlestickData> secondUptrend = generateTrend(
      startDate: secondUptrendStart,
      length: secondUptrendLength,
      startPrice: retracement.last.close,
      endPrice: peakPrice * 0.97,
      volatilityFactor: 0.5, // Reduced volatility for clearer trend
    );

    // Generate second peak with more pronounced peak
    final DateTime secondPeakStart = secondUptrendStart.add(Duration(days: secondUptrendLength));
    final List<CandlestickData> secondPeak = generateTrend(
      startDate: secondPeakStart,
      length: secondPeakLength,
      startPrice: secondUptrend.last.close,
      endPrice: peakPrice * 0.99, // Slightly lower than first peak for realism
      volatilityFactor: 0.4, // Reduced volatility for clearer pattern
    );

    // Generate breakdown below support - more pronounced for clarity
    final DateTime breakdownStart = secondPeakStart.add(Duration(days: secondPeakLength));
    final List<CandlestickData> breakdown = generateTrend(
      startDate: breakdownStart,
      length: breakdownLength,
      startPrice: secondPeak.last.close,
      endPrice: supportPrice * 0.88, // More pronounced breakdown for clarity
      volatilityFactor: 0.6, // Moderate volatility for realistic breakdown
    );

    // Generate post-pattern downtrend with clear continuation
    final DateTime postPatternStart = breakdownStart.add(Duration(days: breakdownLength));
    final List<CandlestickData> postPattern = generateTrend(
      startDate: postPatternStart,
      length: postPatternLength,
      startPrice: breakdown.last.close,
      endPrice: postPatternTargetPrice,
      volatilityFactor: 0.7, // Moderate volatility for realistic follow-through
    );

    // Combine all segments
    final List<CandlestickData> candlesticks = [
      ...prePattern,
      ...uptrend,
      ...firstPeak,
      ...retracement,
      ...secondUptrend,
      ...secondPeak,
      ...breakdown,
      ...postPattern,
    ];

    // Add minimal noise to keep the pattern clear while still realistic
    return addNoise(candlesticks, 0.003); // Reduced noise for clearer pattern
  }

  @override
  List<SupportResistanceLevel> generateSupportResistanceLevels(
    List<CandlestickData> candlesticks,
  ) {
    // Find the support level by looking at the trough between peaks
    final int patternStart = candlesticks.length ~/ 3;
    final int patternEnd = patternStart + (candlesticks.length ~/ 3) * 2;

    final List<CandlestickData> patternCandles = candlesticks.sublist(patternStart, patternEnd);

    // Find the lowest point in the middle of the pattern for support
    int middleStart = patternCandles.length ~/ 3;
    int middleEnd = middleStart + (patternCandles.length ~/ 3);

    final List<CandlestickData> middleCandles = patternCandles.sublist(middleStart, middleEnd);

    final double supportPrice = middleCandles.fold(
      double.infinity,
      (lowest, candle) => lowest < candle.low ? lowest : candle.low,
    );

    // Find the highest points for the peaks
    final List<CandlestickData> firstThird = patternCandles.sublist(0, patternCandles.length ~/ 3);
    final List<CandlestickData> lastThird = patternCandles.sublist(
      (patternCandles.length * 2) ~/ 3,
      patternCandles.length,
    );

    final double firstPeakPrice = firstThird.fold(
      double.negativeInfinity,
      (highest, candle) => highest > candle.high ? highest : candle.high,
    );

    final double secondPeakPrice = lastThird.fold(
      double.negativeInfinity,
      (highest, candle) => highest > candle.high ? highest : candle.high,
    );

    // Use the average of the two peaks for the resistance level
    final double resistancePrice = (firstPeakPrice + secondPeakPrice) / 2;

    // Calculate the target price (measured move)
    final double targetPrice = supportPrice - (resistancePrice - supportPrice);

    return [
      // Support level
      SupportResistanceLevel.support(
        price: supportPrice,
        strength: 10, // Increased strength for emphasis
        label: 'Support',
      ),

      // Resistance level at the peaks
      SupportResistanceLevel.resistance(
        price: resistancePrice,
        strength: 9, // Increased strength for emphasis
        label: 'Resistance',
      ),

      // First peak level
      SupportResistanceLevel.resistance(
        price: firstPeakPrice,
        strength: 7,
        label: 'First Peak',
      ),

      // Second peak level
      SupportResistanceLevel.resistance(
        price: secondPeakPrice,
        strength: 7,
        label: 'Second Peak',
      ),

      // Target level (measured move)
      SupportResistanceLevel.support(
        price: targetPrice,
        strength: 8,
        label: 'Target',
      ),
    ];
  }
}
