import 'dart:math';

import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';

/// Base class for generating chart patterns
abstract class BaseGenerator {
  /// Random number generator
  final Random _random;
  
  /// Base price for the pattern
  final double basePrice;
  
  /// Volatility factor (0.0-1.0)
  final double volatility;
  
  /// Whether to generate bullish or bearish pattern
  final bool isBullish;

  /// Creates a new base generator
  BaseGenerator({
    int? seed,
    this.basePrice = 100.0,
    this.volatility = 0.5,
    this.isBullish = true,
  }) : _random = Random(seed);

  /// Generates a list of candlestick data for the pattern
  List<CandlestickData> generateCandlesticks({
    int length = 30,
    DateTime? startDate,
  });

  /// Generates support and resistance levels for the pattern
  List<SupportResistanceLevel> generateSupportResistanceLevels(
    List<CandlestickData> candlesticks,
  );

  /// Generates a random price movement
  double generateRandomMove(double baseMove) {
    return baseMove * (1.0 + ((_random.nextDouble() * 2 - 1) * volatility));
  }

  /// Generates a random price within a range
  double generateRandomPrice(double min, double max) {
    return min + _random.nextDouble() * (max - min);
  }

  /// Generates a random volume
  double generateRandomVolume(double baseVolume) {
    return baseVolume * (0.5 + _random.nextDouble());
  }

  /// Generates a random candlestick
  CandlestickData generateRandomCandlestick({
    required DateTime date,
    required double previousClose,
    double movePercent = 0.01,
    double wickPercent = 0.5,
    bool? forceBullish,
    bool? forceBearish,
  }) {
    // Determine if this candle will be bullish or bearish
    final bool isCandleBullish = forceBullish != null
        ? forceBullish
        : forceBearish != null
            ? !forceBearish
            : _random.nextBool();
    
    // Generate the price move
    final double move = generateRandomMove(previousClose * movePercent);
    
    // Calculate open and close based on bullish/bearish
    double open, close;
    if (isCandleBullish) {
      open = previousClose;
      close = previousClose + move;
    } else {
      open = previousClose;
      close = previousClose - move;
    }
    
    // Generate high and low with wicks
    final double bodyDiff = (close - open).abs();
    final double upperWick = bodyDiff * wickPercent * _random.nextDouble();
    final double lowerWick = bodyDiff * wickPercent * _random.nextDouble();
    
    final double high = max(open, close) + upperWick;
    final double low = min(open, close) - lowerWick;
    
    // Generate random volume
    final double volume = generateRandomVolume(1000);
    
    return CandlestickData(
      date: date,
      open: open,
      high: high,
      low: low,
      close: close,
      volume: volume,
    );
  }

  /// Generates a trend of candlesticks
  List<CandlestickData> generateTrend({
    required DateTime startDate,
    required int length,
    required double startPrice,
    required double endPrice,
    double wickPercent = 0.5,
    double volatilityFactor = 1.0,
  }) {
    final List<CandlestickData> candlesticks = [];
    
    // Calculate the average price move per candle
    final double totalMove = endPrice - startPrice;
    final double avgMovePerCandle = totalMove / length;
    
    double currentPrice = startPrice;
    
    for (int i = 0; i < length; i++) {
      final DateTime date = startDate.add(Duration(days: i));
      
      // Calculate target price for this candle
      final double targetPrice = startPrice + (avgMovePerCandle * (i + 1));
      
      // Add some randomness to the move
      final double randomFactor = 1.0 + ((_random.nextDouble() * 2 - 1) * volatilityFactor * volatility);
      final double move = (targetPrice - currentPrice) * randomFactor;
      
      // Determine if this candle will be bullish or bearish based on the overall trend
      // but with some randomness
      final bool isCandleBullish = _random.nextDouble() < 0.5
          ? totalMove > 0 // Follow the trend
          : totalMove < 0; // Against the trend
      
      // Calculate open and close based on bullish/bearish
      double open, close;
      if (isCandleBullish) {
        open = currentPrice;
        close = currentPrice + move.abs();
      } else {
        open = currentPrice;
        close = currentPrice - move.abs();
      }
      
      // Generate high and low with wicks
      final double bodyDiff = (close - open).abs();
      final double upperWick = bodyDiff * wickPercent * _random.nextDouble();
      final double lowerWick = bodyDiff * wickPercent * _random.nextDouble();
      
      final double high = max(open, close) + upperWick;
      final double low = min(open, close) - lowerWick;
      
      // Generate random volume
      final double volume = generateRandomVolume(1000);
      
      final candlestick = CandlestickData(
        date: date,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: volume,
      );
      
      candlesticks.add(candlestick);
      
      // Update current price for next candle
      currentPrice = close;
    }
    
    return candlesticks;
  }

  /// Adds noise to a list of candlesticks
  List<CandlestickData> addNoise(
    List<CandlestickData> candlesticks,
    double noiseFactor,
  ) {
    return candlesticks.map((candle) {
      final double noise = candle.close * noiseFactor * (_random.nextDouble() * 2 - 1);
      
      double open = candle.open + noise;
      double close = candle.close + noise;
      double high = candle.high + noise.abs();
      double low = candle.low - noise.abs();
      
      // Ensure high is the highest and low is the lowest
      high = max(high, max(open, close));
      low = min(low, min(open, close));
      
      return CandlestickData(
        date: candle.date,
        open: open,
        high: high,
        low: low,
        close: close,
        volume: candle.volume,
      );
    }).toList();
  }

  /// Finds the highest price in a list of candlesticks
  double findHighestPrice(List<CandlestickData> candlesticks) {
    return candlesticks.fold(
      double.negativeInfinity,
      (highest, candle) => max(highest, candle.high),
    );
  }

  /// Finds the lowest price in a list of candlesticks
  double findLowestPrice(List<CandlestickData> candlesticks) {
    return candlesticks.fold(
      double.infinity,
      (lowest, candle) => min(lowest, candle.low),
    );
  }

  /// Scales a list of candlesticks to fit within a price range
  List<CandlestickData> scaleToRange(
    List<CandlestickData> candlesticks,
    double minPrice,
    double maxPrice,
  ) {
    final double currentMin = findLowestPrice(candlesticks);
    final double currentMax = findHighestPrice(candlesticks);
    final double currentRange = currentMax - currentMin;
    final double targetRange = maxPrice - minPrice;
    
    if (currentRange <= 0) return candlesticks;
    
    final double scaleFactor = targetRange / currentRange;
    
    return candlesticks.map((candle) {
      final double scaledOpen = (candle.open - currentMin) * scaleFactor + minPrice;
      final double scaledHigh = (candle.high - currentMin) * scaleFactor + minPrice;
      final double scaledLow = (candle.low - currentMin) * scaleFactor + minPrice;
      final double scaledClose = (candle.close - currentMin) * scaleFactor + minPrice;
      
      return CandlestickData(
        date: candle.date,
        open: scaledOpen,
        high: scaledHigh,
        low: scaledLow,
        close: scaledClose,
        volume: candle.volume,
      );
    }).toList();
  }
}
