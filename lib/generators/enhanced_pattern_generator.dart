import 'dart:math';

import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/enhanced_support_resistance_level.dart';
import 'package:learn_chart_patterns/models/pattern_annotation.dart';
import 'package:learn_chart_patterns/models/trend_line.dart';

/// A class that generates enhanced chart patterns with annotations and trend lines
class EnhancedPatternGenerator {
  /// Random number generator
  final Random _random;

  /// Creates a new enhanced pattern generator
  EnhancedPatternGenerator({int? seed}) : _random = Random(seed);

  /// Generates candlestick data for a head and shoulders pattern
  List<CandlestickData> generateHeadAndShoulders({
    int length = 45, // Increased length for better visibility
    double volatility = 0.5,
    double basePrice = 100.0,
  }) {
    final candlesticks = <CandlestickData>[];
    final DateTime startDate = DateTime.now().subtract(const Duration(days: 100));

    // Initial uptrend - more pronounced
    double price = basePrice * 0.8; // Start lower for more pronounced uptrend
    for (int i = 0; i < 10; i++) {
      price = _generateNextPrice(price, trend: 0.6, volatility: volatility * 0.8); // Clearer uptrend
      candlesticks.add(_generateCandlestick(
        date: startDate.add(Duration(days: i)),
        price: price,
        volatility: volatility * 0.7, // Reduced volatility for clearer trend
      ));
    }

    // Left shoulder - more pronounced
    final leftShoulderPeak = price * 1.15; // Higher peak for better visibility
    for (int i = 0; i < 6; i++) { // Increased length for better visibility
      price = _interpolate(price, leftShoulderPeak, i / 5);
      candlesticks.add(_generateCandlestick(
        date: startDate.add(Duration(days: 10 + i)),
        price: price,
        volatility: volatility * 0.6, // Reduced volatility for clearer pattern
      ));
    }

    // Decline from left shoulder
    final necklinePrice = leftShoulderPeak * 0.88; // Deeper trough for better visibility
    for (int i = 0; i < 5; i++) {
      price = _interpolate(leftShoulderPeak, necklinePrice, i / 4);
      candlesticks.add(_generateCandlestick(
        date: startDate.add(Duration(days: 16 + i)),
        price: price,
        volatility: volatility * 0.7, // Reduced volatility for clearer pattern
      ));
    }

    // Head - more pronounced
    final headPeak = leftShoulderPeak * 1.25; // Much higher head for better visibility
    for (int i = 0; i < 6; i++) { // Increased length for better visibility
      price = _interpolate(necklinePrice, headPeak, i / 5);
      candlesticks.add(_generateCandlestick(
        date: startDate.add(Duration(days: 21 + i)),
        price: price,
        volatility: volatility * 0.6, // Reduced volatility for clearer pattern
      ));
    }

    // Decline from head
    for (int i = 0; i < 5; i++) {
      price = _interpolate(headPeak, necklinePrice * 1.02, i / 4); // Slightly above neckline for realism
      candlesticks.add(_generateCandlestick(
        date: startDate.add(Duration(days: 27 + i)),
        price: price,
        volatility: volatility * 0.7, // Reduced volatility for clearer pattern
      ));
    }

    // Right shoulder - more pronounced
    final rightShoulderPeak = leftShoulderPeak * 0.95; // Slightly lower than left shoulder for realism
    for (int i = 0; i < 6; i++) { // Increased length for better visibility
      price = _interpolate(necklinePrice * 1.02, rightShoulderPeak, i / 5);
      candlesticks.add(_generateCandlestick(
        date: startDate.add(Duration(days: 32 + i)),
        price: price,
        volatility: volatility * 0.6, // Reduced volatility for clearer pattern
      ));
    }

    // Breakdown - more pronounced
    final targetPrice = necklinePrice - (headPeak - necklinePrice);
    for (int i = 0; i < 6; i++) { // Increased length for better visibility
      price = _interpolate(rightShoulderPeak, necklinePrice * 0.90, i / 5); // More pronounced breakdown
      candlesticks.add(_generateCandlestick(
        date: startDate.add(Duration(days: 38 + i)),
        price: price,
        volatility: volatility * 0.8, // Moderate volatility for realistic breakdown
      ));
    }

    // Downtrend after breakdown - more pronounced
    for (int i = 0; i < 6; i++) { // Increased length for better visibility
      price = _interpolate(necklinePrice * 0.90, targetPrice, i / 5);
      candlesticks.add(_generateCandlestick(
        date: startDate.add(Duration(days: 44 + i)),
        price: price,
        volatility: volatility * 1.2, // Moderate volatility for realistic follow-through
      ));
    }

    return candlesticks;
  }

  /// Generates trend lines for a head and shoulders pattern
  List<TrendLine> generateHeadAndShouldersTrendLines(List<CandlestickData> candlesticks) {
    if (candlesticks.length < 50) { // Adjusted for longer pattern
      return [];
    }

    final trendLines = <TrendLine>[];

    // Neckline - more prominent
    trendLines.add(TrendLine.horizontal(
      startDate: candlesticks[16].date,
      endDate: candlesticks[44].date,
      price: (candlesticks[21].low + candlesticks[32].low) / 2,
      color: Colors.blue.shade700, // Darker blue for better visibility
      label: 'Neckline',
      width: 2.0, // Thicker line for better visibility
    ));

    // Left shoulder trend line - more prominent
    trendLines.add(TrendLine.fromPoints(
      startDate: candlesticks[10].date,
      startPrice: candlesticks[10].close,
      endDate: candlesticks[15].date,
      endPrice: candlesticks[15].high,
      color: Colors.orange.shade700, // Darker orange for better visibility
      label: 'Left Shoulder',
      width: 1.5, // Thicker line for better visibility
    ));

    // Head trend line - more prominent
    trendLines.add(TrendLine.fromPoints(
      startDate: candlesticks[21].date,
      startPrice: candlesticks[21].close,
      endDate: candlesticks[26].date,
      endPrice: candlesticks[26].high,
      color: Colors.purple.shade700, // Darker purple for better visibility
      label: 'Head',
      width: 1.5, // Thicker line for better visibility
    ));

    // Right shoulder trend line - more prominent
    trendLines.add(TrendLine.fromPoints(
      startDate: candlesticks[32].date,
      startPrice: candlesticks[32].close,
      endDate: candlesticks[37].date,
      endPrice: candlesticks[37].high,
      color: Colors.orange.shade700, // Darker orange for better visibility
      label: 'Right Shoulder',
      width: 1.5, // Thicker line for better visibility
    ));

    // Breakdown trend line - more prominent
    trendLines.add(TrendLine.fromPoints(
      startDate: candlesticks[38].date,
      startPrice: candlesticks[38].close,
      endDate: candlesticks[49].date,
      endPrice: candlesticks[49].close,
      color: Colors.red.shade700, // Darker red for better visibility
      label: 'Breakdown',
      width: 2.0, // Thicker line for better visibility
      isDashed: true, // Dashed line for better visibility
    ));

    // Target line - added for educational value
    final necklinePrice = (candlesticks[21].low + candlesticks[32].low) / 2;
    final headPeak = candlesticks[26].high;
    final targetPrice = necklinePrice - (headPeak - necklinePrice);

    trendLines.add(TrendLine.horizontal(
      startDate: candlesticks[44].date,
      endDate: candlesticks[49].date,
      price: targetPrice,
      color: Colors.green.shade700, // Dark green for better visibility
      label: 'Target',
      width: 1.5, // Thicker line for better visibility
      isDashed: true, // Dotted line for better visibility
    ));

    return trendLines;
  }

  /// Generates annotations for a head and shoulders pattern
  List<PatternAnnotation> generateHeadAndShouldersAnnotations(List<CandlestickData> candlesticks) {
    if (candlesticks.length < 50) { // Adjusted for longer pattern
      return [];
    }

    final annotations = <PatternAnnotation>[];

    // Left shoulder annotation - more prominent
    annotations.add(PatternAnnotation(
      date: candlesticks[15].date,
      price: candlesticks[15].high * 1.03, // Higher placement for better visibility
      text: 'Left Shoulder',
      color: Colors.orange.shade700, // Darker orange for better visibility
      isImportant: true, // Mark as important for better visibility
    ));

    // Head annotation - more prominent
    annotations.add(PatternAnnotation(
      date: candlesticks[26].date,
      price: candlesticks[26].high * 1.03, // Higher placement for better visibility
      text: 'Head',
      color: Colors.purple.shade700, // Darker purple for better visibility
      isImportant: true, // Mark as important for better visibility
    ));

    // Right shoulder annotation - more prominent
    annotations.add(PatternAnnotation(
      date: candlesticks[37].date,
      price: candlesticks[37].high * 1.03, // Higher placement for better visibility
      text: 'Right Shoulder',
      color: Colors.orange.shade700, // Darker orange for better visibility
      isImportant: true, // Mark as important for better visibility
    ));

    // Neckline annotation - more prominent
    annotations.add(PatternAnnotation(
      date: candlesticks[27].date,
      price: (candlesticks[21].low + candlesticks[32].low) / 2 * 0.97, // Lower placement for better visibility
      text: 'Neckline (Support)',
      color: Colors.blue.shade700, // Darker blue for better visibility
      isImportant: true, // Mark as important for better visibility
    ));

    // Breakdown annotation - more prominent
    annotations.add(PatternAnnotation.breakout(
      date: candlesticks[41].date,
      price: candlesticks[41].low * 0.97, // Lower placement for better visibility
      text: 'Breakdown Point',
      color: Colors.red.shade700, // Darker red for better visibility
    ));

    // Target annotation - more prominent
    final necklinePrice = (candlesticks[21].low + candlesticks[32].low) / 2;
    final headPeak = candlesticks[26].high;
    final targetPrice = necklinePrice - (headPeak - necklinePrice);

    annotations.add(PatternAnnotation.target(
      date: candlesticks[49].date,
      price: targetPrice * 0.97, // Lower placement for better visibility
      text: 'Price Target',
      color: Colors.green.shade700, // Darker green for better visibility
    ));

    // Educational annotation - added for better understanding
    annotations.add(PatternAnnotation(
      date: candlesticks[5].date,
      price: candlesticks[5].high * 1.10, // Higher placement for better visibility
      text: 'Prior Uptrend',
      color: Colors.grey.shade700, // Dark grey for subtle visibility
    ));

    // Measured move annotation - added for educational value
    annotations.add(PatternAnnotation(
      date: candlesticks[26].date,
      price: (headPeak + necklinePrice) / 2, // Middle point between head and neckline
      text: 'Measured Move',
      color: Colors.purple.shade300, // Light purple for subtle visibility
    ));

    return annotations;
  }

  /// Generates support and resistance levels for a head and shoulders pattern
  List<EnhancedSupportResistanceLevel> generateHeadAndShouldersSupportResistance(List<CandlestickData> candlesticks) {
    if (candlesticks.length < 50) { // Adjusted for longer pattern
      return [];
    }

    final levels = <EnhancedSupportResistanceLevel>[];

    // Calculate key price levels
    final necklinePrice = (candlesticks[21].low + candlesticks[32].low) / 2;
    final headPeak = candlesticks[26].high;
    final leftShoulderPeak = candlesticks[15].high;
    final rightShoulderPeak = candlesticks[37].high;
    final targetPrice = necklinePrice - (headPeak - necklinePrice);

    // Neckline as support (before breakdown) - more prominent
    levels.add(EnhancedSupportResistanceLevel(
      price: necklinePrice,
      isSupport: true,
      isStrong: true,
      startIndex: 16,
      endIndex: 38,
      label: 'Neckline (Support)',
      color: Colors.blue.shade700, // Darker blue for better visibility
    ));

    // Neckline as resistance (after breakdown) - more prominent
    levels.add(EnhancedSupportResistanceLevel(
      price: necklinePrice,
      isSupport: false,
      isStrong: true,
      startIndex: 39,
      endIndex: candlesticks.length - 1,
      label: 'Neckline (Resistance)',
      color: Colors.blue.shade700, // Darker blue for better visibility
    ));

    // Head peak as strong resistance - more prominent
    levels.add(EnhancedSupportResistanceLevel(
      price: headPeak,
      isSupport: false,
      isStrong: true,
      startIndex: 27,
      endIndex: candlesticks.length - 1,
      label: 'Head Resistance',
      color: Colors.purple.shade700, // Darker purple for better visibility
    ));

    // Left shoulder peak as resistance - added for educational value
    levels.add(EnhancedSupportResistanceLevel(
      price: leftShoulderPeak,
      isSupport: false,
      isStrong: false,
      startIndex: 16,
      endIndex: candlesticks.length - 1,
      label: 'Left Shoulder Resistance',
      color: Colors.orange.shade700, // Darker orange for better visibility
    ));

    // Right shoulder peak as resistance - added for educational value
    levels.add(EnhancedSupportResistanceLevel(
      price: rightShoulderPeak,
      isSupport: false,
      isStrong: false,
      startIndex: 38,
      endIndex: candlesticks.length - 1,
      label: 'Right Shoulder Resistance',
      color: Colors.orange.shade700, // Darker orange for better visibility
    ));

    // Target level as support - more prominent
    levels.add(EnhancedSupportResistanceLevel(
      price: targetPrice,
      isSupport: true,
      isStrong: true,
      startIndex: 44,
      endIndex: candlesticks.length - 1,
      label: 'Price Target',
      color: Colors.green.shade700, // Darker green for better visibility
    ));

    return levels;
  }

  /// Generates a candlestick
  CandlestickData _generateCandlestick({
    required DateTime date,
    required double price,
    required double volatility,
  }) {
    final range = price * volatility * 0.1;
    final open = price * (1 + (0.5 - _random.nextDouble()) * 0.02);
    final close = price * (1 + (0.5 - _random.nextDouble()) * 0.02);
    final high = max(open, close) + _random.nextDouble() * range;
    final low = min(open, close) - _random.nextDouble() * range;
    final volume = 1000 + _random.nextInt(9000);

    return CandlestickData(
      date: date,
      open: open,
      high: high,
      low: low,
      close: close,
      volume: volume.toDouble(),
    );
  }

  /// Generates the next price with a trend bias
  double _generateNextPrice(double currentPrice, {
    double trend = 0.0,
    double volatility = 0.5,
  }) {
    final change = currentPrice * volatility * 0.05 * (trend + (0.5 - _random.nextDouble()));
    return max(0.01, currentPrice + change);
  }

  /// Interpolates between two values
  double _interpolate(double start, double end, double t) {
    return start + (end - start) * t;
  }

  /// Generates enhanced pattern data for a given pattern
  EnhancedPatternData generateEnhancedPattern(ChartPattern pattern, {
    int length = 40,
    double volatility = 0.5,
    double basePrice = 100.0,
  }) {
    List<CandlestickData> candlesticks;
    List<TrendLine> trendLines;
    List<PatternAnnotation> annotations;
    List<EnhancedSupportResistanceLevel> supportResistanceLevels;

    switch (pattern.id) {
      case 'head_and_shoulders':
        candlesticks = generateHeadAndShoulders(
          length: length,
          volatility: volatility,
          basePrice: basePrice,
        );
        trendLines = generateHeadAndShouldersTrendLines(candlesticks);
        annotations = generateHeadAndShouldersAnnotations(candlesticks);
        supportResistanceLevels = generateHeadAndShouldersSupportResistance(candlesticks);
        break;
      // Add cases for other patterns here
      default:
        // For now, just generate head and shoulders for all patterns
        candlesticks = generateHeadAndShoulders(
          length: length,
          volatility: volatility,
          basePrice: basePrice,
        );
        trendLines = generateHeadAndShouldersTrendLines(candlesticks);
        annotations = generateHeadAndShouldersAnnotations(candlesticks);
        supportResistanceLevels = generateHeadAndShouldersSupportResistance(candlesticks);
    }

    return EnhancedPatternData(
      pattern: pattern,
      candlesticks: candlesticks,
      trendLines: trendLines,
      annotations: annotations,
      supportResistanceLevels: supportResistanceLevels,
    );
  }
}

/// A class that holds enhanced pattern data
class EnhancedPatternData {
  /// The pattern
  final ChartPattern pattern;

  /// The candlestick data
  final List<CandlestickData> candlesticks;

  /// The trend lines
  final List<TrendLine> trendLines;

  /// The annotations
  final List<PatternAnnotation> annotations;

  /// The support and resistance levels
  final List<EnhancedSupportResistanceLevel> supportResistanceLevels;

  /// Creates a new enhanced pattern data
  const EnhancedPatternData({
    required this.pattern,
    required this.candlesticks,
    required this.trendLines,
    required this.annotations,
    required this.supportResistanceLevels,
  });
}
