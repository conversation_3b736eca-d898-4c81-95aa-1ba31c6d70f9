import 'dart:developer' as developer;
import 'package:learn_chart_patterns/generators/ascending_triangle_generator.dart';
import 'package:learn_chart_patterns/generators/base_generator.dart';
import 'package:learn_chart_patterns/generators/cup_and_handle_generator.dart';
import 'package:learn_chart_patterns/generators/double_top_generator.dart';
import 'package:learn_chart_patterns/generators/head_and_shoulders_generator.dart';

/// Factory for creating pattern generators
class PatternGeneratorFactory {
  /// Creates a generator for the specified pattern
  static BaseGenerator createGenerator({
    required String patternId,
    int? seed,
    double basePrice = 100.0,
    double volatility = 0.5,
    bool? isBullish,
  }) {
    switch (patternId) {
      case 'head_and_shoulders':
        return HeadAndShouldersGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? false,
        );
      case 'inverse_head_and_shoulders':
        return HeadAndShouldersGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );
      case 'double_top':
        return DoubleTopGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? false,
        );
      case 'double_bottom':
        // Double bottom is just an inverted double top
        return DoubleTopGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );
      case 'cup_and_handle':
        return CupAndHandleGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );
      case 'ascending_triangle':
        return AscendingTriangleGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );
      // Handle Triple Top/Bottom patterns using Double Top/Bottom generator
      case 'triple_top':
        return DoubleTopGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? false,
        );
      case 'triple_bottom':
        return DoubleTopGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );

      // Handle Descending Triangle using Ascending Triangle generator
      case 'descending_triangle':
        return AscendingTriangleGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? false,
        );

      // Handle Symmetrical Triangle using Ascending Triangle generator
      case 'symmetrical_triangle':
        return AscendingTriangleGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );

      // Handle Flag patterns using Double Top/Bottom generator
      case 'bullish_flag':
      case 'bearish_flag':
      case 'bullish_pennant':
      case 'bearish_pennant':
        final bool isBullishPattern = patternId.startsWith('bullish');
        return DoubleTopGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? isBullishPattern,
        );

      // Handle Wedge patterns using Ascending Triangle generator
      case 'rising_wedge':
      case 'falling_wedge':
        final bool isBullishPattern = patternId == 'falling_wedge';
        return AscendingTriangleGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? isBullishPattern,
        );

      // Handle Rectangle pattern using Double Top generator
      case 'rectangle':
        return DoubleTopGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );

      // Handle Rounding patterns using Cup and Handle generator
      case 'rounding_bottom':
        return CupAndHandleGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );
      case 'rounding_top':
        return CupAndHandleGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? false,
        );

      // Fallback for any other patterns
      default:
        developer.log('Warning: Using fallback generator for unknown pattern ID: $patternId');
        return DoubleTopGenerator(
          seed: seed,
          basePrice: basePrice,
          volatility: volatility,
          isBullish: isBullish ?? true,
        );
    }
  }
}
