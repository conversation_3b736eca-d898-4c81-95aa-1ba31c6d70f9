import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/trading_course.dart';
import 'package:learn_chart_patterns/providers/trading_course_provider.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

/// The lesson screen of the app
class LessonScreen extends StatefulWidget {
  /// The course ID
  final String courseId;
  
  /// The lesson ID
  final String lessonId;
  
  /// Creates a new lesson screen
  const LessonScreen({
    super.key,
    required this.courseId,
    required this.lessonId,
  });

  @override
  State<LessonScreen> createState() => _LessonScreenState();
}

class _LessonScreenState extends State<LessonScreen> {
  /// The current lesson
  Lesson? _lesson;
  
  /// The current course
  TradingCourse? _course;
  
  /// The current module
  CourseModule? _module;
  
  /// The current lesson index
  int _lessonIndex = 0;
  
  /// The current module index
  int _moduleIndex = 0;
  
  /// Whether the lesson is completed
  bool _isCompleted = false;
  
  @override
  void initState() {
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLesson();
    });
  }
  
  /// Loads the lesson
  void _loadLesson() {
    final provider = Provider.of<TradingCourseProvider>(context, listen: false);
    
    // Get the course
    final course = provider.getCourseById(widget.courseId);
    if (course == null) return;
    
    // Find the lesson and its module
    CourseModule? foundModule;
    Lesson? foundLesson;
    int moduleIndex = 0;
    int lessonIndex = 0;
    
    for (int i = 0; i < course.modules.length; i++) {
      final module = course.modules[i];
      
      for (int j = 0; j < module.lessons.length; j++) {
        final lesson = module.lessons[j];
        
        if (lesson.id == widget.lessonId) {
          foundModule = module;
          foundLesson = lesson;
          moduleIndex = i;
          lessonIndex = j;
          break;
        }
      }
      
      if (foundLesson != null) break;
    }
    
    if (foundLesson == null) return;
    
    // Check if the lesson is completed
    final isCompleted = provider.isLessonCompleted(widget.lessonId);
    
    setState(() {
      _course = course;
      _module = foundModule;
      _lesson = foundLesson;
      _moduleIndex = moduleIndex;
      _lessonIndex = lessonIndex;
      _isCompleted = isCompleted;
    });
  }
  
  /// Marks the lesson as completed
  void _markAsCompleted() {
    if (_lesson == null) return;
    
    final provider = Provider.of<TradingCourseProvider>(context, listen: false);
    provider.markLessonCompleted(_lesson!.id);
    
    setState(() {
      _isCompleted = true;
    });
  }
  
  /// Navigates to the next lesson
  void _goToNextLesson() {
    if (_course == null || _module == null) return;
    
    // Check if there's another lesson in the current module
    if (_lessonIndex < _module!.lessons.length - 1) {
      // Go to the next lesson in the current module
      final nextLesson = _module!.lessons[_lessonIndex + 1];
      
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => LessonScreen(
            courseId: widget.courseId,
            lessonId: nextLesson.id,
          ),
        ),
      );
    } else if (_moduleIndex < _course!.modules.length - 1) {
      // Go to the first lesson of the next module
      final nextModule = _course!.modules[_moduleIndex + 1];
      final nextLesson = nextModule.lessons.first;
      
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => LessonScreen(
            courseId: widget.courseId,
            lessonId: nextLesson.id,
          ),
        ),
      );
    } else {
      // This is the last lesson of the last module
      // Go back to the course detail screen
      Navigator.pop(context);
    }
  }
  
  /// Navigates to the previous lesson
  void _goToPreviousLesson() {
    if (_course == null || _module == null) return;
    
    // Check if there's a previous lesson in the current module
    if (_lessonIndex > 0) {
      // Go to the previous lesson in the current module
      final previousLesson = _module!.lessons[_lessonIndex - 1];
      
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => LessonScreen(
            courseId: widget.courseId,
            lessonId: previousLesson.id,
          ),
        ),
      );
    } else if (_moduleIndex > 0) {
      // Go to the last lesson of the previous module
      final previousModule = _course!.modules[_moduleIndex - 1];
      final previousLesson = previousModule.lessons.last;
      
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => LessonScreen(
            courseId: widget.courseId,
            lessonId: previousLesson.id,
          ),
        ),
      );
    } else {
      // This is the first lesson of the first module
      // Do nothing
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (_lesson == null || _module == null || _course == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Loading...'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _module!.title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(150),
              ),
            ),
            Text(
              _lesson!.title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          // Lesson completion status
          Padding(
            padding: const EdgeInsets.only(right: AppConstants.paddingMedium),
            child: _isCompleted
                ? const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                  )
                : const Icon(
                    Icons.radio_button_unchecked,
                    color: Colors.grey,
                  ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Lesson content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Lesson metadata
                  Row(
                    children: [
                      _buildMetadataChip(
                        label: _lesson!.contentTypeText,
                        color: theme.colorScheme.primary,
                        icon: _lesson!.contentTypeIcon,
                      ),
                      
                      const SizedBox(width: AppConstants.spacingSmall),
                      
                      _buildMetadataChip(
                        label: '${_lesson!.durationMinutes} min',
                        color: theme.colorScheme.secondary,
                        icon: Icons.access_time,
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.spacingMedium),
                  
                  // Lesson content
                  _buildLessonContent(),
                  
                  // Ad banner
                  if (!_course!.isPremium) ...[
                    const SizedBox(height: AppConstants.spacingLarge),
                    const AdBanner(isHomeBanner: true),
                  ],
                  
                  const SizedBox(height: AppConstants.spacingLarge),
                ],
              ),
            ),
          ),
          
          // Bottom navigation
          _buildBottomNavigation(),
        ],
      ),
    );
  }
  
  /// Builds the lesson content
  Widget _buildLessonContent() {
    if (_lesson == null) return const SizedBox.shrink();
    
    switch (_lesson!.contentType) {
      case ContentType.text:
        return MarkdownBody(
          data: _lesson!.content,
          styleSheet: MarkdownStyleSheet(
            p: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              height: 1.6,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(220),
            ),
            h1: TextStyle(
              fontSize: AppConstants.fontSizeLarge * 1.2,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            h2: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            h3: TextStyle(
              fontSize: AppConstants.fontSizeMedium * 1.2,
              fontWeight: FontWeight.bold,
            ),
            listBullet: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: AppConstants.fontSizeMedium * 1.2,
            ),
          ),
          onTapLink: (text, href, title) {
            if (href != null) {
              launchUrl(Uri.parse(href));
            }
          },
        );
      
      case ContentType.video:
        // In a real app, this would be a video player
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(20),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              child: const Center(
                child: Icon(
                  Icons.play_circle_fill,
                  size: 64,
                  color: Colors.white,
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            MarkdownBody(
              data: _lesson!.content,
              styleSheet: MarkdownStyleSheet(
                p: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  height: 1.6,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(220),
                ),
              ),
            ),
          ],
        );
      
      case ContentType.chart:
        // In a real app, this would be an interactive chart
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 300,
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(20),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              child: const Center(
                child: Icon(
                  Icons.candlestick_chart,
                  size: 64,
                  color: Colors.white,
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            MarkdownBody(
              data: _lesson!.content,
              styleSheet: MarkdownStyleSheet(
                p: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  height: 1.6,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(220),
                ),
              ),
            ),
          ],
        );
      
      case ContentType.quiz:
        // In a real app, this would be an interactive quiz
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarkdownBody(
              data: _lesson!.content,
              styleSheet: MarkdownStyleSheet(
                p: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  height: 1.6,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(220),
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(5),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: Colors.black.withAlpha(10),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quiz',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.spacingMedium),
                  
                  const Text(
                    'Quiz content would be displayed here in a real app.',
                  ),
                ],
              ),
            ),
          ],
        );
    }
  }
  
  /// Builds the bottom navigation
  Widget _buildBottomNavigation() {
    final theme = Theme.of(context);
    
    // Determine if this is the first or last lesson
    final isFirstLesson = _moduleIndex == 0 && _lessonIndex == 0;
    final isLastModule = _moduleIndex == _course!.modules.length - 1;
    final isLastLesson = _lessonIndex == _module!.lessons.length - 1;
    final isLastLessonOfCourse = isLastModule && isLastLesson;
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Previous button
          if (!isFirstLesson)
            OutlinedButton.icon(
              onPressed: _goToPreviousLesson,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Previous'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
              ),
            )
          else
            const SizedBox.shrink(),
          
          const Spacer(),
          
          // Complete button or Next button
          if (!_isCompleted)
            ElevatedButton.icon(
              onPressed: _markAsCompleted,
              icon: const Icon(Icons.check),
              label: const Text('Mark as Completed'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
              ),
            )
          else
            ElevatedButton.icon(
              onPressed: _goToNextLesson,
              icon: isLastLessonOfCourse
                  ? const Icon(Icons.check_circle)
                  : const Icon(Icons.arrow_forward),
              label: Text(isLastLessonOfCourse ? 'Finish Course' : 'Next Lesson'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  /// Builds a metadata chip
  Widget _buildMetadataChip({
    required String label,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall / 2,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: AppConstants.iconSizeSmall,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
