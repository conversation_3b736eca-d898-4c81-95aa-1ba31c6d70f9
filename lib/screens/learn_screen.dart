import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/providers/pattern_provider.dart';
import 'package:learn_chart_patterns/providers/progress_provider.dart';
import 'package:learn_chart_patterns/providers/theme_provider.dart';
import 'package:learn_chart_patterns/screens/pattern_detail_screen.dart';
import 'package:learn_chart_patterns/widgets/pattern_card.dart';
import 'package:provider/provider.dart';

/// The learn screen of the app
class LearnScreen extends StatefulWidget {
  /// Creates a new learn screen
  const LearnScreen({super.key});

  @override
  State<LearnScreen> createState() => _LearnScreenState();
}

class _LearnScreenState extends State<LearnScreen> with SingleTickerProviderStateMixin {
  /// Tab controller for the different pattern categories
  late TabController _tabController;

  /// The currently selected difficulty filter
  PatternDifficulty? _selectedDifficulty;

  /// The currently selected trend filter
  PatternTrend? _selectedTrend;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final patternProvider = context.watch<PatternProvider>();
    final progressProvider = context.watch<ProgressProvider>();
    final themeProvider = context.watch<ThemeProvider>();
    final chartColors = themeProvider.getChartColors(Theme.of(context).brightness);
    final theme = Theme.of(context);

    // Get patterns based on the selected tab
    List<ChartPattern> patterns;
    switch (_tabController.index) {
      case 0: // All patterns
        patterns = patternProvider.allPatterns;
        break;
      case 1: // Learned patterns
        patterns = patternProvider.allPatterns
            .where((pattern) => progressProvider.isPatternLearned(pattern.id))
            .toList();
        break;
      case 2: // Unlearned patterns
        patterns = patternProvider.allPatterns
            .where((pattern) => !progressProvider.isPatternLearned(pattern.id))
            .toList();
        break;
      default:
        patterns = patternProvider.allPatterns;
    }

    // Apply difficulty filter
    if (_selectedDifficulty != null) {
      patterns = patterns
          .where((pattern) => pattern.difficulty == _selectedDifficulty)
          .toList();
    }

    // Apply trend filter
    if (_selectedTrend != null) {
      patterns = patterns
          .where((pattern) => pattern.trend == _selectedTrend)
          .toList();
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Learn Patterns',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Learned'),
            Tab(text: 'Unlearned'),
          ],
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withAlpha(150),
          indicatorColor: theme.colorScheme.primary,
          indicatorWeight: 3,
          labelStyle: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          unselectedLabelStyle: theme.textTheme.titleSmall,
          onTap: (_) {
            setState(() {});
          },
        ),
        actions: [
          // Filter button
          Container(
            margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.filter_list),
              color: theme.colorScheme.primary,
              onPressed: _showFilterDialog,
            ),
          ),
        ],
      ),
      body: patterns.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.symmetric(
                vertical: AppConstants.paddingMedium,
              ),
              itemCount: patterns.length,
              itemBuilder: (context, index) {
                final pattern = patterns[index];
                final isLearned = progressProvider.isPatternLearned(pattern.id);
                final accuracy = progressProvider.getPatternAccuracy(pattern.id);

                return PatternCard(
                  pattern: pattern,
                  isLearned: isLearned,
                  accuracy: accuracy,
                  chartColors: chartColors,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PatternDetailScreen(pattern: pattern),
                      ),
                    );
                  },
                );
              },
            ),
    );
  }

  /// Shows the filter dialog
  void _showFilterDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                'Filter Patterns',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Difficulty filter
                  Text(
                    'Difficulty',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: AppConstants.spacingSmall),

                  Wrap(
                    spacing: AppConstants.spacingSmall,
                    children: [
                      _buildFilterChip(
                        label: 'Beginner',
                        color: const Color(0xFF4CAF50), // Green
                        selected: _selectedDifficulty == PatternDifficulty.beginner,
                        onSelected: (selected) {
                          setState(() {
                            _selectedDifficulty = selected
                                ? PatternDifficulty.beginner
                                : null;
                          });
                        },
                      ),

                      _buildFilterChip(
                        label: 'Intermediate',
                        color: const Color(0xFFFFA726), // Orange
                        selected: _selectedDifficulty == PatternDifficulty.intermediate,
                        onSelected: (selected) {
                          setState(() {
                            _selectedDifficulty = selected
                                ? PatternDifficulty.intermediate
                                : null;
                          });
                        },
                      ),

                      _buildFilterChip(
                        label: 'Advanced',
                        color: const Color(0xFFFF6B6B), // Coral
                        selected: _selectedDifficulty == PatternDifficulty.advanced,
                        onSelected: (selected) {
                          setState(() {
                            _selectedDifficulty = selected
                                ? PatternDifficulty.advanced
                                : null;
                          });
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: AppConstants.spacingMedium),

                  // Trend filter
                  Text(
                    'Trend',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: AppConstants.spacingSmall),

                  Wrap(
                    spacing: AppConstants.spacingSmall,
                    children: [
                      _buildFilterChip(
                        label: 'Bullish',
                        color: const Color(0xFF4CAF50), // Green
                        selected: _selectedTrend == PatternTrend.bullish,
                        onSelected: (selected) {
                          setState(() {
                            _selectedTrend = selected
                                ? PatternTrend.bullish
                                : null;
                          });
                        },
                      ),

                      _buildFilterChip(
                        label: 'Bearish',
                        color: const Color(0xFFFF6B6B), // Coral
                        selected: _selectedTrend == PatternTrend.bearish,
                        onSelected: (selected) {
                          setState(() {
                            _selectedTrend = selected
                                ? PatternTrend.bearish
                                : null;
                          });
                        },
                      ),

                      _buildFilterChip(
                        label: 'Neutral',
                        color: const Color(0xFF4A6FFF), // Blue
                        selected: _selectedTrend == PatternTrend.neutral,
                        onSelected: (selected) {
                          setState(() {
                            _selectedTrend = selected
                                ? PatternTrend.neutral
                                : null;
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                // Clear filters button
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedDifficulty = null;
                      _selectedTrend = null;
                    });
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: theme.colorScheme.onSurface.withAlpha(180),
                  ),
                  child: const Text('Clear Filters'),
                ),

                // Apply button
                ElevatedButton(
                  onPressed: () {
                    this.setState(() {});
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Builds a filter chip
  Widget _buildFilterChip({
    required String label,
    required bool selected,
    required ValueChanged<bool> onSelected,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            color: selected ? color : Theme.of(context).colorScheme.onSurface,
            fontWeight: selected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: selected,
        onSelected: onSelected,
        showCheckmark: false,
        backgroundColor: Colors.white,
        selectedColor: color.withAlpha(30),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          side: BorderSide(
            color: selected ? color : Colors.grey.withAlpha(50),
            width: 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingSmall,
          vertical: AppConstants.paddingSmall / 2,
        ),
      ),
    );
  }

  /// Builds the empty state
  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off,
              size: AppConstants.iconSizeLarge,
              color: theme.colorScheme.primary,
            ),
          ),

          const SizedBox(height: AppConstants.spacingMedium),

          Text(
            'No patterns found',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: AppConstants.spacingSmall),

          Text(
            'Try changing the filters',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(180),
            ),
          ),

          const SizedBox(height: AppConstants.spacingLarge),

          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedDifficulty = null;
                _selectedTrend = null;
                _tabController.index = 0;
              });
            },
            icon: const Icon(Icons.filter_alt_off),
            label: const Text('Clear Filters'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
                vertical: AppConstants.paddingMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
