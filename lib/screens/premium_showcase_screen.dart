import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/constants/premium_pattern_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/services/premium_content_service.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';
import 'package:learn_chart_patterns/services/visual_enhancement_service.dart';
import 'package:learn_chart_patterns/widgets/enhanced_candlestick_chart.dart';
import 'package:provider/provider.dart';

/// A screen that showcases premium content
class PremiumShowcaseScreen extends StatefulWidget {
  /// Creates a new premium showcase screen
  const PremiumShowcaseScreen({super.key});

  @override
  State<PremiumShowcaseScreen> createState() => _PremiumShowcaseScreenState();
}

class _PremiumShowcaseScreenState extends State<PremiumShowcaseScreen> with SingleTickerProviderStateMixin {
  /// Tab controller
  late TabController _tabController;

  /// Premium content service
  final PremiumContentService _premiumContentService = PremiumContentService();

  /// Purchase service
  final PurchaseService _purchaseService = PurchaseService();

  /// Visual enhancement service
  late VisualEnhancementService _visualEnhancementService;

  /// Animation controller
  late AnimationController _animationController;

  /// Animations
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize tab controller
    _tabController = TabController(length: 4, vsync: this);

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Initialize animations
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _slideAnimation = Tween<double>(
      begin: 50,
      end: 0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuad,
    ));

    // Start animation
    _animationController.forward();

    // Get visual enhancement service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _visualEnhancementService = Provider.of<VisualEnhancementService>(context, listen: false);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: const Text(
                'Premium Content',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Background gradient
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.purple.shade800,
                          Colors.purple.shade500,
                          Colors.indigo.shade500,
                        ],
                      ),
                    ),
                  ),

                  // Pattern overlay
                  Opacity(
                    opacity: 0.1,
                    child: Image.asset(
                      'assets/images/pattern_background.png',
                      fit: BoxFit.cover,
                    ),
                  ),

                  // Premium icon
                  Positioned(
                    right: AppConstants.paddingLarge,
                    bottom: AppConstants.paddingLarge,
                    child: Icon(
                      Icons.star,
                      size: AppConstants.iconSizeLarge * 2,
                      color: Colors.amber.withAlpha(150),
                    ),
                  ),

                  // Gradient overlay
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    height: 100,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withAlpha(150),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            bottom: TabBar(
              controller: _tabController,
              indicatorColor: Colors.amber,
              tabs: const [
                Tab(text: 'Harmonic'),
                Tab(text: 'Elliott Wave'),
                Tab(text: 'Candlestick'),
                Tab(text: 'Advanced'),
              ],
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Opacity(
                  opacity: _fadeAnimation.value,
                  child: Transform.translate(
                    offset: Offset(0, _slideAnimation.value),
                    child: child,
                  ),
                );
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Premium benefits
                  Padding(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    child: Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Unlock Premium Features',
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),

                            const SizedBox(height: AppConstants.spacingMedium),

                            _buildBenefitItem(
                              icon: Icons.auto_awesome,
                              title: '30+ Advanced Chart Patterns',
                              description: 'Master professional-grade patterns used by expert traders',
                            ),

                            _buildBenefitItem(
                              icon: Icons.menu_book,
                              title: 'Detailed Trading Strategies',
                              description: 'Learn how to trade each pattern with step-by-step guides',
                            ),

                            _buildBenefitItem(
                              icon: Icons.candlestick_chart,
                              title: 'Interactive Pattern Analysis',
                              description: 'Explore patterns with annotations and trend lines',
                            ),

                            _buildBenefitItem(
                              icon: Icons.block,
                              title: 'Ad-Free Experience',
                              description: 'Enjoy learning without interruptions',
                            ),

                            const SizedBox(height: AppConstants.spacingLarge),

                            // Upgrade button
                            Center(
                              child: ElevatedButton.icon(
                                onPressed: () => _showUpgradeOptions(context),
                                icon: const Icon(Icons.star),
                                label: const Text('Upgrade Now'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.amber,
                                  foregroundColor: Colors.black87,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppConstants.paddingLarge,
                                    vertical: AppConstants.paddingMedium,
                                  ),
                                  textStyle: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: AppConstants.fontSizeMedium,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Pattern preview
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                    ),
                    child: Text(
                      'Premium Pattern Preview',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const SizedBox(height: AppConstants.spacingMedium),

                  // Pattern tabs
                  SizedBox(
                    height: 400,
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // Harmonic patterns
                        _buildPatternGrid(PremiumPatternData.harmonicPatterns),

                        // Elliott Wave patterns
                        _buildPatternGrid(PremiumPatternData.elliottWavePatterns),

                        // Advanced candlestick patterns
                        _buildPatternGrid(PremiumPatternData.advancedCandlestickPatterns),

                        // Advanced chart patterns
                        _buildPatternGrid(PremiumPatternData.advancedChartPatterns),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppConstants.spacingLarge),

                  // Testimonials
                  Padding(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    child: Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'What Our Users Say',
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),

                            const SizedBox(height: AppConstants.spacingMedium),

                            _buildTestimonial(
                              name: 'Sarah J.',
                              rating: 5,
                              text: 'The premium patterns have completely transformed my trading. I can now identify setups I never saw before!',
                            ),

                            _buildTestimonial(
                              name: 'Michael T.',
                              rating: 5,
                              text: 'Worth every penny. The harmonic pattern section alone has paid for the upgrade many times over.',
                            ),

                            _buildTestimonial(
                              name: 'David R.',
                              rating: 4,
                              text: 'Great content and explanations. The interactive charts make learning these complex patterns much easier.',
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: AppConstants.spacingLarge * 2),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a benefit item
  Widget _buildBenefitItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingSmall),
            decoration: BoxDecoration(
              color: Colors.amber.withAlpha(50),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            child: Icon(
              icon,
              color: Colors.amber,
              size: AppConstants.iconSizeMedium,
            ),
          ),

          const SizedBox(width: AppConstants.spacingMedium),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: AppConstants.fontSizeMedium,
                  ),
                ),

                const SizedBox(height: AppConstants.spacingSmall / 2),

                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a pattern grid
  Widget _buildPatternGrid(List<ChartPattern> patterns) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: AppConstants.paddingMedium,
        mainAxisSpacing: AppConstants.paddingMedium,
      ),
      itemCount: patterns.length,
      itemBuilder: (context, index) {
        final pattern = patterns[index];
        return _buildPatternCard(pattern);
      },
    );
  }

  /// Builds a pattern card
  Widget _buildPatternCard(ChartPattern pattern) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: InkWell(
        onTap: () => _showPatternPreview(context, pattern),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Stack(
          children: [
            // Pattern content
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Pattern icon
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingSmall),
                    decoration: BoxDecoration(
                      color: pattern.color?.withAlpha(30) ?? Colors.grey.withAlpha(30),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                    child: Icon(
                      Icons.candlestick_chart,
                      color: pattern.color ?? Colors.grey,
                      size: AppConstants.iconSizeMedium,
                    ),
                  ),

                  const SizedBox(height: AppConstants.spacingMedium),

                  // Pattern name
                  Text(
                    pattern.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: AppConstants.spacingSmall / 2),

                  // Pattern category
                  Text(
                    pattern.category ?? 'Premium',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: pattern.color ?? Colors.grey,
                    ),
                  ),

                  const SizedBox(height: AppConstants.spacingSmall),

                  // Pattern description
                  Expanded(
                    child: Text(
                      pattern.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Pattern reliability
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: AppConstants.iconSizeSmall,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Reliability: ${pattern.reliability ?? 4}/5',
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Premium badge
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.amber,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                ),
                child: const Text(
                  'PREMIUM',
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a testimonial
  Widget _buildTestimonial({
    required String name,
    required int rating,
    required String text,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Rating
          Row(
            children: [
              ...List.generate(
                rating,
                (index) => const Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: AppConstants.iconSizeSmall,
                ),
              ),
              ...List.generate(
                5 - rating,
                (index) => Icon(
                  Icons.star,
                  color: Colors.grey[300],
                  size: AppConstants.iconSizeSmall,
                ),
              ),

              const SizedBox(width: AppConstants.spacingSmall),

              Text(
                name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.spacingSmall),

          // Testimonial text
          Text(
            text,
            style: TextStyle(
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),

          const Divider(),
        ],
      ),
    );
  }

  /// Shows a pattern preview
  void _showPatternPreview(BuildContext context, ChartPattern pattern) {
    // Provide haptic feedback
    _visualEnhancementService.performHapticFeedback(HapticFeedbackType.medium);

    // Show the premium upgrade dialog
    _premiumContentService.showPremiumPatternsUpgradeDialog(context);
  }

  /// Shows upgrade options
  void _showUpgradeOptions(BuildContext context) {
    // Provide haptic feedback
    _visualEnhancementService.performHapticFeedback(HapticFeedbackType.medium);

    // Show the premium upgrade dialog
    _premiumContentService.showPremiumAccessUpgradeDialog(context);
  }
}
