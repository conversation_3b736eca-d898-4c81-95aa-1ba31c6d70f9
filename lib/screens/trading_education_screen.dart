import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/trading_course.dart';
import 'package:learn_chart_patterns/providers/trading_course_provider.dart';
import 'package:learn_chart_patterns/screens/course_detail_screen.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:provider/provider.dart';

/// The trading education screen of the app
class TradingEducationScreen extends StatefulWidget {
  /// Creates a new trading education screen
  const TradingEducationScreen({super.key});

  @override
  State<TradingEducationScreen> createState() => _TradingEducationScreenState();
}

class _TradingEducationScreenState extends State<TradingEducationScreen> with SingleTickerProviderStateMixin {
  /// Tab controller
  late TabController _tabController;
  
  /// Selected difficulty filter
  DifficultyLevel? _selectedDifficulty;
  
  /// Whether to show only premium courses
  bool _showOnlyPremium = false;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    // Initialize the course provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<TradingCourseProvider>(context, listen: false);
      provider.init();
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Trading Education',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withAlpha(150),
          indicatorColor: theme.colorScheme.primary,
          tabs: const [
            Tab(text: 'Forex'),
            Tab(text: 'Crypto'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Filters
          _buildFilters(),
          
          // Course list
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Forex courses
                _buildCourseList(CourseType.forex),
                
                // Crypto courses
                _buildCourseList(CourseType.crypto),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Builds the filters section
  Widget _buildFilters() {
    final theme = Theme.of(context);
    
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingSmall),
          
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Difficulty filters
                _buildFilterChip(
                  label: 'All Levels',
                  selected: _selectedDifficulty == null,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedDifficulty = null;
                      });
                    }
                  },
                  color: theme.colorScheme.primary,
                ),
                
                _buildFilterChip(
                  label: 'Beginner',
                  selected: _selectedDifficulty == DifficultyLevel.beginner,
                  onSelected: (selected) {
                    setState(() {
                      _selectedDifficulty = selected ? DifficultyLevel.beginner : null;
                    });
                  },
                  color: Colors.green,
                ),
                
                _buildFilterChip(
                  label: 'Intermediate',
                  selected: _selectedDifficulty == DifficultyLevel.intermediate,
                  onSelected: (selected) {
                    setState(() {
                      _selectedDifficulty = selected ? DifficultyLevel.intermediate : null;
                    });
                  },
                  color: Colors.orange,
                ),
                
                _buildFilterChip(
                  label: 'Advanced',
                  selected: _selectedDifficulty == DifficultyLevel.advanced,
                  onSelected: (selected) {
                    setState(() {
                      _selectedDifficulty = selected ? DifficultyLevel.advanced : null;
                    });
                  },
                  color: Colors.red,
                ),
                
                const SizedBox(width: AppConstants.spacingMedium),
                
                // Premium filter
                _buildFilterChip(
                  label: 'Premium',
                  selected: _showOnlyPremium,
                  onSelected: (selected) {
                    setState(() {
                      _showOnlyPremium = selected;
                    });
                  },
                  color: Colors.amber,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Builds a filter chip
  Widget _buildFilterChip({
    required String label,
    required bool selected,
    required ValueChanged<bool> onSelected,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            color: selected ? color : Theme.of(context).colorScheme.onSurface,
            fontWeight: selected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: selected,
        onSelected: onSelected,
        showCheckmark: false,
        backgroundColor: Colors.white,
        selectedColor: color.withAlpha(30),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          side: BorderSide(
            color: selected ? color : Colors.grey.withAlpha(50),
            width: 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingSmall,
          vertical: AppConstants.paddingSmall / 2,
        ),
      ),
    );
  }
  
  /// Builds the course list
  Widget _buildCourseList(CourseType type) {
    return Consumer<TradingCourseProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        // Get courses based on type
        List<TradingCourse> courses = type == CourseType.forex
            ? provider.forexCourses
            : provider.cryptoCourses;
        
        // Apply difficulty filter
        if (_selectedDifficulty != null) {
          courses = courses.where((c) => c.difficulty == _selectedDifficulty).toList();
        }
        
        // Apply premium filter
        if (_showOnlyPremium) {
          courses = courses.where((c) => c.isPremium).toList();
        }
        
        if (courses.isEmpty) {
          return _buildEmptyState();
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          itemCount: courses.length + 1, // +1 for the ad banner
          itemBuilder: (context, index) {
            if (index == courses.length) {
              return const AdBanner(isHomeBanner: true);
            }
            
            final course = courses[index];
            return _buildCourseCard(course, provider);
          },
        );
      },
    );
  }
  
  /// Builds a course card
  Widget _buildCourseCard(TradingCourse course, TradingCourseProvider provider) {
    final theme = Theme.of(context);
    final completionPercentage = course.getCompletionPercentage(provider.completionStatus);
    
    return Card(
      elevation: 0,
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CourseDetailScreen(courseId: course.id),
            ),
          );
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Course header
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Course icon
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingSmall),
                    decoration: BoxDecoration(
                      color: course.color.withAlpha(30),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                    ),
                    child: Icon(
                      course.icon,
                      color: course.color,
                      size: AppConstants.iconSizeLarge,
                    ),
                  ),
                  
                  const SizedBox(width: AppConstants.spacingMedium),
                  
                  // Course info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          course.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        
                        const SizedBox(height: AppConstants.spacingSmall / 2),
                        
                        Text(
                          course.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(180),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        const SizedBox(height: AppConstants.spacingMedium),
                        
                        // Course metadata
                        Row(
                          children: [
                            // Difficulty
                            _buildMetadataChip(
                              label: course.difficultyText,
                              color: _getDifficultyColor(course.difficulty),
                              icon: Icons.signal_cellular_alt,
                            ),
                            
                            const SizedBox(width: AppConstants.spacingSmall),
                            
                            // Duration
                            _buildMetadataChip(
                              label: course.formattedDuration,
                              color: theme.colorScheme.primary,
                              icon: Icons.access_time,
                            ),
                            
                            if (course.isPremium) ...[
                              const SizedBox(width: AppConstants.spacingSmall),
                              
                              // Premium badge
                              _buildMetadataChip(
                                label: 'Premium',
                                color: Colors.amber,
                                icon: Icons.star,
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              // Progress bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Progress',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${(completionPercentage * 100).toInt()}%',
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.spacingSmall / 2),
                  
                  LinearProgressIndicator(
                    value: completionPercentage,
                    backgroundColor: theme.colorScheme.primary.withAlpha(30),
                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// Builds a metadata chip
  Widget _buildMetadataChip({
    required String label,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall / 2,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: AppConstants.iconSizeSmall,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Builds the empty state
  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off,
              size: AppConstants.iconSizeLarge,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          Text(
            'No courses found',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingSmall),
          
          Text(
            'Try changing the filters',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(180),
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingLarge),
          
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedDifficulty = null;
                _showOnlyPremium = false;
              });
            },
            icon: const Icon(Icons.filter_alt_off),
            label: const Text('Clear Filters'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
                vertical: AppConstants.paddingMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Gets the difficulty color
  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return Colors.green;
      case DifficultyLevel.intermediate:
        return Colors.orange;
      case DifficultyLevel.advanced:
        return Colors.red;
    }
  }
}
