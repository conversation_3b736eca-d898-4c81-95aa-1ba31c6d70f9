import 'dart:math';

import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';
import 'package:learn_chart_patterns/providers/theme_provider.dart';
import 'package:learn_chart_patterns/widgets/candlestick_chart.dart';
import 'package:provider/provider.dart';

/// The basics screen of the app
class BasicsScreen extends StatefulWidget {
  /// Creates a new basics screen
  const BasicsScreen({super.key});

  @override
  State<BasicsScreen> createState() => _BasicsScreenState();
}

class _BasicsScreenState extends State<BasicsScreen> {
  /// The current section index
  int _currentSection = 0;

  /// The page controller for the sections
  final PageController _pageController = PageController();

  /// The sections of the basics module
  final List<_BasicsSection> _sections = [
    _BasicsSection(
      title: 'Introduction to Candlesticks',
      content: 'Candlestick charts originated in Japan in the 18th century and were used by rice traders to track market prices. Today, they are one of the most popular chart types used in technical analysis.\n\nCandlestick charts display the open, high, low, and close prices for a specific time period, providing a comprehensive view of price action.',
      image: 'assets/images/candlestick_intro.png',
      generateChart: (random) {
        final DateTime now = DateTime.now();
        final List<CandlestickData> candlesticks = [];

        for (int i = 0; i < 20; i++) {
          final double open = 100 + random.nextDouble() * 20;
          final double close = 100 + random.nextDouble() * 20;
          final double high = max(open, close) + random.nextDouble() * 5;
          final double low = min(open, close) - random.nextDouble() * 5;

          candlesticks.add(CandlestickData(
            date: now.subtract(Duration(days: 20 - i)),
            open: open,
            high: high,
            low: low,
            close: close,
          ));
        }

        return candlesticks;
      },
    ),
    _BasicsSection(
      title: 'Anatomy of a Candlestick',
      content: 'A candlestick consists of a body and wicks (shadows):\n\n• Body: Represents the range between the open and close prices.\n• Upper Wick: Represents the high price.\n• Lower Wick: Represents the low price.\n\nThe color of the body indicates whether the price closed higher or lower than it opened:\n\n• Green/White: Bullish (close > open)\n• Red/Black: Bearish (close < open)',
      generateChart: (random) {
        final DateTime now = DateTime.now();
        final List<CandlestickData> candlesticks = [];

        // Bullish candle - larger and more pronounced
        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 3)),
          open: 100,
          high: 115, // Increased high for better visibility
          low: 90,   // Decreased low for better visibility
          close: 110, // Increased close for more pronounced bullish candle
        ));

        // Bearish candle - larger and more pronounced
        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 2)),
          open: 110,
          high: 115,
          low: 85,   // Decreased low for better visibility
          close: 90, // Decreased close for more pronounced bearish candle
        ));

        // Doji candle - with more pronounced wicks
        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 1)),
          open: 100,
          high: 110, // Increased high for better visibility
          low: 90,   // Decreased low for better visibility
          close: 100, // Equal to open for a perfect doji
        ));

        return candlesticks;
      },
      generateSupportResistance: (candlesticks) {
        // Add labels to identify the candle types
        return [
          SupportResistanceLevel(
            price: candlesticks[0].high + 5,
            type: LevelType.resistance,
            label: 'Bullish Candle',
            color: Colors.green,
          ),
          SupportResistanceLevel(
            price: candlesticks[1].high + 5,
            type: LevelType.resistance,
            label: 'Bearish Candle',
            color: Colors.red,
          ),
          SupportResistanceLevel(
            price: candlesticks[2].high + 5,
            type: LevelType.resistance,
            label: 'Doji Candle',
            color: Colors.blue,
          ),
        ];
      },
    ),
    _BasicsSection(
      title: 'Bullish vs Bearish Candles',
      content: 'Bullish Candles (Green/White):\n• Indicate buying pressure\n• Close price is higher than open price\n• Often signal potential upward movement\n\nBearish Candles (Red/Black):\n• Indicate selling pressure\n• Close price is lower than open price\n• Often signal potential downward movement\n\nThe length of the body and wicks can provide additional information about market sentiment and volatility.',
      generateChart: (random) {
        final DateTime now = DateTime.now();
        final List<CandlestickData> candlesticks = [];

        // Bullish trend with clear, educational examples
        double price = 100;
        for (int i = 0; i < 10; i++) {
          // Make the pattern more pronounced and educational
          double open, close, high, low;

          if (i % 3 == 0) {
            // Strong bullish candle with small wicks
            open = price;
            close = open + 5;
            high = close + 1;
            low = open - 1;
          } else if (i % 3 == 1) {
            // Bullish candle with longer upper wick (showing resistance)
            open = price;
            close = open + 3;
            high = close + 4;
            low = open - 1;
          } else {
            // Bullish candle with longer lower wick (showing support)
            open = price;
            close = open + 3;
            high = close + 1;
            low = open - 4;
          }

          candlesticks.add(CandlestickData(
            date: now.subtract(Duration(days: 20 - i)),
            open: open,
            high: high,
            low: low,
            close: close,
          ));

          price = close;
        }

        // Bearish trend with clear, educational examples
        for (int i = 10; i < 20; i++) {
          // Make the pattern more pronounced and educational
          double open, close, high, low;

          if (i % 3 == 0) {
            // Strong bearish candle with small wicks
            open = price;
            close = open - 5;
            high = open + 1;
            low = close - 1;
          } else if (i % 3 == 1) {
            // Bearish candle with longer upper wick (showing resistance)
            open = price;
            close = open - 3;
            high = open + 4;
            low = close - 1;
          } else {
            // Bearish candle with longer lower wick (showing support)
            open = price;
            close = open - 3;
            high = open + 1;
            low = close - 4;
          }

          candlesticks.add(CandlestickData(
            date: now.subtract(Duration(days: 20 - i)),
            open: open,
            high: high,
            low: low,
            close: close,
          ));

          price = close;
        }

        return candlesticks;
      },
      generateSupportResistance: (candlesticks) {
        return [
          SupportResistanceLevel(
            price: candlesticks[4].high + 3,
            type: LevelType.resistance,
            label: 'Bullish Trend',
            color: Colors.green,
          ),
          SupportResistanceLevel(
            price: candlesticks[14].high + 3,
            type: LevelType.resistance,
            label: 'Bearish Trend',
            color: Colors.red,
          ),
        ];
      },
    ),
    _BasicsSection(
      title: 'Common Candlestick Patterns',
      content: 'Single Candlestick Patterns:\n• Doji: Open and close are nearly equal, indicating indecision\n• Hammer: Small body with long lower wick, potential bullish reversal\n• Shooting Star: Small body with long upper wick, potential bearish reversal\n\nMultiple Candlestick Patterns:\n• Engulfing: A candle that completely engulfs the previous candle\n• Morning/Evening Star: Three-candle pattern indicating potential reversal\n• Harami: A small candle contained within the previous larger candle',
      generateChart: (random) {
        final DateTime now = DateTime.now();
        final List<CandlestickData> candlesticks = [];

        // Doji
        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 6)),
          open: 100,
          high: 105,
          low: 95,
          close: 100,
        ));

        // Hammer
        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 5)),
          open: 100,
          high: 102,
          low: 90,
          close: 101,
        ));

        // Shooting Star
        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 4)),
          open: 101,
          high: 110,
          low: 99,
          close: 100,
        ));

        // Bullish Engulfing
        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 3)),
          open: 100,
          high: 101,
          low: 97,
          close: 98,
        ));

        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 2)),
          open: 97,
          high: 103,
          low: 96,
          close: 102,
        ));

        // Morning Star
        candlesticks.add(CandlestickData(
          date: now.subtract(const Duration(days: 1)),
          open: 102,
          high: 105,
          low: 101,
          close: 104,
        ));

        return candlesticks;
      },
    ),
    _BasicsSection(
      title: 'Support and Resistance',
      content: 'Support and resistance are key concepts in technical analysis:\n\nSupport:\n• Price level where buying interest is strong enough to overcome selling pressure\n• Acts as a floor, preventing prices from falling further\n• Often formed by previous lows\n\nResistance:\n• Price level where selling interest is strong enough to overcome buying pressure\n• Acts as a ceiling, preventing prices from rising further\n• Often formed by previous highs\n\nWhen support or resistance levels are broken, they often switch roles (former support becomes resistance and vice versa).',
      generateChart: (random) {
        final DateTime now = DateTime.now();
        final List<CandlestickData> candlesticks = [];

        // Generate candlesticks with clear support and resistance
        double supportLevel = 95;
        double resistanceLevel = 105;

        for (int i = 0; i < 20; i++) {
          double open, close, high, low;

          if (i < 10) {
            // Price bouncing between support and resistance
            if (i % 2 == 0) {
              // Moving up from support
              open = supportLevel + random.nextDouble() * 2;
              close = resistanceLevel - random.nextDouble() * 2;
              high = close + random.nextDouble() * 1;
              low = open - random.nextDouble() * 1;
            } else {
              // Moving down from resistance
              open = resistanceLevel - random.nextDouble() * 2;
              close = supportLevel + random.nextDouble() * 2;
              high = open + random.nextDouble() * 1;
              low = close - random.nextDouble() * 1;
            }
          } else {
            // Breakout above resistance
            open = resistanceLevel + (i - 10) * 1.0 + random.nextDouble() * 2;
            close = open + 1 + random.nextDouble() * 2;
            high = close + random.nextDouble() * 1;
            low = open - random.nextDouble() * 1;
          }

          candlesticks.add(CandlestickData(
            date: now.subtract(Duration(days: 20 - i)),
            open: open,
            high: high,
            low: low,
            close: close,
          ));
        }

        return candlesticks;
      },
      generateSupportResistance: (candlesticks) {
        return [
          SupportResistanceLevel.support(
            price: 95,
            label: 'Support',
          ),
          SupportResistanceLevel.resistance(
            price: 105,
            label: 'Resistance',
          ),
        ];
      },
    ),
    _BasicsSection(
      title: 'Trend Analysis',
      content: 'Trends are the general direction in which prices are moving:\n\nUptrend:\n• Series of higher highs and higher lows\n• Indicates bullish sentiment\n• Buy on dips strategy often used\n\nDowntrend:\n• Series of lower highs and lower lows\n• Indicates bearish sentiment\n• Sell on rallies strategy often used\n\nSideways/Consolidation:\n• Price moves within a horizontal range\n• Indicates balance between buyers and sellers\n• Often precedes a significant move in either direction',
      generateChart: (random) {
        final DateTime now = DateTime.now();
        final List<CandlestickData> candlesticks = [];

        // Uptrend
        double price = 90;
        for (int i = 0; i < 7; i++) {
          final double open = price;
          final double close = open + (1 + random.nextDouble() * 2);
          final double high = close + random.nextDouble() * 1;
          final double low = open - random.nextDouble() * 1;

          candlesticks.add(CandlestickData(
            date: now.subtract(Duration(days: 20 - i)),
            open: open,
            high: high,
            low: low,
            close: close,
          ));

          price = close;
        }

        // Sideways
        for (int i = 7; i < 14; i++) {
          final double open = price + (random.nextDouble() * 2 - 1);
          final double close = open + (random.nextDouble() * 2 - 1);
          final double high = max(open, close) + random.nextDouble() * 1;
          final double low = min(open, close) - random.nextDouble() * 1;

          candlesticks.add(CandlestickData(
            date: now.subtract(Duration(days: 20 - i)),
            open: open,
            high: high,
            low: low,
            close: close,
          ));

          price = close;
        }

        // Downtrend
        for (int i = 14; i < 20; i++) {
          final double open = price;
          final double close = open - (1 + random.nextDouble() * 2);
          final double high = open + random.nextDouble() * 1;
          final double low = close - random.nextDouble() * 1;

          candlesticks.add(CandlestickData(
            date: now.subtract(Duration(days: 20 - i)),
            open: open,
            high: high,
            low: low,
            close: close,
          ));

          price = close;
        }

        return candlesticks;
      },
      generateSupportResistance: (candlesticks) {
        return [
          SupportResistanceLevel.trendLine(
            startDate: candlesticks[0].date,
            endDate: candlesticks[6].date,
            startPrice: candlesticks[0].low,
            endPrice: candlesticks[6].low,
            label: 'Uptrend',
            color: Colors.green,
          ),
          SupportResistanceLevel.trendLine(
            startDate: candlesticks[14].date,
            endDate: candlesticks[19].date,
            startPrice: candlesticks[14].high,
            endPrice: candlesticks[19].high,
            label: 'Downtrend',
            color: Colors.red,
          ),
        ];
      },
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = context.watch<ThemeProvider>();
    final chartColors = themeProvider.getChartColors(Theme.of(context).brightness);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Candlestick Basics'),
      ),
      body: Column(
        children: [
          // Progress indicator
          LinearProgressIndicator(
            value: (_currentSection + 1) / _sections.length,
            backgroundColor: Colors.grey.withAlpha(76), // 0.3 * 255 = 76
            color: Theme.of(context).colorScheme.primary,
          ),

          // Section content
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: _sections.length,
              onPageChanged: (index) {
                setState(() {
                  _currentSection = index;
                });
              },
              itemBuilder: (context, index) {
                final section = _sections[index];
                final random = Random(index);
                final candlesticks = section.generateChart(random);
                final supportResistanceLevels = section.generateSupportResistance?.call(candlesticks);

                return SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Section title
                      Text(
                        section.title,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(height: AppConstants.spacingLarge),

                      // Chart
                      Center(
                        child: CandlestickChart(
                          candlesticks: candlesticks,
                          supportResistanceLevels: supportResistanceLevels,
                          chartColors: chartColors,
                          showVolume: false,
                          showTooltip: true,
                        ),
                      ),

                      const SizedBox(height: AppConstants.spacingLarge),

                      // Content
                      Text(
                        section.content,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),

                      const SizedBox(height: AppConstants.spacingLarge * 2),
                    ],
                  ),
                );
              },
            ),
          ),

          // Navigation buttons
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Previous button
                ElevatedButton(
                  onPressed: _currentSection > 0
                      ? () {
                          _pageController.previousPage(
                            duration: AppConstants.defaultAnimationDuration,
                            curve: Curves.easeInOut,
                          );
                        }
                      : null,
                  child: const Text('Previous'),
                ),

                // Section indicator
                Text(
                  '${_currentSection + 1} / ${_sections.length}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),

                // Next button
                ElevatedButton(
                  onPressed: _currentSection < _sections.length - 1
                      ? () {
                          _pageController.nextPage(
                            duration: AppConstants.defaultAnimationDuration,
                            curve: Curves.easeInOut,
                          );
                        }
                      : null,
                  child: const Text('Next'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// A section of the basics module
class _BasicsSection {
  /// The title of the section
  final String title;

  /// The content of the section
  final String content;

  /// The image path for the section (optional)
  final String? image;

  /// Function to generate candlestick data for the section
  final List<CandlestickData> Function(Random random) generateChart;

  /// Function to generate support/resistance levels for the section (optional)
  final List<SupportResistanceLevel> Function(List<CandlestickData> candlesticks)? generateSupportResistance;

  /// Creates a new basics section
  _BasicsSection({
    required this.title,
    required this.content,
    this.image,
    required this.generateChart,
    this.generateSupportResistance,
  });
}
