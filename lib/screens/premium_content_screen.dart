import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/premium_content.dart';
import 'package:learn_chart_patterns/screens/premium_content_detail_screen.dart';
import 'package:learn_chart_patterns/screens/premium_screen.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:provider/provider.dart';

/// Screen that displays all premium content items
class PremiumContentScreen extends StatefulWidget {
  /// Creates a new premium content screen
  const PremiumContentScreen({super.key});

  @override
  State<PremiumContentScreen> createState() => _PremiumContentScreenState();
}

class _PremiumContentScreenState extends State<PremiumContentScreen> {
  /// List of premium content items
  final List<PremiumContent> _premiumContent = PremiumContent.getAll();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final purchaseService = context.watch<PurchaseService>();

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Premium Content',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Upgrade button
          if (!purchaseService.hasPremium)
            TextButton.icon(
              onPressed: () {
                HapticFeedback.selectionClick();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PremiumScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.star),
              label: const Text('Upgrade'),
              style: TextButton.styleFrom(
                foregroundColor: theme.colorScheme.primary,
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Premium status banner
          if (!purchaseService.hasPremium) _buildPremiumBanner(theme),

          // Content list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              itemCount: _premiumContent.length,
              itemBuilder: (context, index) {
                final content = _premiumContent[index];
                return _buildContentCard(
                  context: context,
                  content: content,
                  purchaseService: purchaseService,
                );
              },
            ),
          ),

          // Ad banner (only shown if not premium)
          if (!purchaseService.hasPremium) const AdBanner(isHomeBanner: true),
        ],
      ),
      // Floating action button for premium upgrade
      floatingActionButton: !purchaseService.hasPremium
          ? FloatingActionButton.extended(
              onPressed: () {
                HapticFeedback.mediumImpact();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PremiumScreen(),
                  ),
                );
              },
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.star),
              label: const Text('Upgrade to Premium'),
              elevation: 4,
            )
          : null,
    );
  }

  /// Builds the premium banner for non-premium users
  Widget _buildPremiumBanner(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      color: theme.colorScheme.primary.withAlpha(20),
      child: Row(
        children: [
          Icon(
            Icons.lock,
            color: theme.colorScheme.primary,
            size: AppConstants.iconSizeMedium,
          ),
          const SizedBox(width: AppConstants.spacingMedium),
          Expanded(
            child: Text(
              'Unlock all premium content with a premium subscription',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: AppConstants.spacingMedium),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.mediumImpact();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PremiumScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              elevation: 0,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
            ),
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }

  /// Builds a content card
  Widget _buildContentCard({
    required BuildContext context,
    required PremiumContent content,
    required PurchaseService purchaseService,
  }) {
    final theme = Theme.of(context);

    // Check if the content is locked
    final bool isLocked = _isContentLocked(content, purchaseService);

    return Card(
      elevation: 0,
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: InkWell(
        onTap: () {
          HapticFeedback.selectionClick();
          if (isLocked) {
            // Show upgrade dialog
            _showUpgradeDialog(context, content);
          } else {
            // Navigate to content detail
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PremiumContentDetailScreen(content: content),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              // Icon
              Container(
                padding: const EdgeInsets.all(AppConstants.paddingSmall),
                decoration: BoxDecoration(
                  color: content.color.withAlpha(20),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
                child: Icon(
                  content.icon,
                  color: content.color,
                  size: AppConstants.iconSizeMedium,
                ),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            content.title,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (isLocked)
                          Icon(
                            Icons.lock,
                            color: theme.colorScheme.primary,
                            size: AppConstants.iconSizeSmall,
                          ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.spacingSmall / 2),
                    Text(
                      content.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(180),
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacingSmall),
                    // Required purchase tag
                    if (content.requiresAdvancedPatterns || content.requiresStrategyGuides)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.paddingSmall,
                          vertical: AppConstants.paddingSmall / 2,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withAlpha(20),
                          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                        ),
                        child: Text(
                          content.requiresAdvancedPatterns
                              ? 'Advanced Patterns'
                              : 'Strategy Guides',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              // Arrow
              Icon(
                Icons.arrow_forward_ios,
                color: theme.colorScheme.onSurface.withAlpha(128),
                size: AppConstants.iconSizeSmall,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Checks if the content is locked
  bool _isContentLocked(PremiumContent content, PurchaseService purchaseService) {
    if (!purchaseService.hasPremium) {
      return true;
    }

    if (content.requiresAdvancedPatterns && !purchaseService.hasAdvancedPatterns) {
      return true;
    }

    if (content.requiresStrategyGuides && !purchaseService.hasStrategyGuides) {
      return true;
    }

    return false;
  }

  /// Shows the upgrade dialog
  void _showUpgradeDialog(BuildContext context, PremiumContent content) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: content.color.withValues(alpha: 40),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.lock,
                  color: content.color,
                ),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
              Expanded(
                child: Text(
                  'Unlock ${content.title}',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Content description
                Text(
                  content.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.spacingMedium),

                // Required subscription info
                Text(
                  'This premium content requires:',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: AppConstants.spacingSmall),

                // Premium subscription
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: AppConstants.iconSizeSmall,
                    ),
                    const SizedBox(width: AppConstants.spacingSmall),
                    Expanded(
                      child: Text(
                        'Premium Subscription',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),

                // Advanced patterns package if required
                if (content.requiresAdvancedPatterns) ...[
                  const SizedBox(height: AppConstants.spacingSmall),
                  Row(
                    children: [
                      Icon(
                        Icons.auto_graph,
                        color: Colors.blue,
                        size: AppConstants.iconSizeSmall,
                      ),
                      const SizedBox(width: AppConstants.spacingSmall),
                      Expanded(
                        child: Text(
                          'Advanced Patterns Package',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ],

                // Strategy guides package if required
                if (content.requiresStrategyGuides) ...[
                  const SizedBox(height: AppConstants.spacingSmall),
                  Row(
                    children: [
                      Icon(
                        Icons.menu_book,
                        color: Colors.green,
                        size: AppConstants.iconSizeSmall,
                      ),
                      const SizedBox(width: AppConstants.spacingSmall),
                      Expanded(
                        child: Text(
                          'Strategy Guides Package',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: AppConstants.spacingMedium),

                // Benefits
                Text(
                  'Upgrading gives you access to:',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: AppConstants.spacingSmall),

                // List of benefits
                ...[
                  'All premium educational content',
                  'Advanced chart pattern analysis',
                  'Professional trading strategies',
                  'Ad-free experience',
                  'Regular content updates',
                ].map((benefit) => Padding(
                  padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall / 2),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: theme.colorScheme.primary,
                        size: AppConstants.iconSizeSmall,
                      ),
                      const SizedBox(width: AppConstants.spacingSmall),
                      Expanded(
                        child: Text(benefit),
                      ),
                    ],
                  ),
                )),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                HapticFeedback.selectionClick();
                Navigator.pop(context);
              },
              child: Text(
                'Not Now',
                style: TextStyle(color: theme.colorScheme.primary),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                HapticFeedback.mediumImpact();
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PremiumScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                elevation: 2,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
              ),
              child: const Text('Upgrade Now'),
            ),
          ],
          actionsPadding: const EdgeInsets.fromLTRB(
            AppConstants.paddingMedium,
            0,
            AppConstants.paddingMedium,
            AppConstants.paddingMedium,
          ),
        );
      },
    );
  }
}
