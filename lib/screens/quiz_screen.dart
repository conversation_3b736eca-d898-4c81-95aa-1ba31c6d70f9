import 'dart:math';

import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/generators/pattern_generator_factory.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';
import 'package:learn_chart_patterns/providers/pattern_provider.dart';
import 'package:learn_chart_patterns/providers/progress_provider.dart';
import 'package:learn_chart_patterns/providers/theme_provider.dart';
import 'package:learn_chart_patterns/services/ad_service.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:learn_chart_patterns/widgets/candlestick_chart.dart';
import 'package:learn_chart_patterns/widgets/quiz_option.dart';
import 'package:provider/provider.dart';

/// The quiz screen of the app
class QuizScreen extends StatefulWidget {
  /// Creates a new quiz screen
  const QuizScreen({super.key});

  @override
  State<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends State<QuizScreen> {
  /// The current quiz question
  late _QuizQuestion _currentQuestion;

  /// The selected pattern ID
  String? _selectedPatternId;

  /// Whether to show the result
  bool _showResult = false;

  /// Whether to show support/resistance levels
  bool _showSupportResistance = false;

  /// The number of correct answers
  int _correctAnswers = 0;

  /// The total number of questions
  int _totalQuestions = 0;

  /// Random number generator
  final _random = Random();

  @override
  void initState() {
    super.initState();
    _generateQuestion();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = context.watch<ThemeProvider>();
    final chartColors = themeProvider.getChartColors(Theme.of(context).brightness);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quiz'),
        actions: [
          // Toggle support/resistance button
          IconButton(
            icon: Icon(
              _showSupportResistance
                  ? Icons.show_chart
                  : Icons.candlestick_chart,
            ),
            onPressed: () {
              setState(() {
                _showSupportResistance = !_showSupportResistance;
              });
            },
            tooltip: _showSupportResistance
                ? 'Hide Support/Resistance'
                : 'Show Support/Resistance',
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress indicator
          LinearProgressIndicator(
            value: _totalQuestions > 0 ? _correctAnswers / _totalQuestions : 0,
            backgroundColor: Colors.grey.withAlpha(75),
            color: Colors.green,
          ),

          // Quiz content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Question
                  Text(
                    'What pattern is shown in this chart?',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),

                  const SizedBox(height: AppConstants.spacingMedium),

                  // Score
                  Text(
                    'Score: $_correctAnswers / $_totalQuestions',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),

                  const SizedBox(height: AppConstants.spacingLarge),

                  // Chart
                  Center(
                    child: CandlestickChart(
                      candlesticks: _currentQuestion.candlesticks,
                      supportResistanceLevels: _showSupportResistance
                          ? _currentQuestion.supportResistanceLevels
                          : null,
                      chartColors: chartColors,
                      showVolume: true,
                      showTooltip: true,
                    ),
                  ),

                  const SizedBox(height: AppConstants.spacingLarge),

                  // Options
                  ..._currentQuestion.options.map((pattern) {
                    return QuizOption(
                      pattern: pattern,
                      isSelected: _selectedPatternId == pattern.id,
                      isCorrect: pattern.id == _currentQuestion.correctPattern.id,
                      showResult: _showResult,
                      onTap: () {
                        if (!_showResult) {
                          setState(() {
                            _selectedPatternId = pattern.id;
                          });
                        }
                      },
                    );
                  }).toList(),

                  const SizedBox(height: AppConstants.spacingLarge),

                  // Explanation
                  if (_showResult) ...[
                    Text(
                      'Explanation',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppConstants.spacingMedium),

                    Text(
                      _currentQuestion.correctPattern.description,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),

                    const SizedBox(height: AppConstants.spacingMedium),

                    Text(
                      'Identification Points:',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppConstants.spacingSmall),

                    ...(_currentQuestion.correctPattern.identificationPoints.map((point) {
                      return Padding(
                        padding: const EdgeInsets.only(
                          bottom: AppConstants.paddingSmall,
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('• '),
                            Expanded(
                              child: Text(
                                point,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ),
                          ],
                        ),
                      );
                    })),

                    const SizedBox(height: AppConstants.spacingMedium),

                    // Ad banner
                    const AdBanner(isHomeBanner: true),
                  ],
                ],
              ),
            ),
          ),

          // Bottom buttons
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Row(
              children: [
                // Submit button
                if (!_showResult)
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _selectedPatternId != null
                          ? _submitAnswer
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          vertical: AppConstants.paddingMedium,
                        ),
                      ),
                      child: const Text('Submit Answer'),
                    ),
                  )
                // Next question button
                else
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _nextQuestion,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          vertical: AppConstants.paddingMedium,
                        ),
                      ),
                      child: const Text('Next Question'),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Generates a new quiz question
  void _generateQuestion() {
    final patternProvider = context.read<PatternProvider>();
    final patterns = patternProvider.allPatterns;

    // Select a random pattern as the correct answer
    final correctPattern = patterns[_random.nextInt(patterns.length)];

    // Generate candlestick data for the pattern
    final candlesticks = PatternGeneratorFactory.createGenerator(
      patternId: correctPattern.id,
      seed: _random.nextInt(1000000),
    ).generateCandlesticks(length: 40);

    // Generate support/resistance levels for the pattern
    final supportResistanceLevels = PatternGeneratorFactory.createGenerator(
      patternId: correctPattern.id,
      seed: _random.nextInt(1000000),
    ).generateSupportResistanceLevels(candlesticks);

    // Select random patterns as options (including the correct one)
    final options = <ChartPattern>[];
    options.add(correctPattern);

    // Add random incorrect options
    while (options.length < 4 && options.length < patterns.length) {
      final randomPattern = patterns[_random.nextInt(patterns.length)];
      if (!options.contains(randomPattern)) {
        options.add(randomPattern);
      }
    }

    // Shuffle the options
    options.shuffle(_random);

    // Create the question
    _currentQuestion = _QuizQuestion(
      correctPattern: correctPattern,
      options: options,
      candlesticks: candlesticks,
      supportResistanceLevels: supportResistanceLevels,
    );

    // Reset state
    _selectedPatternId = null;
    _showResult = false;
  }

  /// Submits the answer
  void _submitAnswer() {
    final isCorrect = _selectedPatternId == _currentQuestion.correctPattern.id;

    // Record the result
    context.read<ProgressProvider>().recordQuizResult(
      _currentQuestion.correctPattern.id,
      isCorrect,
    );

    // Update stats
    setState(() {
      _showResult = true;
      _totalQuestions++;
      if (isCorrect) {
        _correctAnswers++;
      }
    });

    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isCorrect
              ? 'Correct! Well done!'
              : 'Incorrect. The correct answer is ${_currentQuestion.correctPattern.name}.',
        ),
        backgroundColor: isCorrect ? Colors.green : Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Moves to the next question
  void _nextQuestion() {
    setState(() {
      _generateQuestion();
    });
  }
}

/// A quiz question
class _QuizQuestion {
  /// The correct pattern
  final ChartPattern correctPattern;

  /// The options to choose from
  final List<ChartPattern> options;

  /// The candlestick data for the pattern
  final List<CandlestickData> candlesticks;

  /// The support/resistance levels for the pattern
  final List<SupportResistanceLevel> supportResistanceLevels;

  /// Creates a new quiz question
  _QuizQuestion({
    required this.correctPattern,
    required this.options,
    required this.candlesticks,
    required this.supportResistanceLevels,
  });
}
