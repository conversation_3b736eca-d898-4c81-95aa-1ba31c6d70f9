import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/generators/pattern_generator_factory.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';
import 'package:learn_chart_patterns/providers/progress_provider.dart';
import 'package:learn_chart_patterns/providers/theme_provider.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:learn_chart_patterns/widgets/candlestick_chart.dart';
import 'package:provider/provider.dart';

/// The pattern detail screen of the app
class PatternDetailScreen extends StatefulWidget {
  /// The chart pattern to display
  final ChartPattern pattern;

  /// Creates a new pattern detail screen
  const PatternDetailScreen({
    super.key,
    required this.pattern,
  });

  @override
  State<PatternDetailScreen> createState() => _PatternDetailScreenState();
}

class _PatternDetailScreenState extends State<PatternDetailScreen> {
  /// The candlestick data for the pattern
  late List<CandlestickData> _candlesticks;

  /// The support/resistance levels for the pattern
  late List<SupportResistanceLevel> _supportResistanceLevels;

  /// Whether the pattern is learned
  late bool _isLearned;

  /// Whether to show support/resistance levels
  bool _showSupportResistance = true;

  @override
  void initState() {
    super.initState();

    // Generate candlestick data
    _candlesticks = PatternGeneratorFactory.createGenerator(
      patternId: widget.pattern.id,
      seed: widget.pattern.id.hashCode,
    ).generateCandlesticks(length: 40);

    // Generate support/resistance levels
    _supportResistanceLevels = PatternGeneratorFactory.createGenerator(
      patternId: widget.pattern.id,
      seed: widget.pattern.id.hashCode,
    ).generateSupportResistanceLevels(_candlesticks);

    // Check if the pattern is learned
    _isLearned = context.read<ProgressProvider>().isPatternLearned(widget.pattern.id);
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = context.watch<ThemeProvider>();
    final chartColors = themeProvider.getChartColors(Theme.of(context).brightness);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          widget.pattern.name,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Toggle support/resistance button
          Container(
            margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                _showSupportResistance
                    ? Icons.show_chart
                    : Icons.candlestick_chart,
                color: theme.colorScheme.primary,
              ),
              onPressed: () {
                setState(() {
                  _showSupportResistance = !_showSupportResistance;
                });
              },
              tooltip: _showSupportResistance
                  ? 'Hide Support/Resistance'
                  : 'Show Support/Resistance',
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Pattern chart
            Card(
              elevation: 0,
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Pattern Chart',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppConstants.paddingSmall,
                                vertical: AppConstants.paddingSmall / 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getTrendColor().withAlpha(20),
                                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    _getTrendIcon(),
                                    size: AppConstants.iconSizeSmall,
                                    color: _getTrendColor(),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _getTrendText(),
                                    style: TextStyle(
                                      color: _getTrendColor(),
                                      fontSize: AppConstants.fontSizeSmall,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.spacingMedium),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        border: Border.all(
                          color: theme.colorScheme.primary.withAlpha(30),
                          width: 1,
                        ),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: CandlestickChart(
                        candlesticks: _candlesticks,
                        supportResistanceLevels: _showSupportResistance
                            ? _supportResistanceLevels
                            : null,
                        chartColors: chartColors,
                        showVolume: false, // Remove volume for cleaner pattern focus
                        showTooltip: true,
                        height: AppConstants.defaultChartHeight * 1.2,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppConstants.spacingLarge),

            // Pattern info
            Card(
              elevation: 0,
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pattern Information',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacingMedium),

                    // Pattern type
                    Row(
                      children: [
                        // Trend chip
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.paddingSmall,
                            vertical: AppConstants.paddingSmall / 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getTrendColor().withAlpha(20),
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                _getTrendIcon(),
                                size: AppConstants.iconSizeSmall,
                                color: _getTrendColor(),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                _getTrendText(),
                                style: TextStyle(
                                  color: _getTrendColor(),
                                  fontSize: AppConstants.fontSizeSmall,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: AppConstants.spacingMedium),

                        // Difficulty chip
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.paddingSmall,
                            vertical: AppConstants.paddingSmall / 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getDifficultyColor().withAlpha(20),
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.signal_cellular_alt,
                                size: AppConstants.iconSizeSmall,
                                color: _getDifficultyColor(),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                _getDifficultyText(),
                                style: TextStyle(
                                  color: _getDifficultyColor(),
                                  fontSize: AppConstants.fontSizeSmall,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.spacingMedium),

                    // Description
                    Text(
                      'Description',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacingSmall),
                    Text(
                      widget.pattern.description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(200),
                        height: 1.5, // Increased line height for better readability
                      ),
                    ),

                    const SizedBox(height: AppConstants.spacingMedium),

                    // Key characteristics
                    Text(
                      'Key Characteristics',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacingSmall),
                    Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withAlpha(15),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        border: Border.all(
                          color: theme.colorScheme.primary.withAlpha(30),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildKeyCharacteristic(
                            theme,
                            icon: _getTrendIcon(),
                            title: 'Pattern Type',
                            description: '${_getTrendText()} ${widget.pattern.trend == PatternTrend.bullish || widget.pattern.trend == PatternTrend.bearish ? "pattern" : "pattern"} typically forming in ${widget.pattern.trend == PatternTrend.bullish ? "downtrends" : "uptrends"}',
                            iconColor: _getTrendColor(),
                          ),
                          const SizedBox(height: AppConstants.spacingMedium),
                          _buildKeyCharacteristic(
                            theme,
                            icon: Icons.signal_cellular_alt,
                            title: 'Reliability',
                            description: 'Considered a ${_getDifficultyText().toLowerCase()} pattern to identify with ${widget.pattern.trend == PatternTrend.bullish ? "bullish" : "bearish"} implications when confirmed',
                            iconColor: _getDifficultyColor(),
                          ),
                          const SizedBox(height: AppConstants.spacingMedium),
                          _buildKeyCharacteristic(
                            theme,
                            icon: Icons.timeline,
                            title: 'Time Frame',
                            description: 'Can form on any time frame, but more reliable on higher time frames (4H, Daily, Weekly)',
                            iconColor: Colors.blue,
                          ),
                          const SizedBox(height: AppConstants.spacingMedium),
                          _buildKeyCharacteristic(
                            theme,
                            icon: Icons.show_chart,
                            title: 'Volume Profile',
                            description: 'Volume typically ${widget.pattern.trend == PatternTrend.bullish ? "increases during breakout" : "decreases during formation and increases on breakdown"}',
                            iconColor: Colors.purple,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppConstants.spacingMedium),

            // Identification Points
            Card(
              elevation: 0,
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.search,
                          color: theme.colorScheme.primary,
                          size: AppConstants.iconSizeMedium,
                        ),
                        const SizedBox(width: AppConstants.spacingSmall),
                        Text(
                          'How to Identify This Pattern',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.spacingSmall),
                    Text(
                      'Look for these key elements when spotting this pattern:',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(200),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacingMedium),
                    _buildNumberedList(widget.pattern.identificationPoints),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppConstants.spacingMedium),

            // Trading Strategies
            Card(
              elevation: 0,
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.trending_up,
                          color: widget.pattern.trend == PatternTrend.bullish ? Colors.green : Colors.red,
                          size: AppConstants.iconSizeMedium,
                        ),
                        const SizedBox(width: AppConstants.spacingSmall),
                        Text(
                          'How to Trade This Pattern',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.spacingSmall),
                    Text(
                      'Follow these steps to effectively trade this pattern:',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(200),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacingMedium),
                    _buildBulletList(widget.pattern.tradingStrategies),

                    const SizedBox(height: AppConstants.spacingMedium),

                    // Risk management tips
                    Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: Colors.amber.withAlpha(30),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        border: Border.all(
                          color: Colors.amber.withAlpha(100),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.warning_amber_rounded,
                                color: Colors.amber,
                                size: AppConstants.iconSizeMedium,
                              ),
                              const SizedBox(width: AppConstants.spacingSmall),
                              Text(
                                'Risk Management Tips',
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber.shade800,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppConstants.spacingSmall),
                          Text(
                            'Always use stop losses and proper position sizing. No pattern works 100% of the time, and false breakouts are common. Consider waiting for confirmation before entering a trade.',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withAlpha(200),
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppConstants.spacingLarge),

            // Mark as learned button
            Card(
              elevation: 0,
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (!_isLearned) ...[
                      Text(
                        'Ready to mark this pattern as learned?',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.spacingSmall),
                      Text(
                        'Track your progress by marking patterns as learned when you understand them.',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.spacingMedium),
                      ElevatedButton.icon(
                        onPressed: _markAsLearned,
                        icon: const Icon(Icons.check),
                        label: const Text('Mark as Learned'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.paddingLarge,
                            vertical: AppConstants.paddingMedium,
                          ),
                        ),
                      ),
                    ] else ...[
                      const Icon(
                        Icons.check_circle,
                        color: Color(0xFF4CAF50),
                        size: AppConstants.iconSizeLarge,
                      ),
                      const SizedBox(height: AppConstants.spacingMedium),
                      Text(
                        'Pattern Learned!',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF4CAF50),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.spacingSmall),
                      Text(
                        'Great job! You\'ve marked this pattern as learned.',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppConstants.spacingLarge),

            // Ad banner
            const AdBanner(isHomeBanner: false),

            const SizedBox(height: AppConstants.spacingLarge),
          ],
        ),
      ),
    );
  }

  /// Builds a bullet list
  Widget _buildBulletList(List<String> items) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items.map((item) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '•',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: AppConstants.spacingSmall),
              Expanded(
                child: Text(
                  item,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(200),
                    height: 1.4, // Increased line height for better readability
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// Builds a key characteristic item
  Widget _buildKeyCharacteristic(
    ThemeData theme, {
    required IconData icon,
    required String title,
    required String description,
    required Color iconColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withAlpha(30),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: AppConstants.iconSizeMedium,
            color: iconColor,
          ),
        ),
        const SizedBox(width: AppConstants.spacingMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(200),
                  height: 1.4, // Increased line height for better readability
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds a numbered list
  Widget _buildNumberedList(List<String> items) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items.asMap().entries.map((entry) {
        final int index = entry.key;
        final String item = entry.value;

        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(220),
                        height: 1.4, // Increased line height for better readability
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }



  /// Gets the trend text
  String _getTrendText() {
    switch (widget.pattern.trend) {
      case PatternTrend.bullish:
        return 'Bullish';
      case PatternTrend.bearish:
        return 'Bearish';
      case PatternTrend.neutral:
        return 'Neutral';
      case PatternTrend.reversal:
        return 'Reversal';
      case PatternTrend.continuation:
        return 'Continuation';
    }
  }

  /// Gets the trend color
  Color _getTrendColor() {
    switch (widget.pattern.trend) {
      case PatternTrend.bullish:
        return const Color(0xFF4CAF50); // Green
      case PatternTrend.bearish:
        return const Color(0xFFFF6B6B); // Coral
      case PatternTrend.neutral:
        return const Color(0xFF4A6FFF); // Blue
      case PatternTrend.reversal:
        return const Color(0xFF9C27B0); // Purple
      case PatternTrend.continuation:
        return const Color(0xFFFF9800); // Orange
    }
  }

  /// Gets the trend icon
  IconData _getTrendIcon() {
    switch (widget.pattern.trend) {
      case PatternTrend.bullish:
        return Icons.trending_up;
      case PatternTrend.bearish:
        return Icons.trending_down;
      case PatternTrend.neutral:
        return Icons.trending_flat;
      case PatternTrend.reversal:
        return Icons.swap_vert;
      case PatternTrend.continuation:
        return Icons.arrow_forward;
    }
  }

  /// Gets the difficulty text
  String _getDifficultyText() {
    switch (widget.pattern.difficulty) {
      case PatternDifficulty.beginner:
        return 'Beginner';
      case PatternDifficulty.intermediate:
        return 'Intermediate';
      case PatternDifficulty.advanced:
        return 'Advanced';
      case null:
        // For premium patterns that might not have difficulty set
        return widget.pattern.complexity != null
            ? 'Complexity: ${widget.pattern.complexity}/5'
            : widget.pattern.category ?? 'Premium';
    }
  }

  /// Gets the difficulty color
  Color _getDifficultyColor() {
    switch (widget.pattern.difficulty) {
      case PatternDifficulty.beginner:
        return const Color(0xFF4CAF50); // Green
      case PatternDifficulty.intermediate:
        return const Color(0xFFFFA726); // Orange
      case PatternDifficulty.advanced:
        return const Color(0xFFFF6B6B); // Coral
      case null:
        // For premium patterns that might not have difficulty set
        return widget.pattern.color ?? const Color(0xFF9C27B0); // Purple default
    }
  }

  /// Marks the pattern as learned
  Future<void> _markAsLearned() async {
    await context.read<ProgressProvider>().markPatternAsLearned(widget.pattern.id);

    setState(() {
      _isLearned = true;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Pattern marked as learned!'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}
