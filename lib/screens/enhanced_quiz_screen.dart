import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/enhanced_quiz_system.dart';
import 'package:learn_chart_patterns/services/enhanced_quiz_generator.dart';
import 'package:learn_chart_patterns/widgets/candlestick_chart.dart';
import 'package:learn_chart_patterns/widgets/pattern_feedback_system.dart';
import 'package:learn_chart_patterns/providers/theme_provider.dart';
import 'package:provider/provider.dart';

/// Enhanced quiz screen with adaptive questioning
class EnhancedQuizScreen extends StatefulWidget {
  /// Initial skill level
  final SkillLevel skillLevel;
  
  /// Quiz configuration
  final QuizSessionConfig? config;

  /// Creates a new enhanced quiz screen
  const EnhancedQuizScreen({
    super.key,
    this.skillLevel = SkillLevel.beginner,
    this.config,
  });

  @override
  State<EnhancedQuizScreen> createState() => _EnhancedQuizScreenState();
}

class _EnhancedQuizScreenState extends State<EnhancedQuizScreen>
    with TickerProviderStateMixin {
  /// Quiz generator
  late EnhancedQuizGenerator _quizGenerator;
  
  /// Current quiz session
  late AdaptiveQuizSession _session;
  
  /// Selected answer IDs
  final Set<String> _selectedAnswerIds = {};
  
  /// Whether the current question is answered
  bool _isAnswered = false;
  
  /// Whether to show explanations
  bool _showExplanation = false;
  
  /// Animation controller for feedback
  late AnimationController _feedbackController;
  
  /// Timer for question time limit
  int _timeRemaining = 0;
  
  /// Whether the session is completed
  bool _isSessionCompleted = false;

  @override
  void initState() {
    super.initState();
    
    _quizGenerator = EnhancedQuizGenerator();
    _session = _quizGenerator.generateAdaptiveSession(
      skillLevel: widget.skillLevel,
      config: widget.config,
    );
    
    _feedbackController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _initializeQuestion();
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    final chartColors = themeProvider.getChartColors(theme.brightness);
    
    if (_isSessionCompleted) {
      return _buildSessionComplete(theme);
    }
    
    final currentQuestion = _session.currentQuestion;
    if (currentQuestion == null) {
      return _buildLoadingScreen(theme);
    }
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Adaptive Quiz',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Progress indicator
          Center(
            child: Padding(
              padding: const EdgeInsets.only(right: AppConstants.paddingMedium),
              child: Text(
                '${_session.currentQuestionIndex + 1}/${_session.questions.length}',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress bar
          LinearProgressIndicator(
            value: (_session.currentQuestionIndex + 1) / _session.questions.length,
            backgroundColor: theme.colorScheme.outline.withAlpha(50),
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
          
          // Question content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Question header
                  _buildQuestionHeader(theme, currentQuestion),
                  
                  const SizedBox(height: AppConstants.spacingLarge),
                  
                  // Chart (if applicable)
                  if (currentQuestion.chartData != null)
                    _buildChartSection(theme, currentQuestion, chartColors),
                  
                  const SizedBox(height: AppConstants.spacingLarge),
                  
                  // Question text
                  Text(
                    currentQuestion.questionText,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.spacingLarge),
                  
                  // Answer options
                  ...currentQuestion.options.map((option) => 
                    _buildAnswerOption(theme, option, currentQuestion)
                  ),
                  
                  const SizedBox(height: AppConstants.spacingLarge),
                  
                  // Explanation (if shown)
                  if (_showExplanation)
                    _buildExplanationSection(theme, currentQuestion),
                ],
              ),
            ),
          ),
          
          // Bottom action bar
          _buildActionBar(theme),
        ],
      ),
    );
  }

  /// Builds question header with metadata
  Widget _buildQuestionHeader(ThemeData theme, EnhancedQuizQuestion question) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          children: [
            // Question type icon
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withAlpha(30),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              ),
              child: Icon(
                _getQuestionTypeIcon(question.type),
                color: theme.colorScheme.primary,
              ),
            ),
            
            const SizedBox(width: AppConstants.spacingMedium),
            
            // Question metadata
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getQuestionTypeText(question.type),
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      // Difficulty
                      _buildMetadataChip(
                        theme,
                        'Level ${question.difficulty}',
                        _getDifficultyColor(question.difficulty),
                      ),
                      
                      const SizedBox(width: AppConstants.spacingSmall),
                      
                      // Category
                      _buildMetadataChip(
                        theme,
                        _getCategoryText(question.category),
                        theme.colorScheme.secondary,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Time remaining (if applicable)
            if (_timeRemaining > 0)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingSmall,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: _timeRemaining <= 10 ? Colors.red.withAlpha(30) : Colors.blue.withAlpha(30),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                ),
                child: Text(
                  '${_timeRemaining}s',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _timeRemaining <= 10 ? Colors.red : Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Builds chart section
  Widget _buildChartSection(
    ThemeData theme,
    EnhancedQuizQuestion question,
    chartColors,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Chart Analysis',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.spacingMedium),
            Container(
              height: AppConstants.defaultChartHeight,
              decoration: BoxDecoration(
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(50),
                ),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              ),
              child: CandlestickChart(
                candlesticks: question.chartData!,
                supportResistanceLevels: question.supportResistanceLevels,
                chartColors: chartColors,
                showVolume: false,
                showTooltip: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds answer option
  Widget _buildAnswerOption(
    ThemeData theme,
    QuizOption option,
    EnhancedQuizQuestion question,
  ) {
    final isSelected = _selectedAnswerIds.contains(option.id);
    final isCorrect = option.isCorrect;
    final showResult = _isAnswered;
    
    Color? backgroundColor;
    Color? borderColor;
    
    if (showResult) {
      if (isCorrect) {
        backgroundColor = Colors.green.withAlpha(30);
        borderColor = Colors.green;
      } else if (isSelected && !isCorrect) {
        backgroundColor = Colors.red.withAlpha(30);
        borderColor = Colors.red;
      }
    } else if (isSelected) {
      backgroundColor = theme.colorScheme.primary.withAlpha(30);
      borderColor = theme.colorScheme.primary;
    }
    
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: AppConstants.spacingMedium),
      child: OutlinedButton(
        onPressed: _isAnswered ? null : () => _selectAnswer(option.id, question),
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          side: BorderSide(
            color: borderColor ?? theme.colorScheme.outline,
            width: borderColor != null ? 2 : 1,
          ),
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
        ),
        child: Row(
          children: [
            // Result icon
            if (showResult) ...[
              Icon(
                isCorrect ? Icons.check_circle : (isSelected ? Icons.cancel : Icons.radio_button_unchecked),
                color: isCorrect ? Colors.green : (isSelected ? Colors.red : theme.colorScheme.outline),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
            ],
            
            // Option text
            Expanded(
              child: Text(
                option.text,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: showResult 
                      ? (isCorrect ? Colors.green : (isSelected ? Colors.red : theme.colorScheme.onSurface))
                      : (isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface),
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds explanation section
  Widget _buildExplanationSection(ThemeData theme, EnhancedQuizQuestion question) {
    return PatternFeedbackSystem(
      pattern: null, // TODO: Get pattern from question if applicable
      isCorrect: _selectedAnswerIds.any((id) => 
        question.options.any((option) => option.id == id && option.isCorrect)
      ),
      userAnswer: _selectedAnswerIds.first,
      explanation: question.explanation.mainExplanation,
      marketContext: question.explanation.marketContext,
      learningTips: question.explanation.relatedConcepts,
    );
  }

  /// Builds action bar
  Widget _buildActionBar(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Hint button (if available)
          if (!_isAnswered && _session.currentQuestion?.explanation != null)
            OutlinedButton.icon(
              onPressed: _showHint,
              icon: const Icon(Icons.lightbulb_outline),
              label: const Text('Hint'),
            ),
          
          const Spacer(),
          
          // Submit/Next button
          ElevatedButton(
            onPressed: _isAnswered ? _nextQuestion : (_selectedAnswerIds.isNotEmpty ? _submitAnswer : null),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
                vertical: AppConstants.paddingMedium,
              ),
            ),
            child: Text(_isAnswered ? 'Next Question' : 'Submit Answer'),
          ),
        ],
      ),
    );
  }

  /// Builds loading screen
  Widget _buildLoadingScreen(ThemeData theme) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: AppConstants.spacingLarge),
            Text(
              'Preparing your adaptive quiz...',
              style: theme.textTheme.titleMedium,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds session complete screen
  Widget _buildSessionComplete(ThemeData theme) {
    final accuracy = _session.metrics.accuracy;
    final isPassed = accuracy >= _session.config.passingAccuracy;
    
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isPassed ? Icons.check_circle : Icons.cancel,
                size: 80,
                color: isPassed ? Colors.green : Colors.red,
              ),
              
              const SizedBox(height: AppConstants.spacingLarge),
              
              Text(
                isPassed ? 'Quiz Completed!' : 'Keep Learning!',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isPassed ? Colors.green : Colors.red,
                ),
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              Text(
                'Score: ${(accuracy * 100).round()}%',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              Text(
                '${_session.metrics.correctAnswers}/${_session.metrics.totalQuestions} correct answers',
                style: theme.textTheme.bodyLarge,
              ),
              
              const SizedBox(height: AppConstants.spacingLarge * 2),
              
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Continue Learning'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds metadata chip
  Widget _buildMetadataChip(ThemeData theme, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 6,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        label,
        style: theme.textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 10,
        ),
      ),
    );
  }

  /// Initializes the current question
  void _initializeQuestion() {
    final question = _session.currentQuestion;
    if (question != null && _session.config.timeLimitPerQuestion != null) {
      _timeRemaining = _session.config.timeLimitPerQuestion!;
      // TODO: Start timer
    }
  }

  /// Selects an answer
  void _selectAnswer(String answerId, EnhancedQuizQuestion question) {
    setState(() {
      if (question.type == QuestionType.multipleSelect) {
        if (_selectedAnswerIds.contains(answerId)) {
          _selectedAnswerIds.remove(answerId);
        } else {
          _selectedAnswerIds.add(answerId);
        }
      } else {
        _selectedAnswerIds.clear();
        _selectedAnswerIds.add(answerId);
      }
    });
  }

  /// Submits the current answer
  void _submitAnswer() {
    final question = _session.currentQuestion!;
    final isCorrect = _selectedAnswerIds.any((id) => 
      question.options.any((option) => option.id == id && option.isCorrect)
    );
    
    setState(() {
      _isAnswered = true;
      _showExplanation = true;
    });
    
    // Update session metrics
    // TODO: Implement session metrics update
    
    _feedbackController.forward();
  }

  /// Shows a hint
  void _showHint() {
    // TODO: Implement hint system
  }

  /// Moves to next question
  void _nextQuestion() {
    if (_session.currentQuestionIndex >= _session.questions.length - 1) {
      setState(() {
        _isSessionCompleted = true;
      });
    } else {
      setState(() {
        _session = AdaptiveQuizSession(
          id: _session.id,
          skillLevel: _session.skillLevel,
          questions: _session.questions,
          currentQuestionIndex: _session.currentQuestionIndex + 1,
          answers: _session.answers,
          startTime: _session.startTime,
          config: _session.config,
          metrics: _session.metrics,
        );
        
        _selectedAnswerIds.clear();
        _isAnswered = false;
        _showExplanation = false;
      });
      
      _feedbackController.reset();
      _initializeQuestion();
    }
  }

  /// Gets question type icon
  IconData _getQuestionTypeIcon(QuestionType type) {
    switch (type) {
      case QuestionType.chartIdentification:
        return Icons.candlestick_chart;
      case QuestionType.multipleChoice:
        return Icons.radio_button_checked;
      case QuestionType.multipleSelect:
        return Icons.check_box;
      case QuestionType.trueFalse:
        return Icons.help;
      case QuestionType.scenario:
        return Icons.business_center;
      default:
        return Icons.quiz;
    }
  }

  /// Gets question type text
  String _getQuestionTypeText(QuestionType type) {
    switch (type) {
      case QuestionType.chartIdentification:
        return 'Chart Analysis';
      case QuestionType.multipleChoice:
        return 'Multiple Choice';
      case QuestionType.multipleSelect:
        return 'Multiple Select';
      case QuestionType.trueFalse:
        return 'True/False';
      case QuestionType.scenario:
        return 'Scenario';
      default:
        return 'Question';
    }
  }

  /// Gets difficulty color
  Color _getDifficultyColor(int difficulty) {
    switch (difficulty) {
      case 1:
      case 2:
        return Colors.green;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Gets category text
  String _getCategoryText(QuizCategory category) {
    switch (category) {
      case QuizCategory.candlestickBasics:
        return 'Candlesticks';
      case QuizCategory.patternRecognition:
        return 'Patterns';
      case QuizCategory.technicalAnalysis:
        return 'Technical';
      case QuizCategory.marketPsychology:
        return 'Psychology';
      case QuizCategory.riskManagement:
        return 'Risk Mgmt';
      case QuizCategory.tradingStrategies:
        return 'Strategies';
      case QuizCategory.forexFundamentals:
        return 'Forex';
      case QuizCategory.cryptoBasics:
        return 'Crypto';
    }
  }
}
