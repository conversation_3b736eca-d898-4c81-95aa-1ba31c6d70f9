import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/services/ad_service.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:provider/provider.dart';

/// The premium screen of the app
class PremiumScreen extends StatefulWidget {
  /// Creates a new premium screen
  const PremiumScreen({super.key});

  @override
  State<PremiumScreen> createState() => _PremiumScreenState();
}

class _PremiumScreenState extends State<PremiumScreen> {
  /// The purchase service
  final PurchaseService _purchaseService = PurchaseService();

  /// The ad service
  final AdService _adService = AdService();

  /// Available products
  List<ProductDetails> _products = [];

  /// Whether purchases are being restored
  bool _isRestoring = false;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  /// Load available products
  void _loadProducts() {
    setState(() {
      _products = _purchaseService.getProducts();
    });
  }

  /// Buy a product
  Future<void> _buyProduct(String productId) async {
    try {
      await _purchaseService.buyProduct(productId);

      // If premium access is purchased, disable ads
      if (productId == 'premium_access' && _purchaseService.hasPremium) {
        _adService.disableAds();
      }

      // Refresh the UI
      setState(() {});
    } catch (e) {
      debugPrint('Error buying product: $e');

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete purchase: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// Restore purchases
  Future<void> _restorePurchases() async {
    setState(() {
      _isRestoring = true;
    });

    try {
      await _purchaseService.restorePurchases();

      // If premium access is restored, disable ads
      if (_purchaseService.hasPremium) {
        _adService.disableAds();
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Purchases restored successfully!'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error restoring purchases: $e');

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to restore purchases: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      setState(() {
        _isRestoring = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Upgrade to Premium',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Restore purchases button
          TextButton.icon(
            onPressed: _isRestoring ? null : _restorePurchases,
            icon: _isRestoring
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  )
                : const Icon(Icons.restore),
            label: const Text('Restore'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header card
            Card(
              elevation: 0,
              color: theme.colorScheme.primary.withAlpha(20),
              margin: const EdgeInsets.only(bottom: AppConstants.paddingLarge),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(AppConstants.paddingMedium),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.star,
                            color: theme.colorScheme.primary,
                            size: AppConstants.iconSizeLarge,
                          ),
                        ),
                        const SizedBox(width: AppConstants.spacingMedium),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Unlock Premium Features',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                              const SizedBox(height: AppConstants.spacingSmall),
                              Text(
                                'Take your trading education to the next level with premium content and features',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Purchase options title
            Padding(
              padding: const EdgeInsets.only(
                left: AppConstants.paddingMedium,
                bottom: AppConstants.paddingMedium,
              ),
              child: Text(
                'Choose Your Plan',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Premium access card
            _buildPremiumCard(
              title: 'Premium Access',
              description: 'Remove ads and unlock all premium features',
              icon: Icons.star,
              color: Colors.amber,
              isPurchased: _purchaseService.hasPremium,
              productId: 'premium_access',
              features: [
                'Ad-free experience',
                'Access to all premium content',
                'Regular updates and new content',
                'Priority support',
              ],
            ),

            // Advanced patterns card
            _buildPremiumCard(
              title: 'Advanced Pattern Pack',
              description: 'Unlock advanced chart patterns and detailed analysis',
              icon: Icons.trending_up,
              color: Colors.blue,
              isPurchased: _purchaseService.hasAdvancedPatterns,
              productId: 'advanced_patterns',
              features: [
                'Harmonic patterns',
                'Complex chart formations',
                'Advanced pattern recognition techniques',
                'Detailed pattern analysis guides',
              ],
            ),

            // Strategy guides card
            _buildPremiumCard(
              title: 'Strategy Guides',
              description: 'Expert trading strategies and detailed guides',
              icon: Icons.menu_book,
              color: Colors.green,
              isPurchased: _purchaseService.hasStrategyGuides,
              productId: 'strategy_guides',
              features: [
                'Professional trading strategies',
                'Risk management systems',
                'Market psychology mastery',
                'Institutional trading insights',
              ],
            ),

            const SizedBox(height: AppConstants.spacingLarge),

            // Benefits section
            Card(
              elevation: 0,
              color: Colors.white,
              margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Benefits of Premium',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacingMedium),
                    // Benefits list
                    _buildBenefitItem(
                      icon: Icons.block,
                      title: 'Ad-Free Experience',
                      description: 'Enjoy the app without any advertisements',
                    ),
                    _buildBenefitItem(
                      icon: Icons.auto_graph,
                      title: 'Advanced Patterns',
                      description: '15+ additional advanced chart patterns',
                    ),
                    _buildBenefitItem(
                      icon: Icons.book,
                      title: 'Expert Strategies',
                      description: 'Detailed trading strategies from professional traders',
                    ),
                    _buildBenefitItem(
                      icon: Icons.update,
                      title: 'Regular Updates',
                      description: 'Get access to new premium content as it\'s released',
                    ),
                  ],
                ),
              ),
            ),

            // Ad banner (only shown if not premium)
            if (!_purchaseService.hasPremium) ...[
              const SizedBox(height: AppConstants.spacingLarge),
              const AdBanner(isHomeBanner: true),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds a premium card
  Widget _buildPremiumCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required bool isPurchased,
    required String productId,
    List<String>? features,
  }) {
    final theme = Theme.of(context);

    // Find the product
    ProductDetails? product;
    try {
      product = _products.firstWhere(
        (product) => product.id == productId,
      );
    } catch (e) {
      // Product not found
      product = null;
    }

    return Card(
      elevation: 0,
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingSmall),
                  decoration: BoxDecoration(
                    color: color.withAlpha(20),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: AppConstants.iconSizeMedium,
                  ),
                ),
                const SizedBox(width: AppConstants.spacingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.spacingSmall / 2),
                      Text(
                        description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Features list
            if (features != null && features.isNotEmpty) ...[
              const SizedBox(height: AppConstants.spacingMedium),
              ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: color,
                      size: AppConstants.iconSizeSmall,
                    ),
                    const SizedBox(width: AppConstants.spacingSmall),
                    Expanded(
                      child: Text(
                        feature,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              )),
              const SizedBox(height: AppConstants.spacingSmall),
            ],

            const SizedBox(height: AppConstants.spacingMedium),
            if (isPurchased)
              // Already purchased
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  vertical: AppConstants.paddingMedium,
                ),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(20),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: AppConstants.iconSizeSmall,
                    ),
                    const SizedBox(width: AppConstants.spacingSmall),
                    Text(
                      'Purchased',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              )
            else if (product != null)
              // Buy button
              ElevatedButton(
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  _buyProduct(productId);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    vertical: AppConstants.paddingMedium,
                  ),
                  minimumSize: const Size(double.infinity, 0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                ),
                child: Text('Buy ${product.price}'),
              )
            else
              // Product not available
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  vertical: AppConstants.paddingMedium,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(20),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
                child: const Center(
                  child: Text('Not available'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Builds a benefit item
  Widget _buildBenefitItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingSmall),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.primary,
              size: AppConstants.iconSizeSmall,
            ),
          ),
          const SizedBox(width: AppConstants.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.spacingSmall / 2),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(180),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
