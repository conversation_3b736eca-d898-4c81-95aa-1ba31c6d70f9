import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/providers/notification_provider.dart';
import 'package:learn_chart_patterns/providers/progress_provider.dart';
import 'package:learn_chart_patterns/providers/theme_provider.dart';
import 'package:learn_chart_patterns/screens/premium_content_screen.dart';
import 'package:provider/provider.dart';

/// A widget that displays a theme mode option
class ThemeModeOption extends StatelessWidget {
  /// The theme mode
  final ThemeMode themeMode;

  /// Whether this option is selected
  final bool isSelected;

  /// Callback when the option is tapped
  final VoidCallback onTap;

  /// Creates a new theme mode option
  const ThemeModeOption({
    super.key,
    required this.themeMode,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Get the display name and color for this theme mode
    String name;
    Color primaryColor;
    Color backgroundColor;
    IconData icon;

    switch (themeMode) {
      case ThemeMode.light:
        name = 'Light';
        primaryColor = const Color(0xFF4A6FFF); // Blue from light theme
        backgroundColor = Colors.white;
        icon = Icons.light_mode;
        break;
      case ThemeMode.dark:
        name = 'Dark';
        primaryColor = const Color(0xFF64B5F6); // Light Blue from dark theme
        backgroundColor = const Color(0xFF121212); // Dark background
        icon = Icons.dark_mode;
        break;
      case ThemeMode.system:
        name = 'System';
        primaryColor = theme.colorScheme.primary;
        backgroundColor = theme.colorScheme.surface;
        icon = Icons.brightness_auto;
        break;
    }

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primary.withValues(alpha: 40) : Colors.transparent,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.dividerColor,
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Theme preview
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                border: Border.all(
                  color: theme.dividerColor,
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                color: primaryColor,
                size: 30,
              ),
            ),

            const SizedBox(height: AppConstants.spacingSmall),

            // Theme name
            Text(
              name,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// A widget that displays an animated settings switch
class AnimatedSettingsSwitch extends StatelessWidget {
  /// The title of the switch
  final String title;

  /// The subtitle of the switch
  final String subtitle;

  /// The icon to display
  final IconData icon;

  /// Whether the switch is enabled
  final bool value;

  /// Callback when the switch changes
  final Function(bool) onChanged;

  /// Creates a new animated settings switch
  const AnimatedSettingsSwitch({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall / 2,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          onTap: () {
            HapticFeedback.selectionClick();
            onChanged(!value);
          },
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: value
                        ? theme.colorScheme.primary.withValues(alpha: 40)
                        : theme.colorScheme.surfaceContainerHighest,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: value ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                  ),
                ),

                const SizedBox(width: AppConstants.spacingMedium),

                // Text
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: AppConstants.spacingSmall / 2),
                      Text(
                        subtitle,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),

                // Switch
                Switch(
                  value: value,
                  onChanged: (newValue) {
                    HapticFeedback.selectionClick();
                    onChanged(newValue);
                  },
                  activeColor: theme.colorScheme.primary,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// A widget that displays a time picker tile
class TimePickerTile extends StatelessWidget {
  /// The current hour
  final int hour;

  /// The current minute
  final int minute;

  /// Callback when the time changes
  final Function(int hour, int minute) onTimeChanged;

  /// Creates a new time picker tile
  const TimePickerTile({
    super.key,
    required this.hour,
    required this.minute,
    required this.onTimeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Format the time
    final formattedHour = hour % 12 == 0 ? 12 : hour % 12;
    final period = hour < 12 ? 'AM' : 'PM';
    final formattedTime = '$formattedHour:${minute.toString().padLeft(2, '0')} $period';

    return ListTile(
      leading: const Icon(Icons.access_time),
      title: const Text('Reminder Time'),
      subtitle: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingSmall,
              vertical: AppConstants.paddingSmall / 2,
            ),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 40),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
            ),
            child: Text(
              formattedTime,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
      onTap: () async {
        HapticFeedback.selectionClick();

        final TimeOfDay? selectedTime = await showTimePicker(
          context: context,
          initialTime: TimeOfDay(
            hour: hour,
            minute: minute,
          ),
          builder: (context, child) {
            return Theme(
              data: theme.copyWith(
                timePickerTheme: TimePickerThemeData(
                  backgroundColor: theme.colorScheme.surface,
                  hourMinuteShape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  dayPeriodShape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  dayPeriodColor: theme.colorScheme.primary.withValues(alpha: 40),
                  dayPeriodTextColor: theme.colorScheme.primary,
                  hourMinuteColor: theme.colorScheme.primary.withValues(alpha: 40),
                  hourMinuteTextColor: theme.colorScheme.primary,
                ),
              ),
              child: child!,
            );
          },
        );

        if (selectedTime != null) {
          onTimeChanged(selectedTime.hour, selectedTime.minute);
        }
      },
    );
  }
}

/// A widget that displays a theme selector
class ThemeSelector extends StatelessWidget {
  /// The current theme mode
  final ThemeMode currentThemeMode;

  /// Callback when the theme mode changes
  final ValueChanged<ThemeMode> onThemeModeChanged;

  /// Creates a new theme selector
  const ThemeSelector({
    super.key,
    required this.currentThemeMode,
    required this.onThemeModeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Light theme
          ThemeModeOption(
            themeMode: ThemeMode.light,
            isSelected: currentThemeMode == ThemeMode.light,
            onTap: () {
              HapticFeedback.selectionClick();
              onThemeModeChanged(ThemeMode.light);
            },
          ),

          // System theme
          ThemeModeOption(
            themeMode: ThemeMode.system,
            isSelected: currentThemeMode == ThemeMode.system,
            onTap: () {
              HapticFeedback.selectionClick();
              onThemeModeChanged(ThemeMode.system);
            },
          ),

          // Dark theme
          ThemeModeOption(
            themeMode: ThemeMode.dark,
            isSelected: currentThemeMode == ThemeMode.dark,
            onTap: () {
              HapticFeedback.selectionClick();
              onThemeModeChanged(ThemeMode.dark);
            },
          ),
        ],
      ),
    );
  }
}

/// A card that displays a settings section
class SettingsSectionCard extends StatelessWidget {
  /// The title of the section
  final String title;

  /// The icon to display
  final IconData icon;

  /// The children to display in the section
  final List<Widget> children;

  /// The color of the icon
  final Color? iconColor;

  /// Creates a new settings section card
  const SettingsSectionCard({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(
        left: AppConstants.paddingMedium,
        right: AppConstants.paddingMedium,
        bottom: AppConstants.paddingMedium,
      ),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Row(
                children: [
                  Icon(
                    icon,
                    color: iconColor ?? theme.colorScheme.primary,
                    size: AppConstants.iconSizeMedium,
                  ),
                  const SizedBox(width: AppConstants.spacingMedium),
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),

            // Divider
            Divider(height: 1, thickness: 1, color: theme.dividerColor.withValues(alpha: 77)),

            // Content
            ...children,
          ],
        ),
      ),
    );
  }
}

/// The settings screen of the app
class SettingsScreen extends StatefulWidget {
  /// Creates a new settings screen
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = context.watch<ThemeProvider>();
    final notificationProvider = context.watch<NotificationProvider>();
    // We use progressProvider via context.read() in the reset dialog

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        elevation: 0,
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return ListView(
            padding: const EdgeInsets.only(top: AppConstants.paddingMedium),
            children: [
              // Appearance section
              Opacity(
                opacity: _animationController.value,
                child: SettingsSectionCard(
                  title: 'Appearance',
                  icon: Icons.palette,
                  children: [
                    // Theme selector
                    ThemeSelector(
                      currentThemeMode: themeProvider.themeMode,
                      onThemeModeChanged: (ThemeMode newMode) {
                        themeProvider.setThemeMode(newMode);
                      },
                    ),
                  ],
                ),
              ),

              // Notifications section
              Opacity(
                opacity: _animationController.value,
                child: SettingsSectionCard(
                  title: 'Notifications',
                  icon: Icons.notifications,
                  children: [
                    // Enable notifications
                    AnimatedSettingsSwitch(
                      title: 'Daily Reminder',
                      subtitle: 'Receive a daily reminder to practice',
                      icon: Icons.notifications_active,
                      value: notificationProvider.notificationsEnabled,
                      onChanged: (bool value) async {
                        if (value) {
                          // Request permission if enabling
                          final granted = await notificationProvider.requestPermissions();
                          if (granted) {
                            await notificationProvider.setNotificationsEnabled(true);
                            setState(() {}); // Refresh UI to show time picker
                          } else {
                            // Show error message on next frame
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Notification permission denied'),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              }
                            });
                          }
                        } else {
                          await notificationProvider.setNotificationsEnabled(false);
                          setState(() {}); // Refresh UI to hide time picker
                        }
                      },
                    ),

                    // Notification time
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: notificationProvider.notificationsEnabled
                          ? TimePickerTile(
                              hour: notificationProvider.notificationHour,
                              minute: notificationProvider.notificationMinute,
                              onTimeChanged: (hour, minute) async {
                                await notificationProvider.setNotificationTime(hour, minute);
                              },
                            )
                          : const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),

              // Data section
              Opacity(
                opacity: _animationController.value,
                child: SettingsSectionCard(
                  title: 'Data',
                  icon: Icons.storage,
                  children: [
                    // Reset progress
                    ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 40),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.delete_forever,
                          color: Colors.red,
                        ),
                      ),
                      title: const Text('Reset Progress'),
                      subtitle: const Text('Delete all progress data'),
                      onTap: () {
                        HapticFeedback.mediumImpact();
                        _showResetConfirmationDialog();
                      },
                    ),
                  ],
                ),
              ),

              // Premium section
              Opacity(
                opacity: _animationController.value,
                child: SettingsSectionCard(
                  title: 'Premium',
                  icon: Icons.star,
                  iconColor: Colors.amber,
                  children: [
                    // Premium content
                    ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.amber.withValues(alpha: 40),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.workspace_premium,
                          color: Colors.amber,
                        ),
                      ),
                      title: const Text('Premium Content'),
                      subtitle: const Text('Unlock advanced patterns and features'),
                      onTap: () {
                        HapticFeedback.selectionClick();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const PremiumContentScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),

              // About section
              Opacity(
                opacity: _animationController.value,
                child: SettingsSectionCard(
                  title: 'About',
                  icon: Icons.info,
                  children: [
                    // App version
                    ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withValues(alpha: 40),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.info,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      title: const Text('Version'),
                      subtitle: const Text(AppConstants.appVersion),
                    ),

                    // App info
                    ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withValues(alpha: 40),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.help,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      title: const Text('About This App'),
                      subtitle: const Text('Learn more about Chart Patterns Trainer'),
                      onTap: () {
                        HapticFeedback.selectionClick();
                        _showAboutDialog();
                      },
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }



  /// Shows the reset confirmation dialog
  void _showResetConfirmationDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 40),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.red,
                ),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
              const Text('Reset Progress'),
            ],
          ),
          content: const Text(
            'Are you sure you want to reset all progress? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                HapticFeedback.selectionClick();
                Navigator.pop(dialogContext);
              },
              child: Text(
                'Cancel',
                style: TextStyle(color: theme.colorScheme.primary),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                HapticFeedback.mediumImpact();

                // Get navigator before async operation
                final navigator = Navigator.of(dialogContext);

                // Reset progress
                await context.read<ProgressProvider>().resetProgress();

                // Close dialog
                navigator.pop();

                // Show success message on next frame
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text('Progress reset successfully'),
                        behavior: SnackBarBehavior.floating,
                        backgroundColor: theme.colorScheme.primary,
                      ),
                    );
                  }
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Reset'),
            ),
          ],
        );
      },
    );
  }

  /// Shows the about dialog
  void _showAboutDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 40),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.info,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
              const Text('About Chart Patterns Trainer'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Chart Patterns Trainer is an educational app designed to help you learn and practice technical analysis chart patterns.',
              ),

              const SizedBox(height: AppConstants.spacingMedium),

              Text(
                'Features:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),

              const SizedBox(height: AppConstants.spacingSmall),

              // Features with icons
              _buildFeatureItem(theme, Icons.auto_graph, 'Learn common chart patterns'),
              _buildFeatureItem(theme, Icons.quiz, 'Practice with interactive quizzes'),
              _buildFeatureItem(theme, Icons.candlestick_chart, 'Study candlestick basics'),
              _buildFeatureItem(theme, Icons.trending_up, 'Track your progress'),
              _buildFeatureItem(theme, Icons.offline_bolt, 'Works completely offline'),

              const SizedBox(height: AppConstants.spacingMedium),

              Text(
                'Version: ${AppConstants.appVersion}',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                HapticFeedback.selectionClick();
                Navigator.pop(dialogContext);
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  /// Builds a feature item for the about dialog
  Widget _buildFeatureItem(ThemeData theme, IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacingSmall),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: AppConstants.spacingSmall),
          Expanded(
            child: Text(text),
          ),
        ],
      ),
    );
  }
}
