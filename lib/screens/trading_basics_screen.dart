import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/data/comprehensive_education_content.dart';
import 'package:learn_chart_patterns/models/comprehensive_trading_education.dart';
import 'package:learn_chart_patterns/widgets/course_card.dart';
import 'package:learn_chart_patterns/widgets/learning_path_widget.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';

/// The trading basics screen of the app
class TradingBasicsScreen extends StatefulWidget {
  /// Creates a new trading basics screen
  const TradingBasicsScreen({super.key});

  @override
  State<TradingBasicsScreen> createState() => _TradingBasicsScreenState();
}

class _TradingBasicsScreenState extends State<TradingBasicsScreen> with SingleTickerProviderStateMixin {
  /// Tab controller
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Trading Basics',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withAlpha(150),
          indicatorColor: theme.colorScheme.primary,
          tabs: const [
            Tab(text: 'Forex'),
            Tab(text: 'Crypto'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Forex content
          _buildForexContent(),

          // Crypto content
          _buildCryptoContent(),
        ],
      ),
    );
  }

  /// Builds the forex content
  Widget _buildForexContent() {
    final forexCourses = ComprehensiveEducationContent.getForexCourses();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Learning path header
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.currency_exchange,
                        color: Colors.blue,
                        size: AppConstants.iconSizeLarge,
                      ),
                      const SizedBox(width: AppConstants.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Forex Trading Education',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: AppConstants.spacingSmall),
                            Text(
                              'Master the world\'s largest financial market with our comprehensive forex education program.',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withAlpha(180),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppConstants.spacingLarge),

          // Course cards
          Text(
            'Available Courses',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: AppConstants.spacingMedium),

          ...forexCourses.map((course) => CourseCard(
            course: course,
            onTap: () {
              // TODO: Navigate to course detail
            },
          )),

          const SizedBox(height: AppConstants.spacingLarge),

          // Quick reference sections
          _buildQuickReferenceSection(),

          const SizedBox(height: AppConstants.spacingMedium),

          _buildSection(
            title: 'What is Forex Trading?',
            content: 'Forex (Foreign Exchange) trading is the buying and selling of currencies on the foreign exchange market. It\'s the largest and most liquid financial market in the world, with an average daily trading volume exceeding \$6 trillion.\n\nIn forex trading, currencies are always traded in pairs, such as EUR/USD or USD/JPY. The first currency in the pair is called the base currency, and the second is called the quote currency.',
            icon: Icons.currency_exchange,
          ),

          _buildSection(
            title: 'Major Currency Pairs',
            content: 'The forex market includes several major currency pairs that are the most traded:\n\n• EUR/USD: Euro/US Dollar\n• USD/JPY: US Dollar/Japanese Yen\n• GBP/USD: British Pound/US Dollar\n• USD/CHF: US Dollar/Swiss Franc\n• AUD/USD: Australian Dollar/US Dollar\n• USD/CAD: US Dollar/Canadian Dollar\n• NZD/USD: New Zealand Dollar/US Dollar',
            icon: Icons.attach_money,
          ),

          _buildSection(
            title: 'Forex Market Hours',
            content: 'The forex market operates 24 hours a day, five days a week, across major financial centers around the world. Trading begins each day in Sydney, then moves to Tokyo, London, and finally New York before starting over again.\n\nThe most active trading periods occur when two major markets overlap, such as London and New York (8:00 AM - 12:00 PM EST).',
            icon: Icons.access_time,
          ),

          _buildSection(
            title: 'Understanding Pips',
            content: 'A pip (percentage in point) is the smallest price movement in a currency pair. For most currency pairs, a pip is a movement in the fourth decimal place (0.0001).\n\nFor example, if EUR/USD moves from 1.1050 to 1.1051, that\'s a one pip movement. For currency pairs involving the Japanese yen, a pip is a movement in the second decimal place (0.01).',
            icon: Icons.trending_up,
          ),

          _buildSection(
            title: 'Leverage and Margin',
            content: 'Leverage allows traders to control a large position with a relatively small amount of capital. For example, with 100:1 leverage, you can control a \$100,000 position with just \$1,000.\n\nMargin is the amount of money required to open a leveraged position. It\'s essentially a good faith deposit that\'s held by your broker while a trade is open.\n\nWhile leverage can amplify profits, it also increases risk and can lead to significant losses.',
            icon: Icons.account_balance,
          ),

          _buildSection(
            title: 'Risk Management',
            content: 'Successful forex trading requires effective risk management:\n\n• Never risk more than 1-2% of your account on a single trade\n• Always use stop-loss orders to limit potential losses\n• Understand and manage leverage carefully\n• Diversify your trades across different currency pairs\n• Keep emotions in check and stick to your trading plan',
            icon: Icons.shield,
          ),

          _buildSection(
            title: 'Technical Analysis Basics',
            content: 'Technical analysis involves studying price charts and using indicators to forecast future price movements. Key concepts include:\n\n• Support and Resistance: Price levels where currencies tend to stop and reverse\n• Trend Lines: Lines drawn to connect higher lows (uptrend) or lower highs (downtrend)\n• Chart Patterns: Formations like head and shoulders, double tops/bottoms, and triangles\n• Moving Averages: Lines showing the average price over a specific period\n• RSI (Relative Strength Index): Measures overbought or oversold conditions\n• MACD (Moving Average Convergence Divergence): Shows momentum and potential trend changes\n\nTechnical analysis works because many traders act on the same signals, creating self-fulfilling price movements.',
            icon: Icons.analytics,
          ),

          _buildSection(
            title: 'Fundamental Analysis in Forex',
            content: 'Fundamental analysis examines economic indicators and geopolitical events to predict currency movements:\n\n• Interest Rates: Higher rates typically strengthen a currency\n• GDP (Gross Domestic Product): Strong economic growth supports currency value\n• Inflation: Moderate inflation is healthy, but high inflation weakens currency\n• Employment Data: Strong job markets strengthen currencies\n• Trade Balance: Export-dominant economies often have stronger currencies\n• Political Stability: Affects investor confidence in a country\'s currency\n\nKey economic releases to watch include central bank announcements, Non-Farm Payrolls (US), and Consumer Price Index reports.',
            icon: Icons.assessment,
          ),

          _buildSection(
            title: 'Common Forex Trading Strategies',
            content: 'Several strategies are suitable for beginners:\n\n• Trend Following: Identifying and trading in the direction of established trends\n• Range Trading: Buying at support and selling at resistance in sideways markets\n• Breakout Trading: Entering when price breaks through significant levels\n• News Trading: Taking positions based on economic announcements\n• Carry Trade: Profiting from interest rate differentials between currencies\n• Scalping: Making many small profits on short-term price movements\n\nStart with simple strategies and add complexity as your experience grows. Always backtest strategies before using real money.',
            icon: Icons.route,
          ),

          _buildSection(
            title: 'Understanding Forex Orders',
            content: 'Different order types help manage trades effectively:\n\n• Market Order: Executes immediately at the current market price\n• Limit Order: Sets a specific price to buy below or sell above current price\n• Stop Order: Triggers a market order when price reaches a specified level\n• Stop-Loss Order: Automatically closes a losing position at a predetermined level\n• Take-Profit Order: Automatically closes a winning position at a predetermined level\n• Trailing Stop: Moves with the market to lock in profits while letting winners run\n\nUsing the right order types is crucial for proper risk management and trade execution.',
            icon: Icons.list_alt,
          ),

          _buildSection(
            title: 'Psychology of Forex Trading',
            content: 'Trading psychology often determines success or failure:\n\n• Fear and Greed: The two primary emotions that lead to trading mistakes\n• FOMO (Fear Of Missing Out): Can lead to chasing trades and poor entries\n• Revenge Trading: Trying to recover losses with risky trades\n• Confirmation Bias: Seeking information that confirms existing beliefs\n• Discipline: Following your trading plan even when emotions run high\n• Patience: Waiting for high-probability setups rather than forcing trades\n\nKeeping a trading journal helps identify emotional patterns and improve decision-making.',
            icon: Icons.psychology,
          ),

          _buildSection(
            title: 'Getting Started with Demo Trading',
            content: 'Demo trading allows practice without financial risk:\n\n• Choose a reputable broker with a stable demo platform\n• Treat demo trading as seriously as real trading\n• Practice your strategy consistently for at least 1-3 months\n• Track all trades and analyze your performance\n• Set realistic goals and measure progress\n• Identify weaknesses in your approach before moving to live trading\n• Gradually transition to small live positions once consistently profitable\n\nRemember that demo trading lacks the emotional component of risking real money, so results may differ in live trading.',
            icon: Icons.school,
          ),

          // Ad banner
          const SizedBox(height: AppConstants.spacingLarge),
          const AdBanner(isHomeBanner: true),
        ],
      ),
    );
  }

  /// Builds the crypto content
  Widget _buildCryptoContent() {
    final cryptoCourses = ComprehensiveEducationContent.getCryptoCourses();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Learning path header
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.currency_bitcoin,
                        color: Colors.orange,
                        size: AppConstants.iconSizeLarge,
                      ),
                      const SizedBox(width: AppConstants.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Cryptocurrency Trading Education',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: AppConstants.spacingSmall),
                            Text(
                              'Learn about blockchain technology, DeFi, and cryptocurrency trading strategies.',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withAlpha(180),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppConstants.spacingLarge),

          // Course cards
          Text(
            'Available Courses',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: AppConstants.spacingMedium),

          ...cryptoCourses.map((course) => CourseCard(
            course: course,
            onTap: () {
              // TODO: Navigate to course detail
            },
          )),

          const SizedBox(height: AppConstants.spacingLarge),

          // Quick reference for crypto
          _buildCryptoQuickReference(),

          const SizedBox(height: AppConstants.spacingMedium),

          _buildSection(
            title: 'What is Cryptocurrency?',
            content: 'Cryptocurrency is a digital or virtual currency that uses cryptography for security and operates on a technology called blockchain. Unlike traditional currencies issued by governments (fiat currencies), cryptocurrencies are typically decentralized and not controlled by any central authority.\n\nBitcoin, created in 2009, was the first cryptocurrency. Since then, thousands of alternative cryptocurrencies (altcoins) have been created.',
            icon: Icons.currency_bitcoin,
          ),

          _buildSection(
            title: 'Understanding Blockchain',
            content: 'Blockchain is the underlying technology behind most cryptocurrencies. It\'s a distributed ledger that records all transactions across a network of computers.\n\nKey features of blockchain include:\n\n• Decentralization: No single entity controls the network\n• Transparency: All transactions are publicly viewable\n• Immutability: Once recorded, data cannot be altered\n• Security: Cryptographic techniques protect the integrity of the data',
            icon: Icons.link,
          ),

          _buildSection(
            title: 'Major Cryptocurrencies',
            content: 'While there are thousands of cryptocurrencies, some of the most significant include:\n\n• Bitcoin (BTC): The first and most valuable cryptocurrency\n• Ethereum (ETH): Features smart contracts and decentralized applications\n• Binance Coin (BNB): Native token of the Binance exchange\n• Solana (SOL): Known for high throughput and low transaction costs\n• Cardano (ADA): Focuses on sustainability and scalability\n• XRP: Designed for cross-border payments',
            icon: Icons.monetization_on,
          ),

          _buildSection(
            title: 'Crypto Trading Basics',
            content: 'Cryptocurrency trading involves buying and selling digital assets on exchanges. Key concepts include:\n\n• Market Orders: Buy or sell immediately at the current market price\n• Limit Orders: Set a specific price at which to buy or sell\n• Stop Orders: Automatically execute when a certain price is reached\n• Trading Pairs: Cryptocurrencies are traded against other cryptocurrencies (BTC/ETH) or fiat currencies (BTC/USD)\n• Liquidity: The ease with which an asset can be bought or sold without affecting its price',
            icon: Icons.candlestick_chart,
          ),

          _buildSection(
            title: 'Crypto Wallets',
            content: 'Cryptocurrency wallets store the private keys needed to access and manage your digital assets. Types of wallets include:\n\n• Hardware Wallets: Physical devices that store keys offline (most secure)\n• Software Wallets: Desktop or mobile applications\n• Web Wallets: Browser-based wallets (convenient but less secure)\n• Paper Wallets: Physical documents containing keys\n\nRemember: "Not your keys, not your coins." Keeping crypto on exchanges means you don\'t fully control your assets.',
            icon: Icons.account_balance_wallet,
          ),

          _buildSection(
            title: 'Risk Management in Crypto',
            content: 'Cryptocurrency markets are highly volatile, making risk management essential:\n\n• Only invest what you can afford to lose\n• Diversify your portfolio across different assets\n• Use stop-loss orders to limit potential losses\n• Research thoroughly before investing (DYOR)\n• Be aware of market cycles and avoid FOMO (Fear Of Missing Out)\n• Consider dollar-cost averaging instead of trying to time the market',
            icon: Icons.warning,
          ),

          _buildSection(
            title: 'DeFi (Decentralized Finance)',
            content: 'DeFi refers to financial services built on blockchain technology that operate without centralized intermediaries like banks:\n\n• Lending Platforms: Earn interest by lending crypto or borrow against your holdings (Aave, Compound)\n• Decentralized Exchanges (DEXs): Trade directly from your wallet without a central authority (Uniswap, SushiSwap)\n• Yield Farming: Provide liquidity to protocols to earn rewards and fees\n• Staking: Lock up crypto to support network operations and earn rewards\n• Synthetic Assets: Digital representations of real-world assets like stocks or commodities\n• Insurance Protocols: Protection against smart contract failures or hacks\n\nDeFi offers greater accessibility and transparency but comes with risks including smart contract vulnerabilities and impermanent loss.',
            icon: Icons.account_balance,
          ),

          _buildSection(
            title: 'NFTs (Non-Fungible Tokens)',
            content: 'NFTs are unique digital assets that represent ownership of specific items:\n\n• Digital Art: One-of-a-kind artwork with verifiable ownership and provenance\n• Collectibles: Digital trading cards, avatars, and other collectible items\n• Gaming Assets: In-game items that can be owned and traded outside the game\n• Virtual Real Estate: Land and property in virtual worlds and metaverses\n• Music and Media: Ownership rights to songs, videos, and other media\n• Event Tickets: Digital tickets with benefits like exclusive access\n\nNFTs use blockchain to establish authenticity and ownership, creating scarcity for digital items that could otherwise be infinitely copied.',
            icon: Icons.art_track,
          ),

          _buildSection(
            title: 'Crypto Market Analysis',
            content: 'Analyzing crypto markets requires both technical and fundamental approaches:\n\nTechnical Analysis:\n• Chart patterns work similarly to traditional markets\n• Volume is particularly important in crypto markets\n• On-chain metrics provide unique insights (e.g., active addresses, transaction volume)\n\nFundamental Analysis:\n• Team and Developer Activity: Active GitHub repositories indicate ongoing development\n• Tokenomics: Supply distribution, inflation rate, and token utility\n• Adoption Metrics: Number of users, transactions, and applications built on the platform\n• Community Strength: Social media presence and community engagement\n• Partnerships and Integrations: Collaborations with established companies or projects\n\nThe 24/7 nature of crypto markets and influence of retail investors can create more volatility than traditional markets.',
            icon: Icons.analytics,
          ),

          _buildSection(
            title: 'Crypto Trading Platforms',
            content: 'Different platforms serve various trading needs:\n\n• Centralized Exchanges (CEX): User-friendly with high liquidity (Coinbase, Binance)\n  - Pros: Easy to use, high liquidity, customer support\n  - Cons: Require KYC, control your keys, potential security risks\n\n• Decentralized Exchanges (DEX): Trade directly from your wallet (Uniswap, dYdX)\n  - Pros: No KYC, self-custody, privacy\n  - Cons: Higher fees, lower liquidity for some assets, more complex\n\n• Peer-to-Peer Platforms: Direct trading between users (LocalBitcoins, Paxful)\n  - Pros: Various payment methods, no central authority\n  - Cons: Higher prices, potential for scams\n\nWhen choosing a platform, consider factors like security history, fees, available trading pairs, user interface, and regulatory compliance.',
            icon: Icons.compare_arrows,
          ),

          _buildSection(
            title: 'Security Best Practices',
            content: 'Protecting your crypto assets is essential:\n\n• Use Hardware Wallets: Store significant amounts in cold storage devices (Ledger, Trezor)\n• Enable 2FA: Use app-based two-factor authentication, not SMS\n• Unique Passwords: Use different strong passwords for each exchange/service\n• Beware of Phishing: Verify website URLs and never click suspicious links\n• Verify Addresses: Always double-check addresses before sending crypto\n• Small Test Transactions: Send a small amount first to verify the correct address\n• Backup Seed Phrases: Store recovery phrases securely in multiple physical locations\n• Research Projects: Avoid scams by thoroughly investigating before investing\n\nRemember: Security breaches and scams are common in crypto. Taking proper precautions is not optional.',
            icon: Icons.security,
          ),

          _buildSection(
            title: 'Crypto Taxation',
            content: 'Understanding basic tax implications of crypto activities:\n\n• Trading Crypto: Usually treated as capital gains/losses\n• Mining Rewards: Often considered income at the fair market value when received\n• Staking Rewards: Generally taxed as income when received\n• NFT Sales: Typically subject to capital gains tax\n• Airdrops: Usually taxable as income when received\n• DeFi Activities: Complex tax implications that may include income and capital gains\n\nTax laws vary by country and change frequently. Consider using crypto tax software to track transactions and consult with a tax professional familiar with cryptocurrency.\n\nDisclaimer: This is not tax advice. Always consult with a qualified tax professional for your specific situation.',
            icon: Icons.receipt_long,
          ),

          // Ad banner
          const SizedBox(height: AppConstants.spacingLarge),
          const AdBanner(isHomeBanner: true),
        ],
      ),
    );
  }

  /// Builds a quick reference section
  Widget _buildQuickReferenceSection() {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Reference',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.spacingMedium),

            // Quick tips
            _buildQuickTip('Risk Management', 'Never risk more than 1-2% per trade'),
            _buildQuickTip('Market Hours', 'Most active: London-NY overlap (1-5 PM GMT)'),
            _buildQuickTip('Major Pairs', 'EUR/USD, GBP/USD, USD/JPY have lowest spreads'),
            _buildQuickTip('Demo Trading', 'Practice for 1-3 months before going live'),
          ],
        ),
      ),
    );
  }

  /// Builds a quick tip item
  Widget _buildQuickTip(String title, String tip) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.lightbulb,
            size: 16,
            color: Colors.amber,
          ),
          const SizedBox(width: AppConstants.spacingSmall),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: theme.textTheme.bodySmall,
                children: [
                  TextSpan(
                    text: '$title: ',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  TextSpan(text: tip),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds crypto quick reference section
  Widget _buildCryptoQuickReference() {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Crypto Quick Reference',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.spacingMedium),

            // Quick tips for crypto
            _buildQuickTip('Security', 'Use hardware wallets for large amounts'),
            _buildQuickTip('Research', 'DYOR - Do Your Own Research before investing'),
            _buildQuickTip('Volatility', 'Crypto markets are 24/7 and highly volatile'),
            _buildQuickTip('DeFi', 'Decentralized Finance offers new opportunities and risks'),
          ],
        ),
      ),
    );
  }

  /// Builds a section
  Widget _buildSection({
    required String title,
    required String content,
    required IconData icon,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingSmall),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withAlpha(20),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: Icon(
                    icon,
                    color: theme.colorScheme.primary,
                    size: AppConstants.iconSizeMedium,
                  ),
                ),
                const SizedBox(width: AppConstants.spacingMedium),
                Expanded(
                  child: Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.spacingMedium),
            Text(
              content,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(180),
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
