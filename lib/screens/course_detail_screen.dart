import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/trading_course.dart';
import 'package:learn_chart_patterns/providers/trading_course_provider.dart';
import 'package:learn_chart_patterns/screens/lesson_screen.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:provider/provider.dart';

/// The course detail screen of the app
class CourseDetailScreen extends StatelessWidget {
  /// The course ID
  final String courseId;
  
  /// Creates a new course detail screen
  const CourseDetailScreen({
    super.key,
    required this.courseId,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<TradingCourseProvider>(
      builder: (context, provider, child) {
        final course = provider.getCourseById(courseId);
        
        if (course == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Course Not Found'),
            ),
            body: const Center(
              child: Text('The requested course could not be found.'),
            ),
          );
        }
        
        final completionPercentage = course.getCompletionPercentage(provider.completionStatus);
        
        return Scaffold(
          backgroundColor: theme.colorScheme.surfaceContainerHighest,
          body: CustomScrollView(
            slivers: [
              // App bar
              SliverAppBar(
                expandedHeight: 200,
                pinned: true,
                backgroundColor: course.color,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    course.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Background color
                      Container(
                        color: course.color,
                      ),
                      
                      // Pattern overlay
                      Opacity(
                        opacity: 0.1,
                        child: Image.asset(
                          'assets/images/pattern_background.png',
                          fit: BoxFit.cover,
                        ),
                      ),
                      
                      // Course icon
                      Positioned(
                        right: AppConstants.paddingLarge,
                        bottom: AppConstants.paddingLarge,
                        child: Icon(
                          course.icon,
                          size: AppConstants.iconSizeLarge * 2,
                          color: Colors.white.withAlpha(100),
                        ),
                      ),
                      
                      // Gradient overlay
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: 100,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withAlpha(150),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Course content
              SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Course info
                    Card(
                      elevation: 0,
                      color: Colors.white,
                      margin: const EdgeInsets.all(AppConstants.paddingMedium),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Course metadata
                            Row(
                              children: [
                                // Difficulty
                                _buildMetadataChip(
                                  context: context,
                                  label: course.difficultyText,
                                  color: _getDifficultyColor(course.difficulty),
                                  icon: Icons.signal_cellular_alt,
                                ),
                                
                                const SizedBox(width: AppConstants.spacingSmall),
                                
                                // Type
                                _buildMetadataChip(
                                  context: context,
                                  label: course.typeText,
                                  color: course.color,
                                  icon: course.type == CourseType.forex
                                      ? Icons.currency_exchange
                                      : Icons.currency_bitcoin,
                                ),
                                
                                const SizedBox(width: AppConstants.spacingSmall),
                                
                                // Duration
                                _buildMetadataChip(
                                  context: context,
                                  label: course.formattedDuration,
                                  color: theme.colorScheme.primary,
                                  icon: Icons.access_time,
                                ),
                                
                                if (course.isPremium) ...[
                                  const SizedBox(width: AppConstants.spacingSmall),
                                  
                                  // Premium badge
                                  _buildMetadataChip(
                                    context: context,
                                    label: 'Premium',
                                    color: Colors.amber,
                                    icon: Icons.star,
                                  ),
                                ],
                              ],
                            ),
                            
                            const SizedBox(height: AppConstants.spacingMedium),
                            
                            // Course description
                            Text(
                              course.description,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withAlpha(180),
                                height: 1.5,
                              ),
                            ),
                            
                            const SizedBox(height: AppConstants.spacingMedium),
                            
                            // Author
                            Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: course.color.withAlpha(30),
                                  child: Text(
                                    course.author[0],
                                    style: TextStyle(
                                      color: course.color,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                
                                const SizedBox(width: AppConstants.spacingSmall),
                                
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Instructor',
                                      style: theme.textTheme.bodySmall?.copyWith(
                                        color: theme.colorScheme.onSurface.withAlpha(150),
                                      ),
                                    ),
                                    
                                    Text(
                                      course.author,
                                      style: theme.textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: AppConstants.spacingMedium),
                            
                            // Progress
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Your Progress',
                                      style: theme.textTheme.titleSmall?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      '${(completionPercentage * 100).toInt()}% Complete',
                                      style: theme.textTheme.bodySmall?.copyWith(
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                
                                const SizedBox(height: AppConstants.spacingSmall),
                                
                                LinearProgressIndicator(
                                  value: completionPercentage,
                                  backgroundColor: theme.colorScheme.primary.withAlpha(30),
                                  valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                                  minHeight: 8,
                                ),
                                
                                const SizedBox(height: AppConstants.spacingSmall),
                                
                                Text(
                                  '${course.getCompletedLessons(provider.completionStatus)}/${course.totalLessons} lessons completed',
                                  style: theme.textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // Course modules
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingMedium,
                      ),
                      child: Text(
                        'Course Content',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.spacingSmall),
                    
                    // Module list
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      itemCount: course.modules.length,
                      itemBuilder: (context, index) {
                        final module = course.modules[index];
                        return _buildModuleCard(context, module, provider, course.isPremium);
                      },
                    ),
                    
                    // Ad banner
                    if (!course.isPremium) ...[
                      const SizedBox(height: AppConstants.spacingMedium),
                      const AdBanner(isHomeBanner: false),
                      const SizedBox(height: AppConstants.spacingLarge),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// Builds a module card
  Widget _buildModuleCard(
    BuildContext context,
    CourseModule module,
    TradingCourseProvider provider,
    bool isPremiumCourse,
  ) {
    final theme = Theme.of(context);
    final completionPercentage = module.getCompletionPercentage(provider.completionStatus);
    
    return Card(
      elevation: 0,
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Module header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingSmall),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withAlpha(20),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: Icon(
                    module.icon,
                    color: theme.colorScheme.primary,
                    size: AppConstants.iconSizeMedium,
                  ),
                ),
                
                const SizedBox(width: AppConstants.spacingMedium),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        module.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      
                      const SizedBox(height: AppConstants.spacingSmall / 2),
                      
                      Text(
                        module.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Progress
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: completionPercentage,
                    backgroundColor: theme.colorScheme.primary.withAlpha(30),
                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    minHeight: 4,
                  ),
                ),
                
                const SizedBox(width: AppConstants.spacingSmall),
                
                Text(
                  '${(completionPercentage * 100).toInt()}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingMedium),
            
            // Lesson list
            ...module.lessons.map((lesson) => _buildLessonItem(
              context,
              lesson,
              provider,
              isPremiumCourse,
            )).toList(),
          ],
        ),
      ),
    );
  }
  
  /// Builds a lesson item
  Widget _buildLessonItem(
    BuildContext context,
    Lesson lesson,
    TradingCourseProvider provider,
    bool isPremiumCourse,
  ) {
    final theme = Theme.of(context);
    final isCompleted = provider.isLessonCompleted(lesson.id);
    
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Container(
        padding: const EdgeInsets.all(AppConstants.paddingSmall),
        decoration: BoxDecoration(
          color: isCompleted
              ? Colors.green.withAlpha(20)
              : theme.colorScheme.primary.withAlpha(20),
          shape: BoxShape.circle,
        ),
        child: Icon(
          isCompleted ? Icons.check : lesson.contentTypeIcon,
          color: isCompleted ? Colors.green : theme.colorScheme.primary,
          size: AppConstants.iconSizeSmall,
        ),
      ),
      title: Text(
        lesson.title,
        style: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text(
        '${lesson.contentTypeText} • ${lesson.durationMinutes} min',
        style: theme.textTheme.bodySmall,
      ),
      trailing: isPremiumCourse
          ? const Icon(Icons.lock_open)
          : const Icon(Icons.arrow_forward_ios, size: AppConstants.iconSizeSmall),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => LessonScreen(
              courseId: courseId,
              lessonId: lesson.id,
            ),
          ),
        );
      },
    );
  }
  
  /// Builds a metadata chip
  Widget _buildMetadataChip({
    required BuildContext context,
    required String label,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall / 2,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: AppConstants.iconSizeSmall,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Gets the difficulty color
  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return Colors.green;
      case DifficultyLevel.intermediate:
        return Colors.orange;
      case DifficultyLevel.advanced:
        return Colors.red;
    }
  }
}
