import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/features/trade_journal/screens/journal_screen.dart';
import 'package:learn_chart_patterns/models/premium_content.dart';
import 'package:learn_chart_patterns/providers/progress_provider.dart';
import 'package:learn_chart_patterns/screens/basics_screen.dart';
import 'package:learn_chart_patterns/screens/learn_screen.dart';
import 'package:learn_chart_patterns/screens/premium_content_detail_screen.dart';
import 'package:learn_chart_patterns/screens/premium_content_screen.dart';
import 'package:learn_chart_patterns/screens/premium_screen.dart';
import 'package:learn_chart_patterns/screens/quiz_screen.dart';
import 'package:learn_chart_patterns/screens/settings_screen.dart';
import 'package:learn_chart_patterns/screens/trading_education_screen.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:provider/provider.dart';

/// The home screen of the app
class HomeScreen extends StatefulWidget {
  /// Creates a new home screen
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();

    // Update streak when the screen is first shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProgressProvider>().updateStreak();
    });
  }

  /// Gets the color for the streak
  Color _getStreakColor(int streakValue) {
    if (streakValue >= 10) {
      return Colors.red;
    } else if (streakValue >= 7) {
      return Colors.orange;
    } else if (streakValue >= 3) {
      return Colors.amber;
    } else if (streakValue > 0) {
      return Colors.green;
    } else {
      return Colors.grey;
    }
  }

  /// Gets the emoji for the streak
  String _getStreakEmoji(int streakValue) {
    if (streakValue >= 10) {
      return '🔥🔥🔥';
    } else if (streakValue >= 7) {
      return '🔥🔥';
    } else if (streakValue >= AppConstants.minStreakForFire) {
      return '🔥';
    } else {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final progressProvider = context.watch<ProgressProvider>();
    final purchaseService = context.watch<PurchaseService>();
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surfaceContainerHighest,
        elevation: 0,
        scrolledUnderElevation: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome back',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(180),
              ),
            ),
            Text(
              AppConstants.appName,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.settings),
              color: theme.colorScheme.primary,
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SettingsScreen(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Streak indicator
            Container(
              margin: const EdgeInsets.only(bottom: AppConstants.spacingLarge),
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withAlpha(15),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withAlpha(30),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.local_fire_department,
                      color: _getStreakColor(progressProvider.currentStreak),
                      size: AppConstants.iconSizeLarge,
                    ),
                  ),
                  const SizedBox(width: AppConstants.spacingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your current streak',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(180),
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              '${progressProvider.currentStreak} days',
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: _getStreakColor(progressProvider.currentStreak),
                              ),
                            ),
                            const SizedBox(width: AppConstants.spacingSmall),
                            Text(
                              _getStreakEmoji(progressProvider.currentStreak),
                              style: const TextStyle(fontSize: 20),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (progressProvider.highestStreak > 0) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingMedium,
                        vertical: AppConstants.paddingSmall,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withAlpha(20),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Best',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withAlpha(180),
                            ),
                          ),
                          Text(
                            '${progressProvider.highestStreak}',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _getStreakColor(progressProvider.highestStreak),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Section title
            Padding(
              padding: const EdgeInsets.only(
                left: AppConstants.paddingSmall,
                bottom: AppConstants.paddingMedium,
              ),
              child: Text(
                'Your Progress',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Progress cards
            Row(
              children: [
                // Completion progress
                Expanded(
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                    ),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LearnScreen(),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                      child: Padding(
                        padding: const EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(AppConstants.paddingSmall),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary.withAlpha(20),
                                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                              ),
                              child: Icon(
                                Icons.check_circle,
                                color: theme.colorScheme.primary,
                                size: AppConstants.iconSizeMedium,
                              ),
                            ),
                            const SizedBox(height: AppConstants.spacingMedium),
                            Text(
                              'Completion',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: AppConstants.spacingSmall),
                            Text(
                              'Patterns learned',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withAlpha(180),
                              ),
                            ),
                            const SizedBox(height: AppConstants.spacingMedium),
                            LinearProgressIndicator(
                              value: progressProvider.completionPercentage,
                              backgroundColor: theme.colorScheme.primary.withAlpha(30),
                              color: theme.colorScheme.primary,
                              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                              minHeight: 6,
                            ),
                            const SizedBox(height: AppConstants.spacingSmall),
                            Align(
                              alignment: Alignment.centerRight,
                              child: Text(
                                '${(progressProvider.completionPercentage * 100).toInt()}%',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: AppConstants.spacingMedium),

                // Quiz accuracy
                Expanded(
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                    ),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const QuizScreen(),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                      child: Padding(
                        padding: const EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(AppConstants.paddingSmall),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFF6B6B).withAlpha(20),
                                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                              ),
                              child: const Icon(
                                Icons.analytics,
                                color: Color(0xFFFF6B6B),
                                size: AppConstants.iconSizeMedium,
                              ),
                            ),
                            const SizedBox(height: AppConstants.spacingMedium),
                            Text(
                              'Accuracy',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: AppConstants.spacingSmall),
                            Text(
                              'Quiz performance',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withAlpha(180),
                              ),
                            ),
                            const SizedBox(height: AppConstants.spacingMedium),
                            LinearProgressIndicator(
                              value: progressProvider.overallQuizAccuracy,
                              backgroundColor: const Color(0xFFFF6B6B).withAlpha(30),
                              color: const Color(0xFFFF6B6B),
                              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                              minHeight: 6,
                            ),
                            const SizedBox(height: AppConstants.spacingSmall),
                            Align(
                              alignment: Alignment.centerRight,
                              child: Text(
                                '${(progressProvider.overallQuizAccuracy * 100).toInt()}%',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFFFF6B6B),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.spacingLarge),

            // Section title
            Text(
              'Study Chart Patterns',
              style: Theme.of(context).textTheme.titleLarge,
            ),

            const SizedBox(height: AppConstants.spacingMedium),

            // Main menu cards
            _buildMenuCard(
              context: context,
              title: 'Learn Patterns',
              description: 'Study and understand different chart patterns',
              icon: Icons.menu_book,
              color: Colors.blue,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LearnScreen(),
                  ),
                );
              },
            ),

            _buildMenuCard(
              context: context,
              title: 'Take Quizzes',
              description: 'Test your knowledge with pattern recognition quizzes',
              icon: Icons.quiz,
              color: Colors.orange,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const QuizScreen(),
                  ),
                );
              },
            ),

            _buildMenuCard(
              context: context,
              title: 'Candlestick Basics',
              description: 'Learn the fundamentals of candlestick charts',
              icon: Icons.school,
              color: Colors.green,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const BasicsScreen(),
                  ),
                );
              },
            ),

            _buildMenuCard(
              context: context,
              title: 'Trading Basics',
              description: 'Learn the fundamentals of forex and crypto trading',
              icon: Icons.currency_exchange,
              color: Colors.purple,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TradingEducationScreen(),
                  ),
                );
              },
            ),

            _buildMenuCard(
              context: context,
              title: 'Trade Journal',
              description: 'Track and analyze your trades to improve performance',
              icon: Icons.analytics,
              color: Colors.teal,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const JournalScreen(),
                  ),
                );
              },
            ),

            const SizedBox(height: AppConstants.spacingLarge),

            // Premium content section
            _buildPremiumContentSection(context),

            const SizedBox(height: AppConstants.spacingLarge),

            // Stats section
            Text(
              'Your Stats',
              style: Theme.of(context).textTheme.titleLarge,
            ),

            const SizedBox(height: AppConstants.spacingMedium),

            // Stats cards
            Card(
              elevation: AppConstants.elevationSmall,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                child: Column(
                  children: [
                    _buildStatRow(
                      context: context,
                      label: 'Patterns Learned',
                      value: '${progressProvider.progress.learnedPatterns.values.where((v) => v).length}/${progressProvider.progress.learnedPatterns.length}',
                      icon: Icons.check_circle,
                      color: Colors.blue,
                    ),

                    const Divider(),

                    _buildStatRow(
                      context: context,
                      label: 'Quizzes Completed',
                      value: '${progressProvider.totalQuizzesCompleted}',
                      icon: Icons.quiz,
                      color: Colors.orange,
                    ),

                    const Divider(),

                    _buildStatRow(
                      context: context,
                      label: 'Correct Answers',
                      value: '${progressProvider.totalCorrectAnswers}',
                      icon: Icons.check,
                      color: Colors.green,
                    ),

                    const Divider(),

                    _buildStatRow(
                      context: context,
                      label: 'Current Streak',
                      value: '${progressProvider.currentStreak} days',
                      icon: Icons.local_fire_department,
                      color: Colors.red,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppConstants.spacingLarge),

            // Ad banner
            const AdBanner(isHomeBanner: true),
          ],
        ),
      ),
    );
  }

  /// Builds a menu card
  Widget _buildMenuCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: AppConstants.elevationSmall,
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              // Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: color.withAlpha(25),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: AppConstants.iconSizeLarge,
                ),
              ),

              const SizedBox(width: AppConstants.spacingMedium),

              // Text
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppConstants.spacingSmall),

                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),

              // Arrow
              Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(128),
                size: AppConstants.iconSizeSmall,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a premium content section
  Widget _buildPremiumContentSection(BuildContext context) {
    final theme = Theme.of(context);
    final purchaseService = context.watch<PurchaseService>();

    // Get a subset of premium content items to display
    final premiumContent = PremiumContent.getAll().take(3).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with "View All" button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Premium Content',
              style: theme.textTheme.titleLarge,
            ),
            TextButton(
              onPressed: () {
                HapticFeedback.selectionClick();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PremiumContentScreen(),
                  ),
                );
              },
              child: Row(
                children: [
                  Text(
                    'View All',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: AppConstants.spacingSmall / 2),
                  Icon(
                    Icons.arrow_forward,
                    size: AppConstants.iconSizeSmall,
                    color: theme.colorScheme.primary,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: AppConstants.spacingMedium),

        // Premium content cards
        for (final content in premiumContent)
          _buildPremiumContentCard(
            context: context,
            content: content,
            purchaseService: purchaseService,
          ),
      ],
    );
  }

  /// Builds a premium content card
  Widget _buildPremiumContentCard({
    required BuildContext context,
    required PremiumContent content,
    required PurchaseService purchaseService,
  }) {
    final theme = Theme.of(context);

    // Check if the content is locked
    final bool isLocked = _isContentLocked(content, purchaseService);

    return Card(
      elevation: AppConstants.elevationSmall,
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: InkWell(
        onTap: () {
          HapticFeedback.selectionClick();
          if (isLocked) {
            // Show upgrade dialog
            _showUpgradeDialog(context);
          } else {
            // Navigate to content detail
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PremiumContentDetailScreen(content: content),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              // Icon with lock overlay if locked
              Stack(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: content.color.withAlpha(25),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                    ),
                    child: Icon(
                      content.icon,
                      color: content.color,
                      size: AppConstants.iconSizeLarge,
                    ),
                  ),
                  if (isLocked)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.lock,
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(width: AppConstants.spacingMedium),

              // Text
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      content.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppConstants.spacingSmall),

                    Text(
                      content.description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(180),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Arrow
              Icon(
                Icons.arrow_forward_ios,
                color: theme.colorScheme.onSurface.withAlpha(128),
                size: AppConstants.iconSizeSmall,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Checks if the content is locked
  bool _isContentLocked(PremiumContent content, PurchaseService purchaseService) {
    if (!purchaseService.hasPremium) {
      return true;
    }

    if (content.requiresAdvancedPatterns && !purchaseService.hasAdvancedPatterns) {
      return true;
    }

    if (content.requiresStrategyGuides && !purchaseService.hasStrategyGuides) {
      return true;
    }

    return false;
  }

  /// Shows the upgrade dialog
  void _showUpgradeDialog(BuildContext context) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 40),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.lock,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
              const Text('Premium Content'),
            ],
          ),
          content: const Text(
            'This content is only available with a premium subscription. Upgrade now to access all premium content.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                'Cancel',
                style: TextStyle(color: theme.colorScheme.primary),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PremiumScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Upgrade Now'),
            ),
          ],
        );
      },
    );
  }

  /// Builds a stat row
  Widget _buildStatRow({
    required BuildContext context,
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: AppConstants.paddingSmall,
      ),
      child: Row(
        children: [
          // Icon
          Icon(
            icon,
            color: color,
            size: AppConstants.iconSizeMedium,
          ),

          const SizedBox(width: AppConstants.spacingMedium),

          // Label
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),

          const Spacer(),

          // Value
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
