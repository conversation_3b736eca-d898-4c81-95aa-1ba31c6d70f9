import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/premium_content.dart';
import 'package:learn_chart_patterns/screens/premium_screen.dart';
import 'package:learn_chart_patterns/services/purchase_service.dart';
import 'package:learn_chart_patterns/widgets/ad_banner.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher_string.dart';

/// Screen that displays the detailed content for a premium item
class PremiumContentDetailScreen extends StatelessWidget {
  /// The premium content to display
  final PremiumContent content;

  /// Creates a new premium content detail screen
  const PremiumContentDetailScreen({
    super.key,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final purchaseService = context.watch<PurchaseService>();

    // Check if the content is locked
    final bool isLocked = _isContentLocked(content, purchaseService);

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          content.title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Upgrade button for locked content
          if (isLocked)
            TextButton.icon(
              onPressed: () {
                HapticFeedback.selectionClick();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PremiumScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.star),
              label: const Text('Upgrade'),
              style: TextButton.styleFrom(
                foregroundColor: theme.colorScheme.primary,
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  _buildHeader(context, theme),

                  const SizedBox(height: AppConstants.spacingMedium),

                  // Content
                  if (isLocked)
                    _buildLockedContent(context, theme)
                  else
                    _buildFullContent(context, theme),
                ],
              ),
            ),
          ),

          // Upgrade banner for locked content
          if (isLocked) _buildUpgradeBanner(context, theme),

          // Ad banner (only shown if not premium)
          if (!purchaseService.hasPremium) const AdBanner(isHomeBanner: true),
        ],
      ),
      // Floating action button for premium upgrade
      floatingActionButton: isLocked
          ? FloatingActionButton.extended(
              onPressed: () {
                HapticFeedback.mediumImpact();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PremiumScreen(),
                  ),
                );
              },
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.lock_open),
              label: const Text('Unlock Content'),
              elevation: 4,
            )
          : null,
    );
  }

  /// Builds the header section
  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Card(
      elevation: 0,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: content.color.withAlpha(20),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              child: Icon(
                content.icon,
                color: content.color,
                size: AppConstants.iconSizeLarge,
              ),
            ),
            const SizedBox(width: AppConstants.spacingMedium),
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    content.title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.spacingSmall / 2),
                  Text(
                    content.description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(180),
                    ),
                  ),
                  const SizedBox(height: AppConstants.spacingSmall),
                  // Required purchase tag
                  if (content.requiresAdvancedPatterns || content.requiresStrategyGuides)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingSmall,
                        vertical: AppConstants.paddingSmall / 2,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withAlpha(20),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                      ),
                      child: Text(
                        content.requiresAdvancedPatterns
                            ? 'Advanced Patterns'
                            : 'Strategy Guides',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the full content for premium users
  Widget _buildFullContent(BuildContext context, ThemeData theme) {
    return Card(
      elevation: 0,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: MarkdownBody(
          data: content.content,
          styleSheet: MarkdownStyleSheet(
            h1: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            h2: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            h3: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            p: theme.textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
            code: theme.textTheme.bodySmall?.copyWith(
              fontFamily: 'monospace',
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              color: theme.colorScheme.primary,
            ),
            codeblockDecoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
            ),
            blockquote: theme.textTheme.bodyMedium?.copyWith(
              fontStyle: FontStyle.italic,
              color: theme.colorScheme.onSurface.withAlpha(180),
            ),
            listBullet: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
          onTapLink: (text, href, title) {
            if (href != null) {
              launchUrlString(href);
            }
          },
        ),
      ),
    );
  }

  /// Builds a preview of the content for non-premium users
  Widget _buildLockedContent(BuildContext context, ThemeData theme) {
    // Extract the first section of the content (up to the first h2 heading)
    final String previewContent = _extractPreviewContent(content.content);

    return Stack(
      children: [
        // Preview content
        Card(
          elevation: 0,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MarkdownBody(
                  data: previewContent,
                  styleSheet: MarkdownStyleSheet(
                    h1: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                    h2: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    h3: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    p: theme.textTheme.bodyMedium?.copyWith(
                      height: 1.5,
                    ),
                  ),
                ),
                const SizedBox(height: AppConstants.spacingLarge),
                const SizedBox(height: AppConstants.spacingLarge),
                const SizedBox(height: AppConstants.spacingLarge),
              ],
            ),
          ),
        ),

        // Gradient overlay
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 200,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white.withAlpha(0),
                  Colors.white.withAlpha(200),
                  Colors.white,
                ],
              ),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(AppConstants.borderRadiusLarge),
                bottomRight: Radius.circular(AppConstants.borderRadiusLarge),
              ),
            ),
          ),
        ),

        // Lock icon and message
        Positioned(
          bottom: AppConstants.paddingLarge,
          left: 0,
          right: 0,
          child: Column(
            children: [
              Icon(
                Icons.lock,
                color: theme.colorScheme.primary,
                size: AppConstants.iconSizeLarge,
              ),
              const SizedBox(height: AppConstants.spacingSmall),
              Text(
                'Unlock full content with premium',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds the upgrade banner for locked content
  Widget _buildUpgradeBanner(BuildContext context, ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      color: theme.colorScheme.primary,
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Unlock this premium content',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: AppConstants.spacingMedium),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.mediumImpact();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PremiumScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: theme.colorScheme.primary,
              elevation: 0,
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
            ),
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  /// Checks if the content is locked
  bool _isContentLocked(PremiumContent content, PurchaseService purchaseService) {
    if (!purchaseService.hasPremium) {
      return true;
    }

    if (content.requiresAdvancedPatterns && !purchaseService.hasAdvancedPatterns) {
      return true;
    }

    if (content.requiresStrategyGuides && !purchaseService.hasStrategyGuides) {
      return true;
    }

    return false;
  }

  /// Extracts a preview of the content (first section)
  String _extractPreviewContent(String fullContent) {
    // Find the first h2 heading
    final RegExp h2Regex = RegExp(r'^## ', multiLine: true);
    final Match? match = h2Regex.firstMatch(fullContent);

    if (match != null) {
      // Return content up to the first h2 heading
      return fullContent.substring(0, match.start);
    } else {
      // If no h2 heading found, return the first few paragraphs
      final paragraphs = fullContent.split('\n\n');
      if (paragraphs.length > 3) {
        return paragraphs.take(3).join('\n\n');
      } else {
        return fullContent;
      }
    }
  }
}
