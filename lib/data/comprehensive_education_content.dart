import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/models/comprehensive_trading_education.dart';

/// Comprehensive education content for forex and crypto trading
class ComprehensiveEducationContent {
  /// Gets all available courses
  static List<TradingEducationCourse> getAllCourses() {
    return [
      ...getForexCourses(),
      ...getCryptoCourses(),
      ...getGeneralTradingCourses(),
    ];
  }

  /// Gets forex trading courses
  static List<TradingEducationCourse> getForexCourses() {
    return [
      // Beginner Forex Course
      TradingEducationCourse(
        title: 'Forex Trading Fundamentals',
        description: 'Complete beginner\'s guide to forex trading, covering currency pairs, market mechanics, and basic analysis techniques.',
        marketType: TradingMarketType.forex,
        difficulty: CourseDifficulty.beginner,
        modules: _getForexBeginnerModules(),
        learningOutcomes: [
          'Understand how the forex market works',
          'Learn about major currency pairs and their characteristics',
          'Master basic technical and fundamental analysis',
          'Develop risk management strategies',
          'Create and test trading strategies',
        ],
        estimatedHours: 12,
        instructor: 'Professional Forex Trader',
        rating: 4.8,
        isPremium: false,
      ),
      
      // Intermediate Forex Course
      TradingEducationCourse(
        title: 'Advanced Forex Strategies',
        description: 'Advanced forex trading strategies, market psychology, and professional trading techniques.',
        marketType: TradingMarketType.forex,
        difficulty: CourseDifficulty.intermediate,
        modules: _getForexIntermediateModules(),
        prerequisites: ['forex_fundamentals'],
        learningOutcomes: [
          'Master advanced chart patterns and indicators',
          'Understand market psychology and sentiment',
          'Learn professional risk management techniques',
          'Develop algorithmic trading strategies',
          'Analyze economic indicators and news impact',
        ],
        estimatedHours: 18,
        instructor: 'Senior Market Analyst',
        rating: 4.9,
        isPremium: true,
      ),
    ];
  }

  /// Gets cryptocurrency trading courses
  static List<TradingEducationCourse> getCryptoCourses() {
    return [
      // Beginner Crypto Course
      TradingEducationCourse(
        title: 'Cryptocurrency Trading Basics',
        description: 'Introduction to cryptocurrency markets, blockchain technology, and crypto trading fundamentals.',
        marketType: TradingMarketType.crypto,
        difficulty: CourseDifficulty.beginner,
        modules: _getCryptoBeginnerModules(),
        learningOutcomes: [
          'Understand blockchain technology and cryptocurrencies',
          'Learn about different types of cryptocurrencies',
          'Master crypto trading platforms and tools',
          'Understand DeFi and yield farming basics',
          'Develop security best practices',
        ],
        estimatedHours: 10,
        instructor: 'Blockchain Expert',
        rating: 4.7,
        isPremium: false,
      ),
      
      // Advanced Crypto Course
      TradingEducationCourse(
        title: 'Advanced Crypto Trading & DeFi',
        description: 'Advanced cryptocurrency trading strategies, DeFi protocols, and institutional crypto trading.',
        marketType: TradingMarketType.crypto,
        difficulty: CourseDifficulty.advanced,
        modules: _getCryptoAdvancedModules(),
        prerequisites: ['crypto_basics'],
        learningOutcomes: [
          'Master advanced crypto trading strategies',
          'Understand DeFi protocols and yield optimization',
          'Learn about NFTs and tokenomics',
          'Develop portfolio management strategies',
          'Understand regulatory landscape',
        ],
        estimatedHours: 20,
        instructor: 'DeFi Protocol Developer',
        rating: 4.8,
        isPremium: true,
      ),
    ];
  }

  /// Gets general trading courses
  static List<TradingEducationCourse> getGeneralTradingCourses() {
    return [
      TradingEducationCourse(
        title: 'Trading Psychology Mastery',
        description: 'Master the psychological aspects of trading, emotional control, and mental discipline.',
        marketType: TradingMarketType.general,
        difficulty: CourseDifficulty.intermediate,
        modules: _getTradingPsychologyModules(),
        learningOutcomes: [
          'Understand trading psychology fundamentals',
          'Develop emotional control techniques',
          'Learn stress management strategies',
          'Master discipline and consistency',
          'Overcome common trading biases',
        ],
        estimatedHours: 8,
        instructor: 'Trading Psychology Expert',
        rating: 4.9,
        isPremium: true,
      ),
    ];
  }

  /// Gets forex beginner modules
  static List<EducationModule> _getForexBeginnerModules() {
    return [
      EducationModule(
        title: 'Introduction to Forex',
        description: 'Understanding the foreign exchange market basics',
        lessons: [
          EducationLesson(
            title: 'What is Forex Trading?',
            content: const LessonContent(
              text: '''
The foreign exchange market, commonly known as Forex or FX, is the largest and most liquid financial market in the world. With a daily trading volume exceeding \$6 trillion, it operates 24 hours a day, five days a week.

## Key Characteristics:
- **Decentralized Market**: No central exchange, trading occurs over-the-counter (OTC)
- **High Liquidity**: Easy to buy and sell currencies
- **24/5 Operation**: Markets open from Sunday evening to Friday evening
- **Global Participation**: Banks, corporations, governments, and individual traders

## How Forex Works:
Forex trading involves buying one currency while simultaneously selling another. Currencies are traded in pairs, such as EUR/USD (Euro/US Dollar) or GBP/JPY (British Pound/Japanese Yen).

When you buy EUR/USD, you're buying Euros and selling US Dollars, betting that the Euro will strengthen against the Dollar.
              ''',
            ),
            type: LessonType.reading,
            order: 1,
            estimatedMinutes: 15,
            keyConcepts: ['Forex market', 'Currency pairs', 'OTC trading', 'Market hours'],
          ),
          EducationLesson(
            title: 'Major Currency Pairs',
            content: const LessonContent(
              text: '''
Currency pairs are categorized into three main groups based on their trading volume and liquidity:

## Major Pairs (Most Traded):
- **EUR/USD** - Euro/US Dollar (Most traded pair)
- **USD/JPY** - US Dollar/Japanese Yen
- **GBP/USD** - British Pound/US Dollar
- **USD/CHF** - US Dollar/Swiss Franc
- **AUD/USD** - Australian Dollar/US Dollar
- **USD/CAD** - US Dollar/Canadian Dollar
- **NZD/USD** - New Zealand Dollar/US Dollar

## Minor Pairs (Cross Currencies):
Currency pairs that don't include the US Dollar:
- EUR/GBP, EUR/JPY, GBP/JPY, etc.

## Exotic Pairs:
Pairs that include currencies from emerging markets:
- USD/TRY (Turkish Lira), EUR/ZAR (South African Rand), etc.

## Understanding Pair Notation:
In EUR/USD:
- **Base Currency**: EUR (the first currency)
- **Quote Currency**: USD (the second currency)
- **Exchange Rate**: How much of the quote currency is needed to buy one unit of the base currency
              ''',
            ),
            type: LessonType.reading,
            order: 2,
            estimatedMinutes: 20,
            keyConcepts: ['Major pairs', 'Minor pairs', 'Exotic pairs', 'Base currency', 'Quote currency'],
          ),
        ],
        order: 1,
        estimatedMinutes: 120,
      ),
      
      EducationModule(
        title: 'Market Analysis Fundamentals',
        description: 'Basic technical and fundamental analysis techniques',
        lessons: [
          EducationLesson(
            title: 'Technical Analysis Basics',
            content: const LessonContent(
              text: '''
Technical analysis is the study of price movements and trading volume to predict future price direction. It's based on three key principles:

## Core Principles:
1. **Price Discounts Everything**: All information is reflected in the price
2. **Price Moves in Trends**: Prices tend to move in identifiable trends
3. **History Repeats**: Past price patterns tend to repeat

## Essential Tools:
- **Support and Resistance**: Key price levels where buying/selling pressure emerges
- **Trend Lines**: Lines connecting swing highs or lows to identify trend direction
- **Moving Averages**: Smoothed price data to identify trend direction
- **Chart Patterns**: Recognizable formations that suggest future price movement

## Timeframes:
- **Scalping**: 1-5 minute charts
- **Day Trading**: 15 minute to 1 hour charts
- **Swing Trading**: 4 hour to daily charts
- **Position Trading**: Daily to weekly charts

Understanding these basics will help you make informed trading decisions based on price action and market structure.
              ''',
            ),
            type: LessonType.reading,
            order: 1,
            estimatedMinutes: 25,
            keyConcepts: ['Technical analysis', 'Support/Resistance', 'Trend lines', 'Chart patterns'],
          ),
        ],
        order: 2,
        estimatedMinutes: 180,
      ),
    ];
  }

  /// Gets forex intermediate modules
  static List<EducationModule> _getForexIntermediateModules() {
    return [
      EducationModule(
        title: 'Advanced Chart Patterns',
        description: 'Complex pattern recognition and trading strategies',
        lessons: [
          EducationLesson(
            title: 'Harmonic Patterns',
            content: const LessonContent(
              text: '''
Harmonic patterns are advanced chart patterns that use Fibonacci ratios to identify potential reversal points. These patterns are more precise than traditional chart patterns.

## Key Harmonic Patterns:

### Gartley Pattern:
- **Structure**: XABCD formation
- **Fibonacci Ratios**: 
  - AB = 61.8% of XA
  - BC = 38.2% or 88.6% of AB
  - CD = 127.2% or 161.8% of BC
- **Entry**: At point D
- **Stop Loss**: Beyond X
- **Target**: 38.2% and 61.8% retracement of AD

### Butterfly Pattern:
- **Extension Pattern**: CD leg extends beyond the XA leg
- **Key Ratio**: D point at 127.2% or 161.8% of XA
- **Higher Probability**: When multiple Fibonacci levels converge

### Bat Pattern:
- **Conservative Pattern**: Less extension than Butterfly
- **D Point**: 88.6% retracement of XA
- **Tight Stop Loss**: Due to precise entry point

## Trading Harmonic Patterns:
1. **Identify the Pattern**: Use Fibonacci tools to confirm ratios
2. **Wait for Completion**: Enter only at the completion point (D)
3. **Confirm with Indicators**: Use RSI, MACD for additional confirmation
4. **Manage Risk**: Set stop loss beyond the pattern's invalidation point
              ''',
            ),
            type: LessonType.reading,
            order: 1,
            estimatedMinutes: 30,
            keyConcepts: ['Harmonic patterns', 'Fibonacci ratios', 'Gartley', 'Butterfly', 'Bat pattern'],
          ),
        ],
        order: 1,
        estimatedMinutes: 240,
      ),
    ];
  }

  /// Gets crypto beginner modules
  static List<EducationModule> _getCryptoBeginnerModules() {
    return [
      EducationModule(
        title: 'Blockchain & Cryptocurrency Basics',
        description: 'Understanding the technology behind cryptocurrencies',
        lessons: [
          EducationLesson(
            title: 'What is Blockchain?',
            content: const LessonContent(
              text: '''
Blockchain is a distributed ledger technology that maintains a continuously growing list of records, called blocks, which are linked and secured using cryptography.

## Key Features:
- **Decentralization**: No single point of control
- **Transparency**: All transactions are publicly visible
- **Immutability**: Once recorded, data cannot be easily changed
- **Security**: Cryptographic hashing protects data integrity

## How It Works:
1. **Transaction Initiation**: User initiates a cryptocurrency transaction
2. **Broadcasting**: Transaction is broadcast to the network
3. **Validation**: Network nodes validate the transaction
4. **Block Creation**: Valid transactions are grouped into a block
5. **Consensus**: Network reaches consensus on the new block
6. **Block Addition**: New block is added to the blockchain
7. **Completion**: Transaction is complete and irreversible

## Types of Blockchains:
- **Public**: Open to everyone (Bitcoin, Ethereum)
- **Private**: Restricted access (Enterprise solutions)
- **Consortium**: Semi-decentralized (Banking consortiums)
- **Hybrid**: Combination of public and private elements
              ''',
            ),
            type: LessonType.reading,
            order: 1,
            estimatedMinutes: 20,
            keyConcepts: ['Blockchain', 'Decentralization', 'Consensus', 'Cryptography'],
          ),
        ],
        order: 1,
        estimatedMinutes: 150,
      ),
    ];
  }

  /// Gets crypto advanced modules
  static List<EducationModule> _getCryptoAdvancedModules() {
    return [
      EducationModule(
        title: 'DeFi Protocols & Yield Farming',
        description: 'Advanced decentralized finance strategies',
        lessons: [
          EducationLesson(
            title: 'Understanding DeFi',
            content: const LessonContent(
              text: '''
Decentralized Finance (DeFi) refers to financial services built on blockchain technology that operate without traditional intermediaries like banks.

## Core DeFi Concepts:
- **Smart Contracts**: Self-executing contracts with terms directly written into code
- **Liquidity Pools**: Collections of funds locked in smart contracts
- **Automated Market Makers (AMMs)**: Algorithms that price assets based on supply and demand
- **Yield Farming**: Strategy to maximize returns by moving funds between different DeFi protocols

## Popular DeFi Protocols:
- **Uniswap**: Decentralized exchange (DEX) for token swapping
- **Compound**: Lending and borrowing protocol
- **Aave**: Flash loans and variable interest rates
- **MakerDAO**: Decentralized stablecoin (DAI) creation
- **Curve**: Optimized for stablecoin trading

## Risks in DeFi:
- **Smart Contract Risk**: Bugs or vulnerabilities in code
- **Impermanent Loss**: Loss due to price divergence in liquidity pools
- **Regulatory Risk**: Changing regulations affecting DeFi protocols
- **Liquidity Risk**: Difficulty withdrawing funds during market stress
              ''',
            ),
            type: LessonType.reading,
            order: 1,
            estimatedMinutes: 25,
            keyConcepts: ['DeFi', 'Smart contracts', 'Liquidity pools', 'Yield farming', 'AMM'],
          ),
        ],
        order: 1,
        estimatedMinutes: 200,
      ),
    ];
  }

  /// Gets trading psychology modules
  static List<EducationModule> _getTradingPsychologyModules() {
    return [
      EducationModule(
        title: 'Emotional Control in Trading',
        description: 'Managing emotions and developing discipline',
        lessons: [
          EducationLesson(
            title: 'Common Trading Emotions',
            content: const LessonContent(
              text: '''
Trading psychology is often the difference between successful and unsuccessful traders. Understanding and managing emotions is crucial for long-term success.

## Primary Trading Emotions:

### Fear:
- **Fear of Missing Out (FOMO)**: Rushing into trades
- **Fear of Loss**: Hesitation to enter valid setups
- **Fear of Being Wrong**: Inability to accept losses

### Greed:
- **Overtrading**: Taking too many positions
- **Overleveraging**: Using excessive position sizes
- **Profit Target Abandonment**: Holding winners too long

### Hope:
- **Holding Losing Positions**: Refusing to cut losses
- **Averaging Down**: Adding to losing positions
- **Ignoring Stop Losses**: Moving stops against you

## Psychological Biases:
- **Confirmation Bias**: Seeking information that confirms your position
- **Anchoring Bias**: Fixating on specific price levels
- **Recency Bias**: Overweighting recent events
- **Overconfidence Bias**: Believing you can predict markets consistently

## Building Emotional Discipline:
1. **Develop a Trading Plan**: Clear rules for entry, exit, and risk management
2. **Practice Mindfulness**: Stay present and aware of emotional states
3. **Keep a Trading Journal**: Record both trades and emotions
4. **Use Position Sizing**: Never risk more than you can afford to lose
5. **Take Breaks**: Step away during emotional periods
              ''',
            ),
            type: LessonType.reading,
            order: 1,
            estimatedMinutes: 20,
            keyConcepts: ['Trading psychology', 'Fear', 'Greed', 'Emotional discipline', 'Cognitive biases'],
          ),
        ],
        order: 1,
        estimatedMinutes: 120,
      ),
    ];
  }
}
