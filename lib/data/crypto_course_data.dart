import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/models/trading_course.dart';

/// Provides cryptocurrency course data
class CryptoCourseData {
  /// Get crypto courses
  static List<TradingCourse> getCourses() {
    return [
      _getCryptoFundamentalsCourse(),
      _getCryptoTechnicalAnalysisCourse(),
      _getCryptoRiskManagementCourse(),
      _getCryptoAdvancedStrategiesCourse(),
    ];
  }

  /// Get crypto fundamentals course
  static TradingCourse _getCryptoFundamentalsCourse() {
    return TradingCourse(
      id: 'crypto-fundamentals',
      title: 'Cryptocurrency Fundamentals',
      description: 'Learn the basics of cryptocurrency, blockchain technology, and crypto markets.',
      type: CourseType.crypto,
      difficulty: DifficultyLevel.beginner,
      icon: Icons.currency_bitcoin,
      color: Colors.amber,
      author: '<PERSON>',
      durationMinutes: 135,
      modules: [
        CourseModule(
          id: 'crypto-intro',
          title: 'Introduction to Cryptocurrency',
          description: 'Understand what cryptocurrency is and how blockchain technology works.',
          icon: Icons.school,
          lessons: [
            Lesson(
              id: 'what-is-crypto',
              title: 'What is Cryptocurrency?',
              content: '''
Cryptocurrency is a digital or virtual currency that uses cryptography for security and operates on a technology called blockchain. Unlike traditional currencies issued by governments (fiat currencies), cryptocurrencies are typically decentralized and not controlled by any central authority.

Bitcoin, created in 2009 by an anonymous person or group known as Satoshi Nakamoto, was the first cryptocurrency. Since then, thousands of alternative cryptocurrencies (altcoins) have been created, each with its own features and purposes.

### Key Characteristics of Cryptocurrencies:

1. **Decentralization**: Most cryptocurrencies operate on decentralized networks using blockchain technology, a distributed ledger enforced by a network of computers.

2. **Limited Supply**: Many cryptocurrencies have a capped supply, meaning there's a maximum number of coins that will ever exist. For example, Bitcoin has a maximum supply of 21 million coins.

3. **Transparency**: All transactions are recorded on a public ledger (blockchain) that anyone can view.

4. **Pseudonymity**: While not completely anonymous, cryptocurrency transactions don't require personal identifying information.

5. **Immutability**: Once recorded on the blockchain, transactions cannot be altered or deleted.

6. **Global Accessibility**: Anyone with internet access can use cryptocurrencies, regardless of their location or banking status.

7. **Fast and Cheap Transfers**: Cryptocurrencies can enable quick and low-cost transfers across borders, without the need for intermediaries.

Cryptocurrencies represent a fundamental shift in how we think about money, value transfer, and financial systems. Understanding the basic principles behind them is essential before diving into trading or investing in this emerging asset class.
''',
              contentType: ContentType.text,
              durationMinutes: 10,
            ),
            Lesson(
              id: 'blockchain-technology',
              title: 'Understanding Blockchain Technology',
              content: '''
Blockchain is the underlying technology behind most cryptocurrencies. It's a distributed ledger that records all transactions across a network of computers.

### What is Blockchain?

At its core, a blockchain is a chain of blocks, where each block contains a list of transactions. Once a block is added to the chain, the information it contains becomes extremely difficult to change or remove, creating a permanent and tamper-resistant record.

### How Blockchain Works:

1. **Transaction Initiation**: A user initiates a transaction (e.g., sending Bitcoin to another user).

2. **Transaction Verification**: The transaction is broadcast to a network of computers (nodes) that validate it using known algorithms.

3. **Block Creation**: Verified transactions are grouped together into a block.

4. **Mining/Consensus**: Nodes compete to solve a complex mathematical problem (in Proof of Work systems) or are selected based on their stake (in Proof of Stake systems) to add the new block to the chain.

5. **Block Addition**: Once a node solves the problem or is selected, the new block is added to the blockchain and linked to the previous block using cryptographic hashes.

6. **Transaction Completion**: The transaction is now recorded on the blockchain and is considered complete.

### Key Features of Blockchain:

1. **Decentralization**: No single entity controls the network; it's maintained by distributed nodes.

2. **Transparency**: All transactions are publicly viewable on the blockchain.

3. **Immutability**: Once recorded, data cannot be altered without consensus from the network.

4. **Security**: Cryptographic techniques protect the integrity of the data.

5. **Consensus Mechanisms**: Methods like Proof of Work (PoW) and Proof of Stake (PoS) ensure agreement on the state of the blockchain.

### Types of Blockchains:

1. **Public Blockchains**: Open to anyone (e.g., Bitcoin, Ethereum)
2. **Private Blockchains**: Restricted to authorized participants
3. **Consortium Blockchains**: Controlled by a group of organizations
4. **Hybrid Blockchains**: Combine features of both public and private blockchains

### Beyond Cryptocurrencies:

Blockchain technology has applications beyond cryptocurrencies, including:
- Supply chain management
- Digital identity verification
- Smart contracts
- Voting systems
- Healthcare record management
- Intellectual property protection

Understanding blockchain technology is fundamental to grasping how cryptocurrencies work and their potential impact on various industries.
''',
              contentType: ContentType.text,
              durationMinutes: 15,
            ),
            Lesson(
              id: 'major-cryptocurrencies',
              title: 'Major Cryptocurrencies',
              content: '''
While there are thousands of cryptocurrencies, some have established themselves as major players in the market. Let's explore the most significant cryptocurrencies and their unique features.

### Bitcoin (BTC):
- The first and most valuable cryptocurrency
- Created in 2009 by Satoshi Nakamoto
- Limited supply of 21 million coins
- Uses Proof of Work consensus mechanism
- Often referred to as "digital gold" due to its store of value properties
- Halving events occur approximately every four years, reducing mining rewards

### Ethereum (ETH):
- Launched in 2015 by Vitalik Buterin
- Features smart contracts (self-executing contracts with the terms directly written into code)
- Supports decentralized applications (dApps)
- Transitioned from Proof of Work to Proof of Stake in 2022 (The Merge)
- Has its own programming language (Solidity)
- Supports the creation of other tokens through ERC standards (ERC-20, ERC-721, etc.)

### Binance Coin (BNB):
- Native token of the Binance exchange
- Used for trading fee discounts, payments, and various applications within the Binance ecosystem
- Powers the Binance Smart Chain, a blockchain platform for developing decentralized apps
- Undergoes regular "burns" to reduce supply

### Solana (SOL):
- Known for high throughput and low transaction costs
- Uses a unique combination of Proof of Stake and Proof of History
- Can process thousands of transactions per second
- Popular for NFTs and decentralized finance applications
- Emphasizes scalability and user experience

### Cardano (ADA):
- Founded by Ethereum co-founder Charles Hoskinson
- Focuses on sustainability, scalability, and transparency
- Uses a research-driven approach with peer-reviewed academic research
- Implements a Proof of Stake consensus mechanism called Ouroboros
- Designed with a layered architecture separating computation from settlement

### XRP:
- Created by Ripple Labs
- Designed for cross-border payments and remittances
- Can settle transactions in seconds
- Does not rely on mining
- Used by financial institutions for faster, cheaper international transfers

### Polkadot (DOT):
- Created by Ethereum co-founder Gavin Wood
- Enables different blockchains to transfer messages and value in a trust-free fashion
- Uses a sharded multichain network allowing parallel processing
- Allows custom blockchains to connect with established public blockchains

### Avalanche (AVAX):
- Platform for decentralized applications and custom blockchain networks
- Known for its high speed and low costs
- Uses a novel consensus protocol that is energy-efficient
- Supports the creation of custom blockchains for specific use cases

### Dogecoin (DOGE):
- Started as a meme coin in 2013
- Gained popularity through social media and celebrity endorsements
- Has an unlimited supply, unlike Bitcoin
- Uses Scrypt algorithm and Proof of Work consensus

### Chainlink (LINK):
- Decentralized oracle network connecting smart contracts with real-world data
- Allows blockchains to securely interact with external data feeds
- Essential infrastructure for many DeFi applications
- Solves the "oracle problem" in blockchain technology

Understanding these major cryptocurrencies provides a foundation for navigating the crypto market and identifying potential investment or trading opportunities.
''',
              contentType: ContentType.text,
              durationMinutes: 20,
            ),
            Lesson(
              id: 'crypto-exchanges',
              title: 'Cryptocurrency Exchanges',
              content: '''
Cryptocurrency exchanges are platforms where you can buy, sell, and trade cryptocurrencies. Understanding the different types of exchanges and how they work is essential for anyone looking to enter the crypto market.

### Types of Cryptocurrency Exchanges:

1. **Centralized Exchanges (CEX)**:
   - Operated by companies that facilitate trading
   - Examples: Binance, Coinbase, Kraken, FTX
   - Pros: User-friendly, high liquidity, customer support
   - Cons: Require KYC, control your private keys, potential security risks

2. **Decentralized Exchanges (DEX)**:
   - Operate without a central authority using smart contracts
   - Examples: Uniswap, SushiSwap, dYdX, PancakeSwap
   - Pros: No KYC, maintain control of your keys, privacy
   - Cons: Lower liquidity, more complex interface, higher transaction fees

3. **Hybrid Exchanges**:
   - Combine features of both centralized and decentralized exchanges
   - Examples: Bisq, Nash
   - Pros: Better security than CEXs, more user-friendly than DEXs
   - Cons: Less established, varying features

### Key Features to Consider When Choosing an Exchange:

1. **Security**:
   - Two-factor authentication (2FA)
   - Cold storage for majority of funds
   - Insurance against hacks
   - History of security incidents

2. **Fees**:
   - Trading fees (maker/taker)
   - Deposit and withdrawal fees
   - Currency conversion fees

3. **Liquidity**:
   - Trading volume
   - Bid-ask spread
   - Order book depth

4. **Available Cryptocurrencies**:
   - Number and variety of tradable assets
   - Support for new or niche cryptocurrencies

5. **User Interface**:
   - Ease of use
   - Mobile app availability
   - Advanced trading features

6. **Regulatory Compliance**:
   - Licensed and regulated in reputable jurisdictions
   - Compliance with AML/KYC requirements
   - Transparent company operations

7. **Customer Support**:
   - Response time
   - Support channels (chat, email, phone)
   - Knowledge base and resources

### Common Exchange Features:

1. **Spot Trading**: Buying and selling cryptocurrencies at the current market price.

2. **Limit Orders**: Setting a specific price at which you want to buy or sell.

3. **Market Orders**: Buying or selling immediately at the best available price.

4. **Stop Orders**: Automatically executing a trade when the price reaches a specified level.

5. **Margin Trading**: Trading with borrowed funds to amplify potential returns (and risks).

6. **Futures Trading**: Contracts to buy or sell assets at a predetermined price at a specified time in the future.

7. **Staking**: Earning rewards by holding and "staking" certain cryptocurrencies.

8. **Lending**: Earning interest by lending your cryptocurrencies to other traders.

### Security Best Practices:

1. **Use Strong Authentication**: Enable 2FA using an authenticator app (not SMS).

2. **Withdraw to Private Wallets**: Don't keep large amounts on exchanges.

3. **Use Reputable Exchanges**: Research the exchange's security history and reputation.

4. **Separate Exchange Accounts**: Use different emails and passwords for different exchanges.

5. **Regular Security Audits**: Check your account activity and security settings regularly.

Choosing the right exchange is a crucial step in your cryptocurrency journey. Consider your specific needs, trading goals, and security requirements when making your decision.
''',
              contentType: ContentType.text,
              durationMinutes: 15,
            ),
            Lesson(
              id: 'crypto-wallets',
              title: 'Cryptocurrency Wallets',
              content: '''
Cryptocurrency wallets are tools that allow you to interact with blockchain networks, storing the private keys needed to access and manage your digital assets. Understanding the different types of wallets and their security features is essential for safeguarding your cryptocurrencies.

### What Cryptocurrency Wallets Actually Store:

Contrary to popular belief, wallets don't actually store your cryptocurrencies. Instead, they store:
- **Private Keys**: Secret codes that allow you to spend your cryptocurrencies
- **Public Keys**: Derived from private keys and used to generate your wallet address
- **Addresses**: Where others can send cryptocurrencies to you

Your actual cryptocurrencies exist on the blockchain, and your wallet provides the keys to access and manage them.

### Types of Cryptocurrency Wallets:

1. **Hardware Wallets**:
   - Physical devices that store private keys offline
   - Examples: Ledger Nano X, Trezor Model T, KeepKey
   - Pros: Highest security, immune to computer viruses, supports multiple cryptocurrencies
   - Cons: Cost money, less convenient for frequent trading, can be lost or damaged

2. **Software Wallets**:
   - Applications installed on computers or mobile devices
   - Examples: Exodus, Electrum, MetaMask, Trust Wallet
   - Pros: Free, convenient, user-friendly interfaces
   - Cons: Less secure than hardware wallets, vulnerable to malware

3. **Web Wallets**:
   - Browser-based wallets accessed through websites
   - Examples: Coinbase Wallet, MyEtherWallet, Guarda
   - Pros: Highly convenient, accessible from any device with internet
   - Cons: Security depends on the provider, vulnerable to phishing attacks

4. **Paper Wallets**:
   - Physical documents containing printed private and public keys
   - Pros: Completely offline, immune to hacking
   - Cons: Can be damaged or lost, difficult to use, becoming obsolete

5. **Mobile Wallets**:
   - Apps designed specifically for smartphones
   - Examples: Trust Wallet, Atomic Wallet, BRD
   - Pros: Convenient for on-the-go use, often include QR code scanning
   - Cons: Vulnerable if phone is compromised or lost

6. **Desktop Wallets**:
   - Applications designed for desktop computers
   - Examples: Exodus, Electrum, Atomic Wallet
   - Pros: Full control, better interfaces than mobile wallets
   - Cons: Vulnerable to malware, less convenient than mobile wallets

### Hot vs. Cold Wallets:

- **Hot Wallets**: Connected to the internet (web, most software, and mobile wallets)
  - Good for: Frequent trading, small amounts
  - Risk level: Higher

- **Cold Wallets**: Not connected to the internet (hardware and paper wallets)
  - Good for: Long-term storage, large amounts
  - Risk level: Lower

### Multi-Signature Wallets:

Multi-signature (multisig) wallets require multiple private keys to authorize a transaction, adding an extra layer of security. They're like a safe deposit box that needs two or more keys to open.

### Wallet Security Best Practices:

1. **Backup Your Wallet**: Write down your recovery phrase (seed phrase) and store it in a secure location.

2. **Use Strong Passwords**: Create unique, complex passwords for your wallets.

3. **Enable Two-Factor Authentication**: Add an extra layer of security when available.

4. **Keep Software Updated**: Ensure your wallet software is always up to date.

5. **Use Multiple Wallets**: Separate your funds between "hot" wallets for trading and "cold" wallets for storage.

6. **Verify Addresses**: Always double-check addresses before sending cryptocurrencies.

7. **Be Wary of Phishing**: Only download wallets from official sources and be cautious of emails or messages asking for your keys.

Remember the crypto mantra: "Not your keys, not your coins." If you don't control the private keys to your cryptocurrency (as is the case with many exchange accounts), you don't truly own your digital assets.
''',
              contentType: ContentType.text,
              durationMinutes: 15,
            ),
          ],
        ),
        
        // Additional modules would be added here
      ],
    );
  }

  /// Get crypto technical analysis course
  static TradingCourse _getCryptoTechnicalAnalysisCourse() {
    // Implementation would be similar to the fundamentals course
    return TradingCourse(
      id: 'crypto-technical-analysis',
      title: 'Crypto Technical Analysis',
      description: 'Learn how to analyze cryptocurrency charts using technical indicators and patterns.',
      type: CourseType.crypto,
      difficulty: DifficultyLevel.intermediate,
      icon: Icons.trending_up,
      color: Colors.teal,
      author: 'Jessica Lee',
      durationMinutes: 165,
      modules: [
        // Modules would be added here
      ],
    );
  }

  /// Get crypto risk management course
  static TradingCourse _getCryptoRiskManagementCourse() {
    // Implementation would be similar to the fundamentals course
    return TradingCourse(
      id: 'crypto-risk-management',
      title: 'Crypto Risk Management',
      description: 'Master essential risk management techniques for cryptocurrency trading and investing.',
      type: CourseType.crypto,
      difficulty: DifficultyLevel.intermediate,
      icon: Icons.shield,
      color: Colors.indigo,
      author: 'David Wilson',
      durationMinutes: 140,
      modules: [
        // Modules would be added here
      ],
    );
  }

  /// Get crypto advanced strategies course
  static TradingCourse _getCryptoAdvancedStrategiesCourse() {
    // Implementation would be similar to the fundamentals course
    return TradingCourse(
      id: 'crypto-advanced-strategies',
      title: 'Advanced Crypto Strategies',
      description: 'Explore sophisticated cryptocurrency trading strategies used by professional traders.',
      type: CourseType.crypto,
      difficulty: DifficultyLevel.advanced,
      icon: Icons.psychology,
      color: Colors.deepPurple,
      isPremium: true,
      author: 'Dr. James Rodriguez',
      durationMinutes: 210,
      modules: [
        // Modules would be added here
      ],
    );
  }
}
