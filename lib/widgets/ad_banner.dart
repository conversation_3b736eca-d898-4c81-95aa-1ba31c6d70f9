import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';

/// A widget that displays a banner ad
class AdBanner extends StatefulWidget {
  /// Whether this is a home banner (true) or detail banner (false)
  final bool isHomeBanner;

  /// Creates a new ad banner
  const AdBanner({
    super.key,
    this.isHomeBanner = true,
  });

  @override
  State<AdBanner> createState() => _AdBannerState();
}

class _AdBannerState extends State<AdBanner> {
  /// The banner ad
  BannerAd? _bannerAd;

  /// Whether the ad is loaded
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    _createAndLoadAd();
  }

  /// Get the appropriate ad unit ID based on platform and ad type
  String _getAdUnitId() {
    // Use test ad unit IDs for development
    const Map<String, Map<String, String>> testAdUnitIds = {
      'android': {
        'banner': 'ca-app-pub-3940256099942544/6300978111',
      },
      'ios': {
        'banner': 'ca-app-pub-3940256099942544/2934735716',
      },
    };

    if (kDebugMode) {
      final platform = Platform.isAndroid ? 'android' : 'ios';
      return testAdUnitIds[platform]?['banner'] ?? '';
    }

    // In production, use real ad unit IDs
    // TODO: Replace with real ad unit IDs
    final platform = Platform.isAndroid ? 'android' : 'ios';
    return testAdUnitIds[platform]?['banner'] ?? '';
  }

  /// Create and load a new banner ad
  void _createAndLoadAd() {
    final AdSize adSize = widget.isHomeBanner
        ? AdSize.banner
        : AdSize.mediumRectangle;

    _bannerAd = BannerAd(
      adUnitId: _getAdUnitId(),
      size: adSize,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          debugPrint('Banner ad loaded successfully.');
          if (mounted) {
            setState(() {
              _isAdLoaded = true;
            });
          }
        },
        onAdFailedToLoad: (ad, error) {
          debugPrint('Banner ad failed to load: $error');
          ad.dispose();
          _bannerAd = null;
        },
      ),
    );

    _bannerAd?.load();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isAdLoaded || _bannerAd == null) {
      return SizedBox(
        height: widget.isHomeBanner ? 50 : 250,
        child: const Center(
          child: SizedBox.shrink(),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(
        vertical: AppConstants.paddingSmall,
      ),
      alignment: Alignment.center,
      width: _bannerAd!.size.width.toDouble(),
      height: _bannerAd!.size.height.toDouble(),
      child: AdWidget(ad: _bannerAd!),
    );
  }

  @override
  void dispose() {
    // Dispose the ad when the widget is disposed
    _bannerAd?.dispose();
    super.dispose();
  }
}
