import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';
import 'package:learn_chart_patterns/themes/app_theme.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

/// A widget that displays a candlestick chart
class Candlestick<PERSON>hart extends StatefulWidget {
  /// The candlestick data to display
  final List<CandlestickData> candlesticks;

  /// Support and resistance levels to display
  final List<SupportResistanceLevel>? supportResistanceLevels;

  /// Whether to show volume
  final bool showVolume;

  /// Whether to show tooltips
  final bool showTooltip;

  /// Whether to show the date axis
  final bool showDateAxis;

  /// Whether to show the price axis
  final bool showPriceAxis;

  /// Whether to show grid lines
  final bool showGridLines;

  /// Whether to enable zoom and pan
  final bool enableZoomPan;

  /// Chart colors
  final ChartColors chartColors;

  /// Chart height
  final double height;

  /// Chart aspect ratio
  final double aspectRatio;

  /// Date format for the x-axis
  final String dateFormat;

  /// Creates a new candlestick chart
  const CandlestickChart({
    super.key,
    required this.candlesticks,
    this.supportResistanceLevels,
    this.showVolume = false, // Default to false since volume display is removed
    this.showTooltip = true,
    this.showDateAxis = true,
    this.showPriceAxis = true,
    this.showGridLines = true,
    this.enableZoomPan = true, // Enable zoom and pan by default
    required this.chartColors,
    this.height = AppConstants.defaultChartHeight,
    this.aspectRatio = AppConstants.defaultChartAspectRatio,
    this.dateFormat = 'MM/dd',
  });

  @override
  State<CandlestickChart> createState() => _CandlestickChartState();
}

class _CandlestickChartState extends State<CandlestickChart> {
  /// Zoom pan behavior
  late ZoomPanBehavior _zoomPanBehavior;

  /// Trackball behavior
  late TrackballBehavior _trackballBehavior;

  @override
  void initState() {
    super.initState();

    // Initialize zoom pan behavior
    _zoomPanBehavior = ZoomPanBehavior(
      enablePinching: widget.enableZoomPan,
      enablePanning: widget.enableZoomPan,
      enableDoubleTapZooming: widget.enableZoomPan,
      enableSelectionZooming: widget.enableZoomPan,
      enableMouseWheelZooming: widget.enableZoomPan,
      zoomMode: ZoomMode.x,
      maximumZoomLevel: 0.1, // Allow zooming in more
    );

    // Initialize trackball behavior
    _trackballBehavior = TrackballBehavior(
      enable: widget.showTooltip,
      activationMode: ActivationMode.singleTap,
      tooltipSettings: const InteractiveTooltip(
        enable: true,
        color: Colors.black87,
      ),
      lineType: TrackballLineType.vertical,
      lineColor: Colors.grey,
      lineWidth: 1,
    );
  }

  @override
  void didUpdateWidget(CandlestickChart oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update zoom pan behavior if enableZoomPan changed
    if (oldWidget.enableZoomPan != widget.enableZoomPan) {
      _zoomPanBehavior = ZoomPanBehavior(
        enablePinching: widget.enableZoomPan,
        enablePanning: widget.enableZoomPan,
        enableDoubleTapZooming: widget.enableZoomPan,
        enableSelectionZooming: widget.enableZoomPan,
        enableMouseWheelZooming: widget.enableZoomPan,
        zoomMode: ZoomMode.x,
        maximumZoomLevel: 0.1,
      );
    }

    // Update trackball behavior if showTooltip changed
    if (oldWidget.showTooltip != widget.showTooltip) {
      _trackballBehavior = TrackballBehavior(
        enable: widget.showTooltip,
        activationMode: ActivationMode.singleTap,
        tooltipSettings: const InteractiveTooltip(
          enable: true,
          color: Colors.black87,
        ),
        lineType: TrackballLineType.vertical,
        lineColor: Colors.grey,
        lineWidth: 1,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      width: widget.height * widget.aspectRatio,
      child: SfCartesianChart(
        plotAreaBorderWidth: 0,
        backgroundColor: widget.chartColors.backgroundColor,
        margin: EdgeInsets.zero,
        primaryXAxis: DateTimeAxis(
          isVisible: widget.showDateAxis,
          majorGridLines: MajorGridLines(
            width: widget.showGridLines ? 0.5 : 0,
            color: widget.showGridLines ? widget.chartColors.gridColor : Colors.transparent,
          ),
          labelStyle: TextStyle(
            color: widget.chartColors.labelColor,
            fontSize: AppConstants.fontSizeSmall,
          ),
          dateFormat: DateFormat(widget.dateFormat),
          intervalType: DateTimeIntervalType.days,
          interval: widget.candlesticks.length > 20 ? 5 : 2,
        ),
        primaryYAxis: NumericAxis(
          isVisible: widget.showPriceAxis,
          majorGridLines: MajorGridLines(
            width: widget.showGridLines ? 0.5 : 0,
            color: widget.showGridLines ? widget.chartColors.gridColor : Colors.transparent,
          ),
          labelStyle: TextStyle(
            color: widget.chartColors.labelColor,
            fontSize: AppConstants.fontSizeSmall,
          ),
          numberFormat: NumberFormat.compact(),
        ),
        // No additional axes needed since volume is removed
        tooltipBehavior: widget.showTooltip
            ? TooltipBehavior(
                enable: true,
                header: '',
                canShowMarker: false,
                format: 'Date: point.x\nOpen: \${point.open}\nHigh: \${point.high}\nLow: \${point.low}\nClose: \${point.close}',
                color: Colors.black87,
                textStyle: const TextStyle(
                  color: Colors.white,
                  fontSize: AppConstants.fontSizeSmall,
                ),
              )
            : null,
        trackballBehavior: _trackballBehavior,
        zoomPanBehavior: _zoomPanBehavior,
        series: <CartesianSeries<dynamic, dynamic>>[
          // Candlestick series
          CandleSeries<CandlestickData, DateTime>(
            dataSource: widget.candlesticks,
            xValueMapper: (CandlestickData data, _) => data.date,
            lowValueMapper: (CandlestickData data, _) => data.low,
            highValueMapper: (CandlestickData data, _) => data.high,
            openValueMapper: (CandlestickData data, _) => data.open,
            closeValueMapper: (CandlestickData data, _) => data.close,
            bearColor: widget.chartColors.bearishColor,
            bullColor: widget.chartColors.bullishColor,
            enableSolidCandles: true,
            animationDuration: 0,
            borderWidth: 2.0, // Further increased border width for better visibility
            width: 0.9, // Even wider candles for better visibility
            spacing: 0.1, // Further reduced spacing between candles for clearer pattern
          ),

          // Volume series removed for cleaner chart visualization

          // Support/resistance levels (if provided)
          if (widget.supportResistanceLevels != null)
            ...widget.supportResistanceLevels!
                .where((level) => level.type != LevelType.trendLine)
                // Make sure we have a valid price
                .where((level) => level.price > 0)
                .map((level) => LineSeries<CandlestickData, DateTime>(
                      dataSource: widget.candlesticks,
                      xValueMapper: (CandlestickData data, _) => data.date,
                      yValueMapper: (CandlestickData data, _) => level.price,
                      color: level.color ??
                          (level.type == LevelType.support
                              ? widget.chartColors.supportColor
                              : widget.chartColors.resistanceColor),
                      width: 2.5, // Increased width for better visibility
                      dashArray: null, // Make all lines solid for clarity
                      animationDuration: 0,
                      name: level.label ?? 'Level',
                      // Remove markers to avoid confusion
                      markerSettings: const MarkerSettings(
                        isVisible: false, // Hide markers to avoid confusion with ASCII-like appearance
                      ),
                      // Add data labels to show the level name
                      dataLabelSettings: DataLabelSettings(
                        isVisible: true,
                        alignment: ChartAlignment.far,
                        labelAlignment: ChartDataLabelAlignment.auto,
                        textStyle: TextStyle(
                          color: Colors.white,
                          fontSize: AppConstants.fontSizeMedium, // Increased font size for better readability
                          fontWeight: FontWeight.bold,
                        ),
                        labelPosition: ChartDataLabelPosition.outside,
                        builder: (dynamic data, dynamic point, dynamic series,
                            int pointIndex, int seriesIndex) {
                          // Only show label at the end of the chart
                          if (pointIndex == widget.candlesticks.length - 1) {
                            final Color labelColor = level.color ??
                                (level.type == LevelType.support
                                    ? widget.chartColors.supportColor
                                    : widget.chartColors.resistanceColor);

                            return Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6, // Increased padding
                                vertical: 4, // Increased padding
                              ),
                              decoration: BoxDecoration(
                                color: labelColor.withAlpha(204), // Use the line color with opacity (0.8 * 255 = 204)
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(
                                  color: labelColor,
                                  width: 1.0,
                                ),
                              ),
                              child: Text(
                                level.label ?? 'Level',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: AppConstants.fontSizeMedium, // Increased font size
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            );
                          }
                          return Container(); // Return empty container instead of null
                        },
                      ),
                    )),

          // Trend lines (if provided)
          if (widget.supportResistanceLevels != null)
            ...widget.supportResistanceLevels!
                .where((level) => level.type == LevelType.trendLine)
                // Make sure we have valid start and end dates and prices
                .where((level) => level.startDate != null &&
                                  level.endDate != null &&
                                  level.startPrice != null &&
                                  level.endPrice != null)
                .map((level) {
                  // Create a list of two points for the trend line
                  final List<CandlestickData> trendLinePoints = [
                    CandlestickData(
                      date: level.startDate!,
                      open: level.startPrice!,
                      high: level.startPrice!,
                      low: level.startPrice!,
                      close: level.startPrice!,
                    ),
                    CandlestickData(
                      date: level.endDate!,
                      open: level.endPrice!,
                      high: level.endPrice!,
                      low: level.endPrice!,
                      close: level.endPrice!,
                    ),
                  ];

                  return LineSeries<CandlestickData, DateTime>(
                    dataSource: trendLinePoints,
                    xValueMapper: (CandlestickData data, _) => data.date,
                    yValueMapper: (CandlestickData data, _) => data.close,
                    color: level.color ?? widget.chartColors.trendLineColor,
                    width: 2.5, // Increased width for better visibility
                    animationDuration: 0,
                    name: level.label ?? 'Trend Line',
                    markerSettings: const MarkerSettings(isVisible: false),
                    // Add data label for trend line
                    dataLabelSettings: DataLabelSettings(
                      isVisible: true,
                      alignment: ChartAlignment.far,
                      labelAlignment: ChartDataLabelAlignment.auto,
                      textStyle: const TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeMedium,
                        fontWeight: FontWeight.bold,
                      ),
                      labelPosition: ChartDataLabelPosition.outside,
                      builder: (dynamic data, dynamic point, dynamic series,
                          int pointIndex, int seriesIndex) {
                        // Only show label at the end point
                        if (pointIndex == 1) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: (level.color ?? widget.chartColors.trendLineColor).withAlpha(204),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: level.color ?? widget.chartColors.trendLineColor,
                                width: 1.0,
                              ),
                            ),
                            child: Text(
                              level.label ?? 'Trend Line',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: AppConstants.fontSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        }
                        return Container();
                      },
                    ),
                  );
                }),
        ],
      ),
    );
  }
}
