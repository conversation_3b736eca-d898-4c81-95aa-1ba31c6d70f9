import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/generators/pattern_generator_factory.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/support_resistance_level.dart';
import 'package:learn_chart_patterns/themes/app_theme.dart';
import 'package:learn_chart_patterns/widgets/candlestick_chart.dart';

/// A card that displays a chart pattern
class PatternCard extends StatefulWidget {
  /// The chart pattern to display
  final ChartPattern pattern;

  /// Whether the pattern has been learned
  final bool isLearned;

  /// The accuracy percentage for this pattern (0.0-1.0)
  final double accuracy;

  /// Chart colors
  final ChartColors chartColors;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Whether to show the chart
  final bool showChart;

  /// Whether to show the accuracy
  final bool showAccuracy;

  /// Creates a new pattern card
  const PatternCard({
    super.key,
    required this.pattern,
    this.isLearned = false,
    this.accuracy = 0.0,
    required this.chartColors,
    this.onTap,
    this.showChart = true,
    this.showAccuracy = true,
  });

  @override
  State<PatternCard> createState() => _PatternCardState();
}

class _PatternCardState extends State<PatternCard> {
  /// The candlestick data for the pattern
  late final candlesticks = PatternGeneratorFactory.createGenerator(
    patternId: widget.pattern.id,
    seed: widget.pattern.id.hashCode,
  ).generateCandlesticks(length: 20);

  /// The support/resistance levels for the pattern
  late final supportResistanceLevels = PatternGeneratorFactory.createGenerator(
    patternId: widget.pattern.id,
    seed: widget.pattern.id.hashCode,
  ).generateSupportResistanceLevels(candlesticks);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      color: Colors.white,
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Pattern icon and learned status
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingSmall),
                    decoration: BoxDecoration(
                      color: (widget.pattern.color ?? theme.colorScheme.primary).withAlpha(20),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                    ),
                    child: Icon(
                      widget.pattern.icon ?? Icons.show_chart,
                      color: widget.pattern.color ?? theme.colorScheme.primary,
                      size: AppConstants.iconSizeMedium,
                    ),
                  ),
                  const Spacer(),
                  if (widget.isLearned)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingMedium,
                        vertical: AppConstants.paddingSmall / 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha(20),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: AppConstants.iconSizeSmall,
                          ),
                          const SizedBox(width: AppConstants.spacingSmall),
                          Text(
                            'Learned',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),

              const SizedBox(height: AppConstants.spacingMedium),

              // Pattern name
              Text(
                widget.pattern.name,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: AppConstants.spacingSmall),

              // Pattern trend and difficulty
              Row(
                children: [
                  _buildTrendChip(context),
                  const SizedBox(width: AppConstants.spacingMedium),
                  _buildDifficultyChip(context),
                ],
              ),

              const SizedBox(height: AppConstants.spacingMedium),

              // Pattern description
              Text(
                widget.pattern.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(180),
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              if (widget.showChart) ...[
                const SizedBox(height: AppConstants.spacingLarge),

                // Pattern chart
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                    border: Border.all(
                      color: theme.colorScheme.primary.withAlpha(30),
                      width: 1,
                    ),
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: CandlestickChart(
                    candlesticks: candlesticks,
                    supportResistanceLevels: supportResistanceLevels,
                    chartColors: widget.chartColors,
                    showVolume: false,
                    showTooltip: false,
                    showDateAxis: false,
                    showPriceAxis: false,
                    showGridLines: false,
                    height: AppConstants.defaultChartHeight * 0.6,
                  ),
                ),
              ],

              if (widget.showAccuracy && widget.accuracy > 0) ...[
                const SizedBox(height: AppConstants.spacingMedium),

                // Accuracy indicator
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Accuracy',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(180),
                      ),
                    ),
                    const SizedBox(height: AppConstants.spacingSmall),
                    Row(
                      children: [
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                            child: LinearProgressIndicator(
                              value: widget.accuracy,
                              backgroundColor: _getAccuracyColor(widget.accuracy).withAlpha(30),
                              color: _getAccuracyColor(widget.accuracy),
                              minHeight: 6,
                            ),
                          ),
                        ),
                        const SizedBox(width: AppConstants.spacingMedium),
                        Text(
                          '${(widget.accuracy * 100).toInt()}%',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _getAccuracyColor(widget.accuracy),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a chip for the pattern trend
  Widget _buildTrendChip(BuildContext context) {
    Color chipColor;
    String trendText;

    switch (widget.pattern.trend) {
      case PatternTrend.bullish:
        chipColor = const Color(0xFF4CAF50); // Green
        trendText = 'Bullish';
        break;
      case PatternTrend.bearish:
        chipColor = const Color(0xFFFF6B6B); // Coral
        trendText = 'Bearish';
        break;
      case PatternTrend.neutral:
        chipColor = const Color(0xFF4A6FFF); // Blue
        trendText = 'Neutral';
        break;
      case PatternTrend.reversal:
        chipColor = const Color(0xFF9C27B0); // Purple
        trendText = 'Reversal';
        break;
      case PatternTrend.continuation:
        chipColor = const Color(0xFFFF9800); // Orange
        trendText = 'Continuation';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall / 2,
      ),
      decoration: BoxDecoration(
        color: chipColor.withAlpha(20),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Text(
        trendText,
        style: TextStyle(
          color: chipColor,
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Builds a chip for the pattern difficulty
  Widget _buildDifficultyChip(BuildContext context) {
    Color chipColor;
    String difficultyText;

    switch (widget.pattern.difficulty) {
      case PatternDifficulty.beginner:
        chipColor = const Color(0xFF4CAF50); // Green
        difficultyText = 'Beginner';
        break;
      case PatternDifficulty.intermediate:
        chipColor = const Color(0xFFFFA726); // Orange
        difficultyText = 'Intermediate';
        break;
      case PatternDifficulty.advanced:
        chipColor = const Color(0xFFFF6B6B); // Coral
        difficultyText = 'Advanced';
        break;
      case null:
        // For premium patterns that might not have difficulty set
        chipColor = widget.pattern.color ?? const Color(0xFF9C27B0); // Purple default
        difficultyText = widget.pattern.complexity != null
            ? 'Complexity: ${widget.pattern.complexity}/5'
            : 'Premium';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall / 2,
      ),
      decoration: BoxDecoration(
        color: chipColor.withAlpha(20),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Text(
        difficultyText,
        style: TextStyle(
          color: chipColor,
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Gets the color for the accuracy indicator
  Color _getAccuracyColor(double accuracy) {
    if (accuracy >= 0.8) {
      return const Color(0xFF4CAF50); // Green
    } else if (accuracy >= 0.6) {
      return const Color(0xFFFFA726); // Orange
    } else {
      return const Color(0xFFFF6B6B); // Coral
    }
  }
}
