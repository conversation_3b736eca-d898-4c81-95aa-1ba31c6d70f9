import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/enhanced_support_resistance_level.dart';
import 'package:learn_chart_patterns/models/pattern_annotation.dart';
import 'package:learn_chart_patterns/models/trend_line.dart';
import 'package:learn_chart_patterns/themes/app_theme.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

/// A widget that displays an enhanced candlestick chart with pattern annotations
class EnhancedCandlestickChart extends StatefulWidget {
  /// The candlestick data to display
  final List<CandlestickData> candlesticks;

  /// Support and resistance levels to display
  final List<EnhancedSupportResistanceLevel>? supportResistanceLevels;

  /// Trend lines to display
  final List<TrendLine>? trendLines;

  /// Pattern annotations to display
  final List<PatternAnnotation>? patternAnnotations;

  /// The pattern being displayed (if any)
  final ChartPattern? pattern;

  /// Whether to show volume
  final bool showVolume;

  /// Whether to show tooltips
  final bool showTooltip;

  /// Whether to show the date axis
  final bool showDateAxis;

  /// Whether to show the price axis
  final bool showPriceAxis;

  /// Whether to show grid lines
  final bool showGridLines;

  /// Whether to show pattern annotations
  final bool showPatternAnnotations;

  /// Whether to enable interactive features
  final bool interactive;

  /// Whether to enable haptic feedback
  final bool enableHapticFeedback;

  /// Chart colors
  final ChartColors chartColors;

  /// Chart height
  final double height;

  /// Chart aspect ratio
  final double aspectRatio;

  /// Date format for the x-axis
  final String dateFormat;

  /// Callback when a candlestick is tapped
  final Function(CandlestickData)? onCandlestickTap;

  /// Creates a new enhanced candlestick chart
  const EnhancedCandlestickChart({
    super.key,
    required this.candlesticks,
    this.supportResistanceLevels,
    this.trendLines,
    this.patternAnnotations,
    this.pattern,
    this.showVolume = true,
    this.showTooltip = true,
    this.showDateAxis = true,
    this.showPriceAxis = true,
    this.showGridLines = true,
    this.showPatternAnnotations = true,
    this.interactive = true,
    this.enableHapticFeedback = true,
    required this.chartColors,
    this.height = AppConstants.defaultChartHeight,
    this.aspectRatio = AppConstants.defaultChartAspectRatio,
    this.dateFormat = 'MM/dd',
    this.onCandlestickTap,
  });

  @override
  State<EnhancedCandlestickChart> createState() => _EnhancedCandlestickChartState();
}

class _EnhancedCandlestickChartState extends State<EnhancedCandlestickChart> with SingleTickerProviderStateMixin {
  /// Animation controller for chart animations
  late AnimationController _animationController;

  /// Animation for chart elements
  late Animation<double> _animation;

  /// Selected candlestick index
  int? _selectedCandlestickIndex;

  /// Trackball behavior
  late TrackballBehavior _trackballBehavior;

  /// Zoom pan behavior
  late ZoomPanBehavior _zoomPanBehavior;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Initialize animation
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Initialize trackball behavior
    _trackballBehavior = TrackballBehavior(
      enable: widget.showTooltip && widget.interactive,
      activationMode: ActivationMode.singleTap,
      tooltipSettings: const InteractiveTooltip(
        enable: true,
        color: Colors.black87,
      ),
      lineType: TrackballLineType.vertical,
      lineColor: widget.chartColors.gridColor,
      lineWidth: 1,
      tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
    );

    // Initialize zoom pan behavior
    _zoomPanBehavior = ZoomPanBehavior(
      enablePinching: widget.interactive,
      enablePanning: widget.interactive,
      enableDoubleTapZooming: widget.interactive,
      zoomMode: ZoomMode.x,
    );

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      width: widget.height * widget.aspectRatio,
      child: Stack(
        children: [
          // Main chart
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return SfCartesianChart(
                plotAreaBorderWidth: 0,
                backgroundColor: widget.chartColors.backgroundColor,
                margin: EdgeInsets.zero,
                primaryXAxis: DateTimeAxis(
                  isVisible: widget.showDateAxis,
                  majorGridLines: MajorGridLines(
                    width: widget.showGridLines ? 0.5 : 0,
                    color: widget.showGridLines ? widget.chartColors.gridColor : Colors.transparent,
                  ),
                  labelStyle: TextStyle(
                    color: widget.chartColors.labelColor,
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                  dateFormat: DateFormat(widget.dateFormat),
                  intervalType: DateTimeIntervalType.days,
                  interval: widget.candlesticks.length > 20 ? 5 : 2,
                ),
                primaryYAxis: NumericAxis(
                  isVisible: widget.showPriceAxis,
                  majorGridLines: MajorGridLines(
                    width: widget.showGridLines ? 0.5 : 0,
                    color: widget.showGridLines ? widget.chartColors.gridColor : Colors.transparent,
                  ),
                  labelStyle: TextStyle(
                    color: widget.chartColors.labelColor,
                    fontSize: AppConstants.fontSizeSmall,
                  ),
                  numberFormat: NumberFormat.compact(),
                  labelFormat: '{value}',
                ),
                axes: widget.showVolume && widget.candlesticks.first.volume != null
                    ? [
                        NumericAxis(
                          name: 'volume',
                          isVisible: false,
                          opposedPosition: true,
                        ),
                      ]
                    : [],
                tooltipBehavior: widget.showTooltip && !widget.interactive
                    ? TooltipBehavior(
                        enable: true,
                        header: '',
                        canShowMarker: false,
                        format: 'point.x\nO: \${point.open}\nH: \${point.high}\nL: \${point.low}\nC: \${point.close}',
                        color: Colors.black87,
                        textStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: AppConstants.fontSizeSmall,
                        ),
                      )
                    : null,
                trackballBehavior: _trackballBehavior,
                zoomPanBehavior: _zoomPanBehavior,
                series: <CartesianSeries<dynamic, dynamic>>[
                  // Candlestick series
                  CandleSeries<CandlestickData, DateTime>(
                    animationDuration: 0,
                    dataSource: widget.candlesticks,
                    xValueMapper: (CandlestickData data, _) => data.date,
                    lowValueMapper: (CandlestickData data, _) => data.low,
                    highValueMapper: (CandlestickData data, _) => data.high,
                    openValueMapper: (CandlestickData data, _) => data.open,
                    closeValueMapper: (CandlestickData data, _) => data.close,
                    bearColor: widget.chartColors.bearishColor,
                    bullColor: widget.chartColors.bullishColor,
                    enableSolidCandles: true,
                    opacity: _animation.value,
                    borderWidth: 1,
                    onPointTap: (ChartPointDetails details) {
                      if (widget.onCandlestickTap != null && details.pointIndex != null) {
                        setState(() {
                          _selectedCandlestickIndex = details.pointIndex;
                        });

                        // Provide haptic feedback
                        if (widget.enableHapticFeedback) {
                          HapticFeedback.lightImpact();
                        }

                        widget.onCandlestickTap!(widget.candlesticks[details.pointIndex!]);
                      }
                    },
                  ),

                  // Volume series (if enabled)
                  if (widget.showVolume && widget.candlesticks.first.volume != null)
                    ColumnSeries<CandlestickData, DateTime>(
                      animationDuration: 0,
                      dataSource: widget.candlesticks,
                      xValueMapper: (CandlestickData data, _) => data.date,
                      yValueMapper: (CandlestickData data, _) => data.volume,
                      yAxisName: 'volume',
                      color: widget.chartColors.volumeColor.withAlpha(75),
                      width: 0.8,
                      opacity: _animation.value * 0.7,
                    ),

                  // Support and resistance levels
                  if (widget.supportResistanceLevels != null)
                    ...widget.supportResistanceLevels!.map((level) => LineSeries<CandlestickData, DateTime>(
                      animationDuration: 0,
                      dataSource: widget.candlesticks,
                      xValueMapper: (CandlestickData data, _) => data.date,
                      yValueMapper: (CandlestickData data, _) => level.price,
                      color: level.isSupport
                          ? widget.chartColors.supportColor
                          : widget.chartColors.resistanceColor,
                      width: 1.5,
                      opacity: _animation.value * 0.8,
                      dashArray: level.isStrong ? null : [5, 5],
                    )),

                  // Trend lines
                  if (widget.trendLines != null)
                    ...widget.trendLines!.map((line) => LineSeries<TrendLinePoint, DateTime>(
                      animationDuration: 0,
                      dataSource: line.points,
                      xValueMapper: (TrendLinePoint point, _) => point.date,
                      yValueMapper: (TrendLinePoint point, _) => point.price,
                      color: line.isBullish
                          ? widget.chartColors.bullishColor
                          : widget.chartColors.bearishColor,
                      width: 2,
                      opacity: _animation.value,
                      dashArray: line.isDashed ? [5, 5] : null,
                    )),
                ],
                annotations: widget.showPatternAnnotations && widget.patternAnnotations != null
                    ? <CartesianChartAnnotation>[
                        ...widget.patternAnnotations!.map((annotation) => CartesianChartAnnotation(
                          widget: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: annotation.color.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: annotation.color,
                                width: 1,
                              ),
                            ),
                            child: Text(
                              annotation.text,
                              style: TextStyle(
                                color: annotation.color,
                                fontSize: AppConstants.fontSizeSmall,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          coordinateUnit: CoordinateUnit.point,
                          x: annotation.date,
                          y: annotation.price,
                        )),
                      ]
                    : null,
              );
            },
          ),

          // Pattern name overlay (if pattern is provided)
          if (widget.pattern != null)
            Positioned(
              top: 8,
              left: 8,
              child: AnimatedOpacity(
                opacity: _animation.value,
                duration: const Duration(milliseconds: 500),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: widget.pattern!.color?.withOpacity(0.2) ??
                           (widget.pattern!.trend == PatternTrend.bullish
                               ? Colors.green.withOpacity(0.2)
                               : Colors.red.withOpacity(0.2)),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: widget.pattern!.color ??
                             (widget.pattern!.trend == PatternTrend.bullish
                                 ? Colors.green
                                 : Colors.red),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    widget.pattern!.name,
                    style: TextStyle(
                      color: widget.pattern!.color ??
                             (widget.pattern!.trend == PatternTrend.bullish
                                 ? Colors.green
                                 : Colors.red),
                      fontSize: AppConstants.fontSizeSmall,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
