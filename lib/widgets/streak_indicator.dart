import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';

/// A widget that displays a streak indicator
class StreakIndicator extends StatelessWidget {
  /// The current streak
  final int streak;
  
  /// The highest streak
  final int highestStreak;
  
  /// Whether to show the highest streak
  final bool showHighestStreak;
  
  /// Whether to show the fire emoji
  final bool showFireEmoji;
  
  /// Creates a new streak indicator
  const StreakIndicator({
    super.key,
    required this.streak,
    this.highestStreak = 0,
    this.showHighestStreak = true,
    this.showFireEmoji = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: AppConstants.elevationSmall,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Streak icon
            Icon(
              Icons.local_fire_department,
              color: _getStreakColor(streak),
              size: AppConstants.iconSizeLarge,
            ),
            
            const SizedBox(width: AppConstants.spacingMedium),
            
            // Streak text
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Current streak
                Row(
                  children: [
                    Text(
                      'Current Streak:',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(width: AppConstants.spacingSmall),
                    Text(
                      '$streak ${_getStreakEmoji(streak)}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getStreakColor(streak),
                      ),
                    ),
                  ],
                ),
                
                // Highest streak
                if (showHighestStreak && highestStreak > 0) ...[
                  const SizedBox(height: AppConstants.spacingSmall),
                  Row(
                    children: [
                      Text(
                        'Highest Streak:',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(width: AppConstants.spacingSmall),
                      Text(
                        '$highestStreak ${_getStreakEmoji(highestStreak)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _getStreakColor(highestStreak),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Gets the color for the streak
  Color _getStreakColor(int streakValue) {
    if (streakValue >= 10) {
      return Colors.red;
    } else if (streakValue >= 7) {
      return Colors.orange;
    } else if (streakValue >= 3) {
      return Colors.amber;
    } else if (streakValue > 0) {
      return Colors.green;
    } else {
      return Colors.grey;
    }
  }

  /// Gets the emoji for the streak
  String _getStreakEmoji(int streakValue) {
    if (!showFireEmoji) return '';
    
    if (streakValue >= 10) {
      return '🔥🔥🔥';
    } else if (streakValue >= 7) {
      return '🔥🔥';
    } else if (streakValue >= AppConstants.minStreakForFire) {
      return '🔥';
    } else {
      return '';
    }
  }
}
