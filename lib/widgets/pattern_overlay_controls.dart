import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/enhanced_pattern_annotation.dart';

/// Widget for controlling pattern overlay visibility
class PatternOverlayControls extends StatefulWidget {
  /// Current overlay configuration
  final PatternOverlayConfig config;
  
  /// Callback when configuration changes
  final ValueChanged<PatternOverlayConfig> onConfigChanged;
  
  /// Whether to show in compact mode
  final bool compact;

  /// Creates a new pattern overlay controls widget
  const PatternOverlayControls({
    super.key,
    required this.config,
    required this.onConfigChanged,
    this.compact = false,
  });

  @override
  State<PatternOverlayControls> createState() => _PatternOverlayControlsState();
}

class _PatternOverlayControlsState extends State<PatternOverlayControls> {
  late PatternOverlayConfig _config;

  @override
  void initState() {
    super.initState();
    _config = widget.config;
  }

  @override
  void didUpdateWidget(PatternOverlayControls oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config != widget.config) {
      _config = widget.config;
    }
  }

  void _updateConfig(PatternOverlayConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (widget.compact) {
      return _buildCompactControls(theme);
    } else {
      return _buildFullControls(theme);
    }
  }

  /// Builds compact controls for mobile/small screens
  Widget _buildCompactControls(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingSmall),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(50),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.visibility,
                size: AppConstants.iconSizeSmall,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: AppConstants.spacingSmall),
              Text(
                'Pattern Overlays',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              // Quick presets
              _buildPresetButton(theme, 'Beginner', PatternOverlayConfig.beginner()),
              const SizedBox(width: AppConstants.spacingSmall),
              _buildPresetButton(theme, 'Clean', PatternOverlayConfig.clean()),
            ],
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Toggle switches in a grid
          Wrap(
            spacing: AppConstants.spacingMedium,
            runSpacing: AppConstants.spacingSmall,
            children: [
              _buildCompactToggle(
                theme,
                'Support/Resistance',
                Icons.trending_flat,
                _config.showSupportResistance,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: value,
                  showTrendLines: _config.showTrendLines,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: _config.showKeyPoints,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: _config.showEducationalNotes,
                  opacity: _config.opacity,
                )),
              ),
              _buildCompactToggle(
                theme,
                'Trend Lines',
                Icons.show_chart,
                _config.showTrendLines,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: _config.showSupportResistance,
                  showTrendLines: value,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: _config.showKeyPoints,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: _config.showEducationalNotes,
                  opacity: _config.opacity,
                )),
              ),
              _buildCompactToggle(
                theme,
                'Key Points',
                Icons.star,
                _config.showKeyPoints,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: _config.showSupportResistance,
                  showTrendLines: _config.showTrendLines,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: value,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: _config.showEducationalNotes,
                  opacity: _config.opacity,
                )),
              ),
              _buildCompactToggle(
                theme,
                'Notes',
                Icons.school,
                _config.showEducationalNotes,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: _config.showSupportResistance,
                  showTrendLines: _config.showTrendLines,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: _config.showKeyPoints,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: value,
                  opacity: _config.opacity,
                )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds full controls for desktop/large screens
  Widget _buildFullControls(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(50),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with presets
          Row(
            children: [
              Icon(
                Icons.visibility,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: AppConstants.spacingMedium),
              Text(
                'Pattern Overlay Controls',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              _buildPresetButton(theme, 'Beginner', PatternOverlayConfig.beginner()),
              const SizedBox(width: AppConstants.spacingSmall),
              _buildPresetButton(theme, 'Advanced', PatternOverlayConfig.advanced()),
              const SizedBox(width: AppConstants.spacingSmall),
              _buildPresetButton(theme, 'Clean', PatternOverlayConfig.clean()),
            ],
          ),
          
          const SizedBox(height: AppConstants.spacingLarge),
          
          // Control sections
          _buildControlSection(
            theme,
            'Chart Elements',
            [
              _buildFullToggle(
                theme,
                'Support & Resistance Lines',
                'Show horizontal support and resistance levels',
                Icons.trending_flat,
                _config.showSupportResistance,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: value,
                  showTrendLines: _config.showTrendLines,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: _config.showKeyPoints,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: _config.showEducationalNotes,
                  opacity: _config.opacity,
                )),
              ),
              _buildFullToggle(
                theme,
                'Trend Lines',
                'Show diagonal trend lines and channels',
                Icons.show_chart,
                _config.showTrendLines,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: _config.showSupportResistance,
                  showTrendLines: value,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: _config.showKeyPoints,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: _config.showEducationalNotes,
                  opacity: _config.opacity,
                )),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          _buildControlSection(
            theme,
            'Educational Features',
            [
              _buildFullToggle(
                theme,
                'Key Points',
                'Highlight important pattern formation points',
                Icons.star,
                _config.showKeyPoints,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: _config.showSupportResistance,
                  showTrendLines: _config.showTrendLines,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: value,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: _config.showEducationalNotes,
                  opacity: _config.opacity,
                )),
              ),
              _buildFullToggle(
                theme,
                'Educational Notes',
                'Show explanatory notes about the pattern',
                Icons.school,
                _config.showEducationalNotes,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: _config.showSupportResistance,
                  showTrendLines: _config.showTrendLines,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: _config.showKeyPoints,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: value,
                  opacity: _config.opacity,
                )),
              ),
              _buildFullToggle(
                theme,
                'Trading Points',
                'Show entry, stop loss, and target levels',
                Icons.trending_up,
                _config.showTradingPoints,
                (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: _config.showSupportResistance,
                  showTrendLines: _config.showTrendLines,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: _config.showKeyPoints,
                  showTradingPoints: value,
                  showEducationalNotes: _config.showEducationalNotes,
                  opacity: _config.opacity,
                )),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Opacity slider
          _buildOpacitySlider(theme),
        ],
      ),
    );
  }

  Widget _buildPresetButton(ThemeData theme, String label, PatternOverlayConfig config) {
    return OutlinedButton(
      onPressed: () => _updateConfig(config),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingSmall,
          vertical: AppConstants.paddingSmall / 2,
        ),
      ),
      child: Text(
        label,
        style: theme.textTheme.bodySmall,
      ),
    );
  }

  Widget _buildCompactToggle(
    ThemeData theme,
    String label,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return InkWell(
      onTap: () => onChanged(!value),
      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingSmall,
          vertical: AppConstants.paddingSmall / 2,
        ),
        decoration: BoxDecoration(
          color: value ? theme.colorScheme.primary.withAlpha(30) : null,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          border: Border.all(
            color: value ? theme.colorScheme.primary : theme.colorScheme.outline.withAlpha(100),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: AppConstants.iconSizeSmall,
              color: value ? theme.colorScheme.primary : theme.colorScheme.onSurface.withAlpha(150),
            ),
            const SizedBox(width: AppConstants.spacingSmall / 2),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: value ? theme.colorScheme.primary : theme.colorScheme.onSurface.withAlpha(150),
                fontWeight: value ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlSection(ThemeData theme, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: AppConstants.spacingSmall),
        ...children,
      ],
    );
  }

  Widget _buildFullToggle(
    ThemeData theme,
    String title,
    String description,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacingSmall),
      child: Row(
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurface.withAlpha(150),
          ),
          const SizedBox(width: AppConstants.spacingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(150),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildOpacitySlider(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Overlay Opacity',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: AppConstants.spacingSmall),
        Row(
          children: [
            Icon(
              Icons.opacity,
              color: theme.colorScheme.onSurface.withAlpha(150),
            ),
            const SizedBox(width: AppConstants.spacingMedium),
            Expanded(
              child: Slider(
                value: _config.opacity,
                min: 0.3,
                max: 1.0,
                divisions: 7,
                label: '${(_config.opacity * 100).round()}%',
                onChanged: (value) => _updateConfig(PatternOverlayConfig(
                  showSupportResistance: _config.showSupportResistance,
                  showTrendLines: _config.showTrendLines,
                  showAnnotations: _config.showAnnotations,
                  showKeyPoints: _config.showKeyPoints,
                  showTradingPoints: _config.showTradingPoints,
                  showEducationalNotes: _config.showEducationalNotes,
                  opacity: value,
                )),
              ),
            ),
            Text(
              '${(_config.opacity * 100).round()}%',
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }
}
