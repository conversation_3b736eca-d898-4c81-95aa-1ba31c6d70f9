import 'package:flutter/material.dart';

/// A custom app icon widget for the Chart Patterns Trainer app
class AppIcon extends StatelessWidget {
  /// The size of the icon
  final double size;

  /// Creates a new app icon
  const AppIcon({
    super.key,
    this.size = 100,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF4A6FFF), // Blue
            Color(0xFF9C27B0), // Purple
          ],
        ),
        borderRadius: BorderRadius.circular(size * 0.2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: size * 0.1,
            offset: Offset(0, size * 0.05),
          ),
        ],
      ),
      child: Center(
        child: CustomPaint(
          size: Size(size * 0.7, size * 0.5),
          painter: ChartPatternPainter(),
        ),
      ),
    );
  }
}

/// Custom painter for drawing a candlestick chart pattern
class ChartPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = size.width * 0.04
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    // Draw a simplified head and shoulders pattern
    final path = Path();
    
    // Starting point
    path.moveTo(0, size.height * 0.7);
    
    // Left shoulder
    path.lineTo(size.width * 0.2, size.height * 0.4);
    
    // Head
    path.lineTo(size.width * 0.4, size.height * 0.7);
    path.lineTo(size.width * 0.6, size.height * 0.2);
    path.lineTo(size.width * 0.8, size.height * 0.7);
    
    // Right shoulder
    path.lineTo(size.width, size.height * 0.5);
    
    canvas.drawPath(path, paint);
    
    // Draw some candlesticks
    _drawCandlestick(canvas, size, 0.15, 0.55, 0.65, true);
    _drawCandlestick(canvas, size, 0.35, 0.35, 0.45, false);
    _drawCandlestick(canvas, size, 0.55, 0.25, 0.35, true);
    _drawCandlestick(canvas, size, 0.75, 0.45, 0.55, false);
  }
  
  /// Draws a single candlestick
  void _drawCandlestick(Canvas canvas, Size size, double x, double openY, double closeY, bool bullish) {
    final candleWidth = size.width * 0.06;
    final wickPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = size.width * 0.01
      ..strokeCap = StrokeCap.round;
    
    final bodyPaint = Paint()
      ..color = Colors.white
      ..style = bullish ? PaintingStyle.stroke : PaintingStyle.fill;
    
    // Draw wick
    canvas.drawLine(
      Offset(x * size.width, size.height * 0.2),
      Offset(x * size.width, size.height * 0.8),
      wickPaint,
    );
    
    // Draw body
    final bodyRect = Rect.fromLTRB(
      (x * size.width) - (candleWidth / 2),
      size.height * openY,
      (x * size.width) + (candleWidth / 2),
      size.height * closeY,
    );
    
    canvas.drawRect(bodyRect, bodyPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
