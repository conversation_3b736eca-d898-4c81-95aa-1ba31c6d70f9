import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/comprehensive_trading_education.dart';

/// Widget for displaying a trading course card
class CourseCard extends StatelessWidget {
  /// The course to display
  final TradingEducationCourse course;
  
  /// Callback when the course is tapped
  final VoidCallback? onTap;
  
  /// Whether to show the premium badge
  final bool showPremiumBadge;
  
  /// Whether the course is completed
  final bool isCompleted;
  
  /// Progress percentage (0-1)
  final double progress;

  /// Creates a new course card
  const CourseCard({
    super.key,
    required this.course,
    this.onTap,
    this.showPremiumBadge = true,
    this.isCompleted = false,
    this.progress = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and badges
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Course icon
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingSmall),
                    decoration: BoxDecoration(
                      color: _getMarketTypeColor().withAlpha(30),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                    child: Icon(
                      _getMarketTypeIcon(),
                      color: _getMarketTypeColor(),
                      size: AppConstants.iconSizeMedium,
                    ),
                  ),
                  
                  const SizedBox(width: AppConstants.spacingMedium),
                  
                  // Title and description
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          course.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: AppConstants.spacingSmall),
                        Text(
                          course.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(180),
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  // Premium badge
                  if (showPremiumBadge && course.isPremium)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingSmall,
                        vertical: AppConstants.paddingSmall / 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.star,
                            size: 12,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            'PRO',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: AppConstants.spacingLarge),
              
              // Course metadata
              Row(
                children: [
                  // Difficulty
                  _buildMetadataChip(
                    theme,
                    icon: Icons.signal_cellular_alt,
                    label: _getDifficultyText(),
                    color: _getDifficultyColor(),
                  ),
                  
                  const SizedBox(width: AppConstants.spacingMedium),
                  
                  // Duration
                  _buildMetadataChip(
                    theme,
                    icon: Icons.access_time,
                    label: '${course.estimatedHours}h',
                    color: theme.colorScheme.primary,
                  ),
                  
                  const SizedBox(width: AppConstants.spacingMedium),
                  
                  // Rating
                  if (course.rating > 0)
                    _buildMetadataChip(
                      theme,
                      icon: Icons.star,
                      label: course.rating.toStringAsFixed(1),
                      color: Colors.amber,
                    ),
                ],
              ),
              
              const SizedBox(height: AppConstants.spacingLarge),
              
              // Progress bar (if started)
              if (progress > 0) ...[
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: progress,
                        backgroundColor: theme.colorScheme.outline.withAlpha(50),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          isCompleted ? Colors.green : theme.colorScheme.primary,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.spacingMedium),
                    Text(
                      isCompleted ? 'Completed' : '${(progress * 100).round()}%',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isCompleted ? Colors.green : theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.spacingMedium),
              ],
              
              // Action button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onTap,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isCompleted 
                        ? Colors.green 
                        : (progress > 0 ? theme.colorScheme.secondary : theme.colorScheme.primary),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    isCompleted 
                        ? 'Review Course'
                        : (progress > 0 ? 'Continue Learning' : 'Start Course'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a metadata chip
  Widget _buildMetadataChip(
    ThemeData theme, {
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall / 2,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: AppConstants.iconSizeSmall,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Gets the market type icon
  IconData _getMarketTypeIcon() {
    switch (course.marketType) {
      case TradingMarketType.forex:
        return Icons.currency_exchange;
      case TradingMarketType.crypto:
        return Icons.currency_bitcoin;
      case TradingMarketType.stocks:
        return Icons.trending_up;
      case TradingMarketType.commodities:
        return Icons.oil_barrel;
      case TradingMarketType.general:
        return Icons.school;
    }
  }

  /// Gets the market type color
  Color _getMarketTypeColor() {
    switch (course.marketType) {
      case TradingMarketType.forex:
        return Colors.blue;
      case TradingMarketType.crypto:
        return Colors.orange;
      case TradingMarketType.stocks:
        return Colors.green;
      case TradingMarketType.commodities:
        return Colors.brown;
      case TradingMarketType.general:
        return Colors.purple;
    }
  }

  /// Gets the difficulty text
  String _getDifficultyText() {
    switch (course.difficulty) {
      case CourseDifficulty.beginner:
        return 'Beginner';
      case CourseDifficulty.intermediate:
        return 'Intermediate';
      case CourseDifficulty.advanced:
        return 'Advanced';
      case CourseDifficulty.expert:
        return 'Expert';
    }
  }

  /// Gets the difficulty color
  Color _getDifficultyColor() {
    switch (course.difficulty) {
      case CourseDifficulty.beginner:
        return Colors.green;
      case CourseDifficulty.intermediate:
        return Colors.orange;
      case CourseDifficulty.advanced:
        return Colors.red;
      case CourseDifficulty.expert:
        return Colors.purple;
    }
  }
}
