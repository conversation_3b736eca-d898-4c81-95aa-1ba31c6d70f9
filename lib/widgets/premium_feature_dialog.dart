import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';

/// Dialog for prompting users to upgrade to premium
class PremiumFeatureDialog extends StatelessWidget {
  /// The title of the dialog
  final String title;

  /// The message to display
  final String message;

  /// The action to perform when the user taps the upgrade button
  final VoidCallback? onUpgrade;

  /// Creates a new premium feature dialog
  const PremiumFeatureDialog({
    super.key,
    required this.title,
    required this.message,
    this.onUpgrade,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: Text(
        title,
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Premium icon
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.star,
              size: 48,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(height: AppConstants.spacingMedium),
          
          // Message
          Text(
            message,
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
      actions: [
        // Cancel button
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('Cancel'),
        ),
        
        // Upgrade button
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            onUpgrade?.call();
            
            // TODO: Navigate to premium upgrade screen if onUpgrade is null
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Upgrade to Premium'),
        ),
      ],
    );
  }
}
