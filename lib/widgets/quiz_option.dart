import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';

/// A widget that displays a quiz option
class QuizOption extends StatelessWidget {
  /// The chart pattern for this option
  final ChartPattern pattern;
  
  /// Whether this option is selected
  final bool isSelected;
  
  /// Whether this option is correct
  final bool isCorrect;
  
  /// Whether to show the result
  final bool showResult;
  
  /// Callback when the option is tapped
  final VoidCallback? onTap;

  /// Creates a new quiz option
  const QuizOption({
    super.key,
    required this.pattern,
    this.isSelected = false,
    this.isCorrect = false,
    this.showResult = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: AppConstants.paddingSmall / 2,
      ),
      child: Material(
        color: _getBackgroundColor(context),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: InkWell(
          onTap: showResult ? null : onTap,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
            child: Row(
              children: [
                // Option icon
                Icon(
                  pattern.icon ?? Icons.show_chart,
                  color: pattern.color ?? Theme.of(context).colorScheme.primary,
                  size: AppConstants.iconSizeMedium,
                ),
                
                const SizedBox(width: AppConstants.spacingMedium),
                
                // Option text
                Expanded(
                  child: Text(
                    pattern.name,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
                
                // Result indicator
                if (showResult)
                  Icon(
                    isCorrect ? Icons.check_circle : Icons.cancel,
                    color: isCorrect ? Colors.green : Colors.red,
                    size: AppConstants.iconSizeMedium,
                  )
                else if (isSelected)
                  Icon(
                    Icons.radio_button_checked,
                    color: Theme.of(context).colorScheme.primary,
                    size: AppConstants.iconSizeMedium,
                  )
                else
                  Icon(
                    Icons.radio_button_unchecked,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    size: AppConstants.iconSizeMedium,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Gets the background color for the option
  Color _getBackgroundColor(BuildContext context) {
    if (!showResult) {
      return isSelected
          ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
          : Theme.of(context).colorScheme.surface;
    }
    
    if (isCorrect) {
      return Colors.green.withOpacity(0.1);
    }
    
    if (isSelected && !isCorrect) {
      return Colors.red.withOpacity(0.1);
    }
    
    return Theme.of(context).colorScheme.surface;
  }
}
