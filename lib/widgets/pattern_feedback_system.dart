import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';

/// Immediate feedback system for pattern learning
class PatternFeedbackSystem extends StatefulWidget {
  /// The pattern being learned
  final ChartPattern pattern;
  
  /// User's answer or identification
  final String? userAnswer;
  
  /// Correct answer
  final String correctAnswer;
  
  /// Whether the answer is correct
  final bool isCorrect;
  
  /// Additional explanation
  final String? explanation;
  
  /// Market context information
  final String? marketContext;
  
  /// Callback when user acknowledges feedback
  final VoidCallback? onAcknowledged;
  
  /// Whether to show detailed feedback
  final bool showDetailed;

  /// Creates a new pattern feedback system
  const PatternFeedbackSystem({
    super.key,
    required this.pattern,
    this.userAnswer,
    required this.correctAnswer,
    required this.isCorrect,
    this.explanation,
    this.marketContext,
    this.onAcknowledged,
    this.showDetailed = true,
  });

  @override
  State<PatternFeedbackSystem> createState() => _PatternFeedbackSystemState();
}

class _PatternFeedbackSystemState extends State<PatternFeedbackSystem>
    with TickerProviderStateMixin {
  /// Animation controller for feedback appearance
  late AnimationController _animationController;
  
  /// Scale animation for feedback card
  late Animation<double> _scaleAnimation;
  
  /// Opacity animation for feedback content
  late Animation<double> _opacityAnimation;
  
  /// Whether detailed explanation is expanded
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Gets feedback color based on correctness
  Color _getFeedbackColor() {
    return widget.isCorrect ? Colors.green : Colors.red;
  }

  /// Gets feedback icon based on correctness
  IconData _getFeedbackIcon() {
    return widget.isCorrect ? Icons.check_circle : Icons.cancel;
  }

  /// Gets feedback title based on correctness
  String _getFeedbackTitle() {
    if (widget.isCorrect) {
      return 'Excellent!';
    } else {
      return 'Not quite right';
    }
  }

  /// Gets encouraging message based on correctness
  String _getEncouragingMessage() {
    if (widget.isCorrect) {
      return 'You correctly identified the ${widget.pattern.name} pattern!';
    } else {
      return 'Let\'s learn from this mistake and improve your pattern recognition skills.';
    }
  }

  /// Gets detailed explanation for the pattern
  String _getDetailedExplanation() {
    if (widget.explanation != null) {
      return widget.explanation!;
    }
    
    // Generate default explanation based on pattern
    switch (widget.pattern.id) {
      case 'double_top':
        return 'A Double Top pattern consists of two peaks at approximately the same price level, separated by a valley. It signals a bearish reversal when the support level (valley) is broken.';
      case 'double_bottom':
        return 'A Double Bottom pattern consists of two troughs at approximately the same price level, separated by a peak. It signals a bullish reversal when the resistance level (peak) is broken.';
      case 'head_and_shoulders':
        return 'A Head and Shoulders pattern has three peaks: left shoulder, head (highest), and right shoulder. The neckline connects the troughs. A break below the neckline confirms the bearish reversal.';
      case 'ascending_triangle':
        return 'An Ascending Triangle has a horizontal resistance line and an ascending support line. It typically breaks upward, continuing the bullish trend.';
      default:
        return 'This pattern has specific characteristics that help identify potential price movements. Study the key elements and practice recognition.';
    }
  }

  /// Gets market context explanation
  String _getMarketContextExplanation() {
    if (widget.marketContext != null) {
      return widget.marketContext!;
    }
    
    // Generate default market context
    if (widget.pattern.type == PatternType.reversal) {
      return 'Reversal patterns like this typically occur at the end of trends and signal a potential change in direction. They\'re most reliable when they appear after a significant price move.';
    } else {
      return 'Continuation patterns like this usually occur during temporary pauses in trends and suggest the trend will resume in the same direction.';
    }
  }

  /// Gets learning tips based on the pattern and correctness
  List<String> _getLearningTips() {
    final tips = <String>[];
    
    if (!widget.isCorrect) {
      tips.add('Take time to study the pattern structure before making your identification');
      tips.add('Look for the key characteristics that define this pattern');
      tips.add('Practice with multiple examples to improve recognition');
    }
    
    // Pattern-specific tips
    switch (widget.pattern.id) {
      case 'double_top':
        tips.add('The two peaks should be roughly equal in height (within 3%)');
        tips.add('Volume often decreases on the second peak');
        break;
      case 'head_and_shoulders':
        tips.add('The head should be clearly higher than both shoulders');
        tips.add('The neckline doesn\'t have to be perfectly horizontal');
        break;
      case 'ascending_triangle':
        tips.add('Look for at least 2 touches of the resistance line');
        tips.add('The support line should show higher lows');
        break;
    }
    
    return tips;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final feedbackColor = _getFeedbackColor();
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                border: Border.all(
                  color: feedbackColor,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: feedbackColor.withAlpha(50),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with result
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    decoration: BoxDecoration(
                      color: feedbackColor.withAlpha(30),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(AppConstants.borderRadiusMedium),
                        topRight: Radius.circular(AppConstants.borderRadiusMedium),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _getFeedbackIcon(),
                          color: feedbackColor,
                          size: AppConstants.iconSizeLarge,
                        ),
                        const SizedBox(width: AppConstants.spacingMedium),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getFeedbackTitle(),
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: feedbackColor,
                                ),
                              ),
                              Text(
                                _getEncouragingMessage(),
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface.withAlpha(180),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Content
                  Padding(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Answer comparison (if user provided an answer)
                        if (widget.userAnswer != null) ...[
                          _buildAnswerComparison(theme),
                          const SizedBox(height: AppConstants.spacingLarge),
                        ],
                        
                        // Pattern explanation
                        if (widget.showDetailed) ...[
                          _buildExplanationSection(theme),
                          const SizedBox(height: AppConstants.spacingLarge),
                        ],
                        
                        // Learning tips
                        _buildLearningTips(theme),
                        
                        const SizedBox(height: AppConstants.spacingLarge),
                        
                        // Action buttons
                        Row(
                          children: [
                            if (!widget.isCorrect)
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () {
                                    // Show pattern study material
                                  },
                                  icon: const Icon(Icons.school),
                                  label: const Text('Study Pattern'),
                                ),
                              ),
                            
                            if (!widget.isCorrect)
                              const SizedBox(width: AppConstants.spacingMedium),
                            
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: widget.onAcknowledged,
                                icon: const Icon(Icons.arrow_forward),
                                label: Text(widget.isCorrect ? 'Continue' : 'Try Again'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: feedbackColor,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Builds answer comparison section
  Widget _buildAnswerComparison(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(50),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Answer Comparison',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.spacingMedium),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Answer:',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(150),
                      ),
                    ),
                    Text(
                      widget.userAnswer!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: widget.isCorrect ? Colors.green : Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Correct Answer:',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(150),
                      ),
                    ),
                    Text(
                      widget.correctAnswer,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds explanation section
  Widget _buildExplanationSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Row(
            children: [
              Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: AppConstants.spacingSmall),
              Text(
                'Pattern Explanation',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
        
        if (_isExpanded) ...[
          const SizedBox(height: AppConstants.spacingMedium),
          Text(
            _getDetailedExplanation(),
            style: theme.textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
          ),
          const SizedBox(height: AppConstants.spacingMedium),
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(30),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              border: Border.all(color: Colors.blue.withAlpha(100)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.info, color: Colors.blue),
                    const SizedBox(width: AppConstants.spacingSmall),
                    Text(
                      'Market Context',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.spacingSmall),
                Text(
                  _getMarketContextExplanation(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Builds learning tips section
  Widget _buildLearningTips(ThemeData theme) {
    final tips = _getLearningTips();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.lightbulb, color: Colors.amber),
            const SizedBox(width: AppConstants.spacingSmall),
            Text(
              'Learning Tips',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.amber.shade700,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.spacingMedium),
        ...tips.map((tip) => Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.spacingSmall),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 6),
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.amber.shade700,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
              Expanded(
                child: Text(
                  tip,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }
}
