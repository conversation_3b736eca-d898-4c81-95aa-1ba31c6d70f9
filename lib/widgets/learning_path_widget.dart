import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/comprehensive_trading_education.dart';

/// Widget for displaying a learning path with course progression
class LearningPathWidget extends StatelessWidget {
  /// The courses in the learning path
  final List<TradingEducationCourse> courses;
  
  /// Progress for each course (course ID -> progress 0-1)
  final Map<String, double> courseProgress;
  
  /// Completed courses
  final Set<String> completedCourses;
  
  /// Callback when a course is selected
  final ValueChanged<TradingEducationCourse>? onCourseSelected;
  
  /// Title of the learning path
  final String title;
  
  /// Description of the learning path
  final String description;

  /// Creates a new learning path widget
  const LearningPathWidget({
    super.key,
    required this.courses,
    this.courseProgress = const {},
    this.completedCourses = const {},
    this.onCourseSelected,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.route,
                  color: theme.colorScheme.primary,
                  size: AppConstants.iconSizeLarge,
                ),
                const SizedBox(width: AppConstants.spacingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.spacingSmall),
                      Text(
                        description,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                      ),
                    ],
                  ),
                ),
                // Overall progress
                _buildOverallProgress(theme),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingLarge),
            
            // Course progression
            ...courses.asMap().entries.map((entry) {
              final index = entry.key;
              final course = entry.value;
              final isLast = index == courses.length - 1;
              
              return _buildCourseStep(
                theme,
                course,
                index,
                isLast,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// Builds overall progress indicator
  Widget _buildOverallProgress(ThemeData theme) {
    final totalCourses = courses.length;
    final completedCount = completedCourses.length;
    final overallProgress = totalCourses > 0 ? completedCount / totalCourses : 0.0;
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withAlpha(30),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Column(
        children: [
          Text(
            '${(overallProgress * 100).round()}%',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Complete',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a course step in the learning path
  Widget _buildCourseStep(
    ThemeData theme,
    TradingEducationCourse course,
    int index,
    bool isLast,
  ) {
    final isCompleted = completedCourses.contains(course.id);
    final progress = courseProgress[course.id] ?? 0.0;
    final isUnlocked = _isCourseUnlocked(course, index);
    
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Step indicator
            Column(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isCompleted 
                        ? Colors.green
                        : (progress > 0 
                            ? theme.colorScheme.primary
                            : (isUnlocked 
                                ? theme.colorScheme.outline
                                : theme.colorScheme.outline.withAlpha(100))),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isCompleted 
                          ? Colors.green
                          : (isUnlocked 
                              ? theme.colorScheme.primary
                              : theme.colorScheme.outline),
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: isCompleted
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 20,
                          )
                        : (progress > 0
                            ? Icon(
                                Icons.play_arrow,
                                color: Colors.white,
                                size: 20,
                              )
                            : (isUnlocked
                                ? Text(
                                    '${index + 1}',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                : Icon(
                                    Icons.lock,
                                    color: theme.colorScheme.outline,
                                    size: 20,
                                  ))),
                  ),
                ),
                
                // Connecting line
                if (!isLast)
                  Container(
                    width: 2,
                    height: 60,
                    color: isCompleted 
                        ? Colors.green
                        : theme.colorScheme.outline.withAlpha(100),
                  ),
              ],
            ),
            
            const SizedBox(width: AppConstants.spacingMedium),
            
            // Course info
            Expanded(
              child: GestureDetector(
                onTap: isUnlocked ? () => onCourseSelected?.call(course) : null,
                child: Container(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    color: isUnlocked 
                        ? theme.colorScheme.surface
                        : theme.colorScheme.surface.withAlpha(100),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    border: Border.all(
                      color: isCompleted 
                          ? Colors.green
                          : (progress > 0 
                              ? theme.colorScheme.primary
                              : theme.colorScheme.outline.withAlpha(100)),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Course title and badges
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              course.title,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: isUnlocked 
                                    ? theme.colorScheme.onSurface
                                    : theme.colorScheme.onSurface.withAlpha(100),
                              ),
                            ),
                          ),
                          
                          // Premium badge
                          if (course.isPremium)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.amber,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'PRO',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                        ],
                      ),
                      
                      const SizedBox(height: AppConstants.spacingSmall),
                      
                      // Course description
                      Text(
                        course.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isUnlocked 
                              ? theme.colorScheme.onSurface.withAlpha(180)
                              : theme.colorScheme.onSurface.withAlpha(100),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: AppConstants.spacingMedium),
                      
                      // Course metadata
                      Row(
                        children: [
                          // Difficulty
                          _buildMetadataChip(
                            theme,
                            Icons.signal_cellular_alt,
                            _getDifficultyText(course.difficulty),
                            _getDifficultyColor(course.difficulty),
                            isUnlocked,
                          ),
                          
                          const SizedBox(width: AppConstants.spacingSmall),
                          
                          // Duration
                          _buildMetadataChip(
                            theme,
                            Icons.access_time,
                            '${course.estimatedHours}h',
                            theme.colorScheme.primary,
                            isUnlocked,
                          ),
                        ],
                      ),
                      
                      // Progress bar
                      if (progress > 0) ...[
                        const SizedBox(height: AppConstants.spacingMedium),
                        LinearProgressIndicator(
                          value: progress,
                          backgroundColor: theme.colorScheme.outline.withAlpha(50),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            isCompleted ? Colors.green : theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        
        if (!isLast) const SizedBox(height: AppConstants.spacingMedium),
      ],
    );
  }

  /// Builds a metadata chip
  Widget _buildMetadataChip(
    ThemeData theme,
    IconData icon,
    String label,
    Color color,
    bool isEnabled,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 6,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: (isEnabled ? color : theme.colorScheme.outline).withAlpha(30),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: isEnabled ? color : theme.colorScheme.outline,
          ),
          const SizedBox(width: 2),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isEnabled ? color : theme.colorScheme.outline,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// Checks if a course is unlocked
  bool _isCourseUnlocked(TradingEducationCourse course, int index) {
    // First course is always unlocked
    if (index == 0) return true;
    
    // Check if all prerequisite courses are completed
    for (int i = 0; i < index; i++) {
      if (!completedCourses.contains(courses[i].id)) {
        return false;
      }
    }
    
    return true;
  }

  /// Gets difficulty text
  String _getDifficultyText(CourseDifficulty difficulty) {
    switch (difficulty) {
      case CourseDifficulty.beginner:
        return 'Beginner';
      case CourseDifficulty.intermediate:
        return 'Intermediate';
      case CourseDifficulty.advanced:
        return 'Advanced';
      case CourseDifficulty.expert:
        return 'Expert';
    }
  }

  /// Gets difficulty color
  Color _getDifficultyColor(CourseDifficulty difficulty) {
    switch (difficulty) {
      case CourseDifficulty.beginner:
        return Colors.green;
      case CourseDifficulty.intermediate:
        return Colors.orange;
      case CourseDifficulty.advanced:
        return Colors.red;
      case CourseDifficulty.expert:
        return Colors.purple;
    }
  }
}
