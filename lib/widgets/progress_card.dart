import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';

/// A card that displays progress information
class ProgressCard extends StatelessWidget {
  /// The title of the card
  final String title;
  
  /// The progress value (0.0-1.0)
  final double progress;
  
  /// The icon to display
  final IconData icon;
  
  /// The color of the progress indicator
  final Color? color;
  
  /// The subtitle to display
  final String? subtitle;
  
  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a new progress card
  const ProgressCard({
    super.key,
    required this.title,
    required this.progress,
    required this.icon,
    this.color,
    this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final progressColor = color ?? Theme.of(context).colorScheme.primary;
    
    return Card(
      elevation: AppConstants.elevationSmall,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon and title
              Row(
                children: [
                  Icon(
                    icon,
                    color: progressColor,
                    size: AppConstants.iconSizeMedium,
                  ),
                  const SizedBox(width: AppConstants.spacingMedium),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.spacingLarge),
              
              // Progress indicator
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey.withOpacity(0.3),
                color: progressColor,
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              // Progress percentage
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Subtitle
                  if (subtitle != null)
                    Text(
                      subtitle!,
                      style: Theme.of(context).textTheme.bodySmall,
                    )
                  else
                    const SizedBox.shrink(),
                  
                  // Percentage
                  Text(
                    '${(progress * 100).toInt()}%',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: progressColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
