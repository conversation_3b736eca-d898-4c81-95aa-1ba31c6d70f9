import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/models/enhanced_pattern_annotation.dart';
import 'package:learn_chart_patterns/widgets/candlestick_chart.dart';
import 'package:learn_chart_patterns/themes/app_theme.dart';

/// Interactive guide for pattern identification with step-by-step assistance
class InteractivePatternGuide extends StatefulWidget {
  /// The pattern to guide identification for
  final ChartPattern pattern;
  
  /// Candlestick data
  final List<CandlestickData> candlesticks;
  
  /// Chart colors
  final ChartColors chartColors;
  
  /// Callback when identification is completed
  final VoidCallback? onCompleted;
  
  /// Callback when user makes an identification attempt
  final ValueChanged<IdentificationAttempt>? onAttempt;

  /// Creates a new interactive pattern guide
  const InteractivePatternGuide({
    super.key,
    required this.pattern,
    required this.candlesticks,
    required this.chartColors,
    this.onCompleted,
    this.onAttempt,
  });

  @override
  State<InteractivePatternGuide> createState() => _InteractivePatternGuideState();
}

class _InteractivePatternGuideState extends State<InteractivePatternGuide> {
  /// Current identification step
  int _currentStep = 0;
  
  /// Identification steps for the pattern
  late List<IdentificationStep> _identificationSteps;
  
  /// User's identification attempts
  final List<IdentificationAttempt> _attempts = [];
  
  /// Whether the guide is completed
  bool _isCompleted = false;
  
  /// Current hint level (0 = no hint, 1 = subtle, 2 = obvious)
  int _hintLevel = 0;
  
  /// Points identified by the user
  final List<Offset> _userPoints = [];

  @override
  void initState() {
    super.initState();
    _generateIdentificationSteps();
  }

  /// Generates identification steps based on the pattern
  void _generateIdentificationSteps() {
    switch (widget.pattern.id) {
      case 'double_top':
        _identificationSteps = _generateDoubleTopSteps();
        break;
      case 'double_bottom':
        _identificationSteps = _generateDoubleBottomSteps();
        break;
      case 'head_and_shoulders':
        _identificationSteps = _generateHeadAndShouldersSteps();
        break;
      case 'ascending_triangle':
        _identificationSteps = _generateAscendingTriangleSteps();
        break;
      default:
        _identificationSteps = _generateGenericSteps();
    }
  }

  /// Generates steps for Double Top identification
  List<IdentificationStep> _generateDoubleTopSteps() {
    return [
      IdentificationStep(
        id: 'find_first_peak',
        instruction: 'Tap on the first peak of the double top pattern',
        hint: 'Look for the highest point in the first half of the chart',
        obviousHint: 'The first peak is the highest point before the price declines significantly',
        expectedType: IdentificationPointType.peak,
        tolerance: 0.02,
      ),
      IdentificationStep(
        id: 'find_second_peak',
        instruction: 'Now tap on the second peak at approximately the same level',
        hint: 'Find another high point that\'s roughly the same height as the first peak',
        obviousHint: 'The second peak should be within 2-3% of the first peak\'s height',
        expectedType: IdentificationPointType.peak,
        tolerance: 0.02,
      ),
      IdentificationStep(
        id: 'find_support',
        instruction: 'Identify the support level between the two peaks',
        hint: 'Look for the lowest point between the two peaks',
        obviousHint: 'This is where the price found temporary support before rallying to the second peak',
        expectedType: IdentificationPointType.support,
        tolerance: 0.03,
      ),
    ];
  }

  /// Generates steps for Double Bottom identification
  List<IdentificationStep> _generateDoubleBottomSteps() {
    return [
      IdentificationStep(
        id: 'find_first_trough',
        instruction: 'Tap on the first trough of the double bottom pattern',
        hint: 'Look for the lowest point in the first part of the pattern',
        obviousHint: 'The first trough is where the price found initial support',
        expectedType: IdentificationPointType.trough,
        tolerance: 0.02,
      ),
      IdentificationStep(
        id: 'find_second_trough',
        instruction: 'Find the second trough at approximately the same level',
        hint: 'Look for another low point that\'s roughly the same depth as the first',
        obviousHint: 'The second trough should be within 2-3% of the first trough\'s depth',
        expectedType: IdentificationPointType.trough,
        tolerance: 0.02,
      ),
      IdentificationStep(
        id: 'find_resistance',
        instruction: 'Identify the resistance level between the two troughs',
        hint: 'Find the highest point between the two low points',
        obviousHint: 'This is where the price faced selling pressure before declining to the second trough',
        expectedType: IdentificationPointType.resistance,
        tolerance: 0.03,
      ),
    ];
  }

  /// Generates steps for Head and Shoulders identification
  List<IdentificationStep> _generateHeadAndShouldersSteps() {
    return [
      IdentificationStep(
        id: 'find_left_shoulder',
        instruction: 'Identify the left shoulder peak',
        hint: 'Look for the first significant peak in the pattern',
        obviousHint: 'The left shoulder is the first peak, lower than the head',
        expectedType: IdentificationPointType.peak,
        tolerance: 0.02,
      ),
      IdentificationStep(
        id: 'find_head',
        instruction: 'Find the head - the highest peak in the pattern',
        hint: 'Look for the tallest peak in the middle of the pattern',
        obviousHint: 'The head should be clearly higher than both shoulders',
        expectedType: IdentificationPointType.peak,
        tolerance: 0.02,
      ),
      IdentificationStep(
        id: 'find_right_shoulder',
        instruction: 'Identify the right shoulder peak',
        hint: 'Find the final peak, similar in height to the left shoulder',
        obviousHint: 'The right shoulder should be roughly the same height as the left shoulder',
        expectedType: IdentificationPointType.peak,
        tolerance: 0.02,
      ),
      IdentificationStep(
        id: 'draw_neckline',
        instruction: 'Draw the neckline connecting the two troughs',
        hint: 'Connect the low points between the shoulders and head',
        obviousHint: 'The neckline is the support level formed by connecting the valleys',
        expectedType: IdentificationPointType.line,
        tolerance: 0.03,
      ),
    ];
  }

  /// Generates steps for Ascending Triangle identification
  List<IdentificationStep> _generateAscendingTriangleSteps() {
    return [
      IdentificationStep(
        id: 'find_resistance',
        instruction: 'Draw the horizontal resistance line',
        hint: 'Connect the peaks that are at roughly the same level',
        obviousHint: 'The resistance line should be horizontal, connecting multiple highs',
        expectedType: IdentificationPointType.line,
        tolerance: 0.02,
      ),
      IdentificationStep(
        id: 'find_support',
        instruction: 'Draw the ascending support line',
        hint: 'Connect the rising low points to form an upward sloping line',
        obviousHint: 'The support line should slope upward, connecting higher lows',
        expectedType: IdentificationPointType.line,
        tolerance: 0.03,
      ),
    ];
  }

  /// Generates generic identification steps
  List<IdentificationStep> _generateGenericSteps() {
    return [
      IdentificationStep(
        id: 'identify_pattern',
        instruction: 'Identify the key elements of this pattern',
        hint: 'Look for the main structural components',
        obviousHint: 'Focus on the most prominent peaks and troughs',
        expectedType: IdentificationPointType.peak,
        tolerance: 0.03,
      ),
    ];
  }

  /// Handles user tap on the chart
  void _handleChartTap(Offset position) {
    if (_isCompleted) return;
    
    final currentStep = _identificationSteps[_currentStep];
    final attempt = IdentificationAttempt(
      stepId: currentStep.id,
      position: position,
      timestamp: DateTime.now(),
    );
    
    _attempts.add(attempt);
    widget.onAttempt?.call(attempt);
    
    // Check if the attempt is correct
    final isCorrect = _validateAttempt(attempt, currentStep);
    
    if (isCorrect) {
      _userPoints.add(position);
      _nextStep();
    } else {
      _showIncorrectFeedback(currentStep);
    }
  }

  /// Validates an identification attempt
  bool _validateAttempt(IdentificationAttempt attempt, IdentificationStep step) {
    // This is a simplified validation - in a real implementation,
    // you would check against the actual pattern coordinates
    return true; // For demo purposes, always return true
  }

  /// Shows feedback for incorrect attempts
  void _showIncorrectFeedback(IdentificationStep step) {
    setState(() {
      _hintLevel = (_hintLevel + 1).clamp(0, 2);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Not quite right. ${_getCurrentHint(step)}'),
        backgroundColor: Colors.orange,
        action: SnackBarAction(
          label: 'Try Again',
          onPressed: () {},
        ),
      ),
    );
  }

  /// Gets the current hint based on hint level
  String _getCurrentHint(IdentificationStep step) {
    switch (_hintLevel) {
      case 1:
        return step.hint;
      case 2:
        return step.obviousHint;
      default:
        return 'Try again!';
    }
  }

  /// Moves to the next identification step
  void _nextStep() {
    if (_currentStep < _identificationSteps.length - 1) {
      setState(() {
        _currentStep++;
        _hintLevel = 0; // Reset hint level for new step
      });
    } else {
      _completeGuide();
    }
  }

  /// Completes the identification guide
  void _completeGuide() {
    setState(() {
      _isCompleted = true;
    });
    
    widget.onCompleted?.call();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Excellent! You\'ve successfully identified the pattern!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Provides a hint for the current step
  void _showHint() {
    setState(() {
      _hintLevel = (_hintLevel + 1).clamp(0, 2);
    });
  }

  /// Resets the guide to start over
  void _resetGuide() {
    setState(() {
      _currentStep = 0;
      _hintLevel = 0;
      _isCompleted = false;
      _userPoints.clear();
      _attempts.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentStep = _isCompleted ? null : _identificationSteps[_currentStep];
    
    return Column(
      children: [
        // Chart with interactive overlay
        GestureDetector(
          onTapDown: (details) {
            if (!_isCompleted) {
              _handleChartTap(details.localPosition);
            }
          },
          child: Stack(
            children: [
              CandlestickChart(
                candlesticks: widget.candlesticks,
                chartColors: widget.chartColors,
                showVolume: false,
                height: AppConstants.defaultChartHeight,
              ),
              // Overlay for user points
              if (_userPoints.isNotEmpty)
                CustomPaint(
                  size: Size.infinite,
                  painter: IdentificationOverlayPainter(
                    points: _userPoints,
                    color: theme.colorScheme.primary,
                  ),
                ),
            ],
          ),
        ),
        
        const SizedBox(height: AppConstants.spacingLarge),
        
        // Instruction panel
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: _isCompleted 
                ? Colors.green.withAlpha(30)
                : theme.colorScheme.primary.withAlpha(30),
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            border: Border.all(
              color: _isCompleted 
                  ? Colors.green
                  : theme.colorScheme.primary,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _isCompleted ? Icons.check_circle : Icons.touch_app,
                    color: _isCompleted ? Colors.green : theme.colorScheme.primary,
                  ),
                  const SizedBox(width: AppConstants.spacingMedium),
                  Expanded(
                    child: Text(
                      _isCompleted 
                          ? 'Pattern Identification Complete!'
                          : 'Step ${_currentStep + 1} of ${_identificationSteps.length}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _isCompleted ? Colors.green : theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.spacingMedium),
              
              Text(
                _isCompleted 
                    ? 'Congratulations! You\'ve successfully identified all key elements of the ${widget.pattern.name} pattern.'
                    : currentStep!.instruction,
                style: theme.textTheme.bodyLarge?.copyWith(
                  height: 1.5,
                ),
              ),
              
              if (!_isCompleted && _hintLevel > 0) ...[
                const SizedBox(height: AppConstants.spacingMedium),
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingSmall),
                  decoration: BoxDecoration(
                    color: Colors.amber.withAlpha(30),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    border: Border.all(color: Colors.amber),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.lightbulb, color: Colors.amber),
                      const SizedBox(width: AppConstants.spacingSmall),
                      Expanded(
                        child: Text(
                          'Hint: ${_getCurrentHint(currentStep!)}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        
        const SizedBox(height: AppConstants.spacingLarge),
        
        // Control buttons
        Row(
          children: [
            if (!_isCompleted) ...[
              OutlinedButton.icon(
                onPressed: _showHint,
                icon: const Icon(Icons.lightbulb_outline),
                label: const Text('Hint'),
              ),
              const SizedBox(width: AppConstants.spacingMedium),
            ],
            
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isCompleted ? _resetGuide : null,
                icon: Icon(_isCompleted ? Icons.refresh : Icons.hourglass_empty),
                label: Text(_isCompleted ? 'Try Again' : 'Identifying...'),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppConstants.spacingMedium),
        
        // Progress indicator
        LinearProgressIndicator(
          value: _isCompleted ? 1.0 : (_currentStep + 1) / _identificationSteps.length,
          backgroundColor: theme.colorScheme.outline.withAlpha(50),
        ),
      ],
    );
  }
}

/// Represents a step in the pattern identification process
class IdentificationStep {
  /// Unique identifier for the step
  final String id;
  
  /// Instruction for the user
  final String instruction;
  
  /// Subtle hint
  final String hint;
  
  /// Obvious hint for struggling users
  final String obviousHint;
  
  /// Expected type of identification
  final IdentificationPointType expectedType;
  
  /// Tolerance for correct identification (as percentage)
  final double tolerance;

  /// Creates a new identification step
  const IdentificationStep({
    required this.id,
    required this.instruction,
    required this.hint,
    required this.obviousHint,
    required this.expectedType,
    required this.tolerance,
  });
}

/// Types of identification points
enum IdentificationPointType {
  peak,
  trough,
  support,
  resistance,
  line,
  breakout,
}

/// Represents a user's identification attempt
class IdentificationAttempt {
  /// Step ID this attempt is for
  final String stepId;
  
  /// Position where user tapped
  final Offset position;
  
  /// Timestamp of the attempt
  final DateTime timestamp;
  
  /// Whether the attempt was correct
  final bool? isCorrect;

  /// Creates a new identification attempt
  const IdentificationAttempt({
    required this.stepId,
    required this.position,
    required this.timestamp,
    this.isCorrect,
  });
}

/// Custom painter for identification overlay
class IdentificationOverlayPainter extends CustomPainter {
  /// Points to draw
  final List<Offset> points;
  
  /// Color for the points
  final Color color;

  /// Creates a new identification overlay painter
  const IdentificationOverlayPainter({
    required this.points,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    for (final point in points) {
      canvas.drawCircle(point, 8, paint);
      
      // Draw a white border
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawCircle(point, 8, borderPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
