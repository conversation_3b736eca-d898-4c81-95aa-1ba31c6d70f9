import 'package:flutter/material.dart';
import 'package:learn_chart_patterns/constants/app_constants.dart';
import 'package:learn_chart_patterns/models/candlestick_data.dart';
import 'package:learn_chart_patterns/models/chart_pattern.dart';
import 'package:learn_chart_patterns/widgets/candlestick_chart.dart';
import 'package:learn_chart_patterns/themes/app_theme.dart';

/// Widget that shows pattern formation over time with step-by-step visualization
class PatternFormationTimeline extends StatefulWidget {
  /// The pattern to demonstrate
  final ChartPattern pattern;
  
  /// Complete candlestick data
  final List<CandlestickData> candlesticks;
  
  /// Chart colors
  final ChartColors chartColors;
  
  /// Whether to auto-play the timeline
  final bool autoPlay;
  
  /// Duration for each step in auto-play mode
  final Duration stepDuration;
  
  /// Callback when timeline step changes
  final ValueChanged<int>? onStepChanged;

  /// Creates a new pattern formation timeline
  const PatternFormationTimeline({
    super.key,
    required this.pattern,
    required this.candlesticks,
    required this.chartColors,
    this.autoPlay = false,
    this.stepDuration = const Duration(seconds: 2),
    this.onStepChanged,
  });

  @override
  State<PatternFormationTimeline> createState() => _PatternFormationTimelineState();
}

class _PatternFormationTimelineState extends State<PatternFormationTimeline>
    with TickerProviderStateMixin {
  /// Current step in the timeline
  int _currentStep = 0;
  
  /// Total number of steps
  late int _totalSteps;
  
  /// Animation controller for auto-play
  AnimationController? _autoPlayController;
  
  /// Timeline steps with descriptions
  late List<TimelineStep> _timelineSteps;
  
  /// Whether auto-play is currently active
  bool _isAutoPlaying = false;

  @override
  void initState() {
    super.initState();
    _generateTimelineSteps();
    _totalSteps = _timelineSteps.length;
    
    if (widget.autoPlay) {
      _startAutoPlay();
    }
  }

  @override
  void dispose() {
    _autoPlayController?.dispose();
    super.dispose();
  }

  /// Generates timeline steps based on the pattern
  void _generateTimelineSteps() {
    final stepSize = (widget.candlesticks.length / 6).round();
    
    _timelineSteps = [
      TimelineStep(
        endIndex: stepSize,
        title: 'Initial Trend',
        description: _getInitialTrendDescription(),
        highlightColor: Colors.blue.withAlpha(100),
      ),
      TimelineStep(
        endIndex: stepSize * 2,
        title: 'Pattern Beginning',
        description: _getPatternBeginningDescription(),
        highlightColor: Colors.orange.withAlpha(100),
      ),
      TimelineStep(
        endIndex: stepSize * 3,
        title: 'Pattern Development',
        description: _getPatternDevelopmentDescription(),
        highlightColor: Colors.purple.withAlpha(100),
      ),
      TimelineStep(
        endIndex: stepSize * 4,
        title: 'Pattern Formation',
        description: _getPatternFormationDescription(),
        highlightColor: Colors.green.withAlpha(100),
      ),
      TimelineStep(
        endIndex: stepSize * 5,
        title: 'Pattern Completion',
        description: _getPatternCompletionDescription(),
        highlightColor: Colors.amber.withAlpha(100),
      ),
      TimelineStep(
        endIndex: widget.candlesticks.length,
        title: 'Breakout/Confirmation',
        description: _getBreakoutDescription(),
        highlightColor: Colors.red.withAlpha(100),
      ),
    ];
  }

  /// Gets description for initial trend phase
  String _getInitialTrendDescription() {
    switch (widget.pattern.id) {
      case 'head_and_shoulders':
        return 'Price is in an uptrend, making higher highs and higher lows. Buyers are in control.';
      case 'double_top':
        return 'Strong uptrend with increasing momentum. Price is approaching a significant resistance level.';
      case 'ascending_triangle':
        return 'Price is trending upward with occasional pullbacks. A resistance level is starting to form.';
      default:
        return 'Initial price movement establishing the trend before pattern formation begins.';
    }
  }

  /// Gets description for pattern beginning phase
  String _getPatternBeginningDescription() {
    switch (widget.pattern.id) {
      case 'head_and_shoulders':
        return 'First shoulder forms as price reaches a peak and pulls back. This establishes initial resistance.';
      case 'double_top':
        return 'First peak forms at a key resistance level. Price fails to break higher and starts to decline.';
      case 'ascending_triangle':
        return 'Price hits resistance and pulls back, but the pullback is shallow, showing underlying strength.';
      default:
        return 'The first key elements of the pattern begin to take shape.';
    }
  }

  /// Gets description for pattern development phase
  String _getPatternDevelopmentDescription() {
    switch (widget.pattern.id) {
      case 'head_and_shoulders':
        return 'Price rallies again, breaking above the first shoulder to form the head - the highest point.';
      case 'double_top':
        return 'Price finds support and begins to rally again, attempting to break the previous high.';
      case 'ascending_triangle':
        return 'Another test of resistance occurs, but again price is rejected. Support line is rising.';
      default:
        return 'The pattern continues to develop with key structural elements becoming clearer.';
    }
  }

  /// Gets description for pattern formation phase
  String _getPatternFormationDescription() {
    switch (widget.pattern.id) {
      case 'head_and_shoulders':
        return 'Price declines from the head and then rallies to form the right shoulder at a similar level to the left.';
      case 'double_top':
        return 'Second peak forms at approximately the same level as the first, creating the "double top" structure.';
      case 'ascending_triangle':
        return 'Multiple tests of resistance create a horizontal line while support keeps rising, forming the triangle.';
      default:
        return 'The pattern structure is now clearly defined with all key elements in place.';
    }
  }

  /// Gets description for pattern completion phase
  String _getPatternCompletionDescription() {
    switch (widget.pattern.id) {
      case 'head_and_shoulders':
        return 'Pattern is complete. The neckline (support between shoulders) is now the key level to watch.';
      case 'double_top':
        return 'Double top pattern is complete. The support level between peaks becomes critical.';
      case 'ascending_triangle':
        return 'Triangle pattern is fully formed. Price is squeezed between horizontal resistance and rising support.';
      default:
        return 'Pattern formation is complete. Watch for confirmation through breakout or breakdown.';
    }
  }

  /// Gets description for breakout phase
  String _getBreakoutDescription() {
    switch (widget.pattern.id) {
      case 'head_and_shoulders':
        return 'Price breaks below the neckline, confirming the bearish reversal. Target is head height below neckline.';
      case 'double_top':
        return 'Breakdown below support confirms the bearish reversal. Measured target equals pattern height.';
      case 'ascending_triangle':
        return 'Breakout above resistance confirms bullish continuation. Target equals triangle height above breakout.';
      default:
        return 'Pattern is confirmed with a decisive breakout. Price moves in the expected direction.';
    }
  }

  /// Starts auto-play animation
  void _startAutoPlay() {
    _autoPlayController = AnimationController(
      duration: widget.stepDuration * _totalSteps,
      vsync: this,
    );
    
    _autoPlayController!.addListener(() {
      final progress = _autoPlayController!.value;
      final newStep = (progress * _totalSteps).floor().clamp(0, _totalSteps - 1);
      
      if (newStep != _currentStep) {
        setState(() {
          _currentStep = newStep;
        });
        widget.onStepChanged?.call(_currentStep);
      }
    });
    
    _autoPlayController!.forward();
    setState(() {
      _isAutoPlaying = true;
    });
  }

  /// Stops auto-play animation
  void _stopAutoPlay() {
    _autoPlayController?.stop();
    setState(() {
      _isAutoPlaying = false;
    });
  }

  /// Goes to next step
  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      widget.onStepChanged?.call(_currentStep);
    }
  }

  /// Goes to previous step
  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      widget.onStepChanged?.call(_currentStep);
    }
  }

  /// Goes to specific step
  void _goToStep(int step) {
    if (step >= 0 && step < _totalSteps) {
      setState(() {
        _currentStep = step;
      });
      widget.onStepChanged?.call(_currentStep);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentTimelineStep = _timelineSteps[_currentStep];
    final visibleCandlesticks = widget.candlesticks.take(currentTimelineStep.endIndex).toList();
    
    return Column(
      children: [
        // Chart with current step data
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            border: Border.all(
              color: currentTimelineStep.highlightColor,
              width: 2,
            ),
          ),
          child: CandlestickChart(
            candlesticks: visibleCandlesticks,
            chartColors: widget.chartColors,
            showVolume: false,
            height: AppConstants.defaultChartHeight,
          ),
        ),
        
        const SizedBox(height: AppConstants.spacingLarge),
        
        // Step information
        Container(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: currentTimelineStep.highlightColor,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: AppConstants.paddingSmall / 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    ),
                    child: Text(
                      'Step ${_currentStep + 1} of $_totalSteps',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.spacingMedium),
                  Expanded(
                    child: Text(
                      currentTimelineStep.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.spacingMedium),
              Text(
                currentTimelineStep.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: AppConstants.spacingLarge),
        
        // Timeline controls
        Row(
          children: [
            // Previous button
            IconButton(
              onPressed: _currentStep > 0 ? _previousStep : null,
              icon: const Icon(Icons.skip_previous),
              tooltip: 'Previous Step',
            ),
            
            // Play/Pause button
            IconButton(
              onPressed: _isAutoPlaying ? _stopAutoPlay : _startAutoPlay,
              icon: Icon(_isAutoPlaying ? Icons.pause : Icons.play_arrow),
              tooltip: _isAutoPlaying ? 'Pause' : 'Auto Play',
            ),
            
            // Next button
            IconButton(
              onPressed: _currentStep < _totalSteps - 1 ? _nextStep : null,
              icon: const Icon(Icons.skip_next),
              tooltip: 'Next Step',
            ),
            
            const SizedBox(width: AppConstants.spacingMedium),
            
            // Progress indicator
            Expanded(
              child: Column(
                children: [
                  LinearProgressIndicator(
                    value: (_currentStep + 1) / _totalSteps,
                    backgroundColor: theme.colorScheme.outline.withAlpha(50),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      currentTimelineStep.highlightColor.withAlpha(200),
                    ),
                  ),
                  const SizedBox(height: AppConstants.spacingSmall),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: _timelineSteps.asMap().entries.map((entry) {
                      final index = entry.key;
                      final step = entry.value;
                      final isActive = index == _currentStep;
                      final isCompleted = index < _currentStep;
                      
                      return GestureDetector(
                        onTap: () => _goToStep(index),
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isActive
                                ? step.highlightColor
                                : isCompleted
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.outline.withAlpha(100),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// Represents a step in the pattern formation timeline
class TimelineStep {
  /// End index for candlesticks to show
  final int endIndex;
  
  /// Title of the step
  final String title;
  
  /// Description of what's happening
  final String description;
  
  /// Highlight color for this step
  final Color highlightColor;

  /// Creates a new timeline step
  const TimelineStep({
    required this.endIndex,
    required this.title,
    required this.description,
    required this.highlightColor,
  });
}
