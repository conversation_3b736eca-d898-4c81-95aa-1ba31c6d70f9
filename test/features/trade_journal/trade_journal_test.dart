import 'package:flutter_test/flutter_test.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_entry.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_tag.dart';
import 'package:learn_chart_patterns/features/trade_journal/models/trade_image.dart';
import 'package:flutter/material.dart';

void main() {
  group('TradeEntry', () {
    test('should create a trade entry with default values', () {
      final entry = TradeEntry.create(
        symbol: 'EUR/USD',
        entryPrice: 1.1234,
        positionSize: 1.0,
        direction: TradeDirection.long,
      );

      expect(entry.symbol, equals('EUR/USD'));
      expect(entry.entryPrice, equals(1.1234));
      expect(entry.positionSize, equals(1.0));
      expect(entry.direction, equals(TradeDirection.long));
      expect(entry.outcome, equals(TradeOutcome.open));
      expect(entry.tags, isEmpty);
      expect(entry.imagePaths, isEmpty);
    });

    test('should close a position correctly', () {
      final entry = TradeEntry.create(
        symbol: 'EUR/USD',
        entryPrice: 1.1234,
        positionSize: 1.0,
        direction: TradeDirection.long,
      );

      final closedEntry = entry.closePosition(
        exitPrice: 1.1334,
        exitDate: DateTime.now(),
      );

      expect(closedEntry.exitPrice, equals(1.1334));
      expect(closedEntry.exitDate, isNotNull);
      expect(closedEntry.outcome, equals(TradeOutcome.profit));
      expect(closedEntry.profitLoss, closeTo(0.01, 0.001));
      expect(closedEntry.profitLossPercentage, closeTo(0.89, 0.01));
    });
  });

  group('TradeTag', () {
    test('should create a trade tag', () {
      final tag = TradeTag.create(
        name: 'Bullish',
        color: Colors.green,
      );

      expect(tag.name, equals('Bullish'));
      expect(tag.color, equals(Colors.green));
      expect(tag.id, isNotEmpty);
    });

    test('should convert to and from map', () {
      final tag = TradeTag.create(
        name: 'Bullish',
        color: Colors.green,
      );

      final map = tag.toMap();
      final fromMap = TradeTag.fromMap(map);

      expect(fromMap.id, equals(tag.id));
      expect(fromMap.name, equals(tag.name));
      expect(fromMap.color.value, equals(tag.color.value));
    });
  });

  group('TradeImage', () {
    test('should create a trade image', () {
      final image = TradeImage.create(
        tradeId: '123',
        imagePath: '/path/to/image.jpg',
        description: 'Test image',
      );

      expect(image.tradeId, equals('123'));
      expect(image.imagePath, equals('/path/to/image.jpg'));
      expect(image.description, equals('Test image'));
      expect(image.id, isNotEmpty);
      expect(image.createdAt, isNotNull);
    });

    test('should convert to and from map', () {
      final image = TradeImage.create(
        tradeId: '123',
        imagePath: '/path/to/image.jpg',
        description: 'Test image',
      );

      final map = image.toMap();
      final fromMap = TradeImage.fromMap(map);

      expect(fromMap.id, equals(image.id));
      expect(fromMap.tradeId, equals(image.tradeId));
      expect(fromMap.imagePath, equals(image.imagePath));
      expect(fromMap.description, equals(image.description));
      expect(fromMap.createdAt.toIso8601String(), equals(image.createdAt.toIso8601String()));
    });
  });
}
